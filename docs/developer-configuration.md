# Developer Configuration Instructions

## Prerequisites

- Python 3.11 or higher
- Node.js 20 or higher
- npm 9 or higher
- Caddy 2.7 or higher
- MailHog 1.0 or higher
- PostgreSQL 16 or higher
- Go 1.23 or higher (required for MailHog)

## Setup Options

You have two options for setting up the development environment:

1. **Automated Setup (Recommended)**

   - Uses the provided setup scripts
   - Handles all configuration automatically
   - Ensures consistent setup across all developers

2. **Manual Setup**
   - Step-by-step instructions for manual configuration
   - Useful for understanding the setup process
   - Required if automated setup fails

## Automated Setup

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/cashflow.git
cd cashflow
```

### 2. Run the Setup Script

```bash
cd scripts
chmod +x setup-dev.sh
./setup-dev.sh
```

The script will:

- Check and install all prerequisites
- Set up PostgreSQL database
- Configure environment files
- Set up Python virtual environment
- Install frontend dependencies
- Configure SSL certificates
- Set up local DNS
- Configure passwordless Caddy execution
- Verify all services

### 3. Start the Development Environment

After the script completes successfully, you can start the application:

```bash
# Start MailHog
mailhog

# Start Caddy
cd caddy
sudo caddy run --config ./Caddyfile.dev

# Start the Backend
cd ../backend
source venv/bin/activate
python -m app.app

# Start the Frontend
cd ../frontend
npm start
```

## Manual Setup

### 1. Install Prerequisites

#### Python 3.11

```bash
sudo apt update
sudo apt install -y software-properties-common
sudo add-apt-repository -y ppa:deadsnakes/ppa
sudo apt update
sudo apt install -y python3.11 python3.11-venv python3.11-dev
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
```

#### Node.js 20

```bash
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs
```

#### Go 1.23 or higher

```bash
# Remove any existing Go installation
sudo rm -rf /usr/local/go

# Download and install Go
GO_VERSION=$(curl -s https://go.dev/VERSION?m=text | head -n1 | cut -d' ' -f1)
curl -LO "https://go.dev/dl/${GO_VERSION}.linux-amd64.tar.gz"
sudo tar -C /usr/local -xzf "${GO_VERSION}.linux-amd64.tar.gz"
rm "${GO_VERSION}.linux-amd64.tar.gz"

# Add Go to PATH
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
```

#### PostgreSQL 16

```bash
# Add PostgreSQL repository
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o /usr/share/keyrings/postgresql-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/postgresql-archive-keyring.gpg] http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
sudo apt update
sudo apt install -y postgresql-16
```

#### Caddy

```bash
sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
sudo apt update
sudo apt install caddy
```

#### MailHog

```bash
go install github.com/mailhog/MailHog@latest
```

### 2. Database Setup

#### Create PostgreSQL Superuser

```bash
sudo -u postgres psql -d postgres <<EOF
CREATE ROLE supergres WITH LOGIN PASSWORD 'your_supergres_password' SUPERUSER;
EOF
```

#### Configure PostgreSQL

```bash
# Update password encryption
sudo sed -i "s/#password_encryption = .*/password_encryption = 'scram-sha-256'/" /etc/postgresql/16/main/postgresql.conf

# Update pg_hba.conf
sudo bash -c "cat > /etc/postgresql/16/main/pg_hba.conf <<EOF
# Local connections
local   all   supergres   scram-sha-256
local   all   cashgres   scram-sha-256
local   all   postgres   reject
local   all   all        scram-sha-256
# TCP/IP connections
host    all   all        127.0.0.1/32   scram-sha-256
host    all   all        ::1/128        scram-sha-256
EOF"

# Restart PostgreSQL
sudo systemctl restart postgresql
```

#### Create Application Database and User

```bash
# Create database and user
sudo -u postgres psql -d postgres <<EOF
CREATE DATABASE cashflow;
CREATE USER cashgres WITH PASSWORD 'your_cashgres_password';
GRANT ALL PRIVILEGES ON DATABASE cashflow TO cashgres;
\c cashflow
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO cashgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO cashgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO cashgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO cashgres;
EOF
```

### 3. Environment Configuration

#### Backend Environment

```bash
cd backend
cp .env.example .env
```

Edit `.env` with:

```env
DATABASE_URL=postgresql://cashgres:your_cashgres_password@localhost:5432/cashflow
SECRET_KEY=your_development_secret_key
DEBUG=True
HOST=0.0.0.0
PORT=8000
BASE_DOMAIN=cashflow.app
ALLOWED_ORIGINS=https://cashflow.app,https://*.cashflow.app
USE_SSL=true
SSL_KEYFILE=../caddy/certs/cashflow.app+4-key.pem
SSL_CERTFILE=../caddy/certs/cashflow.app+4.pem
ENVIRONMENT=development
EMAIL_BACKEND=mailhog
MAILHOG_HOST=localhost
MAILHOG_PORT=1025
```

#### Frontend Environment

```bash
cd ../frontend
cp .env.example .env.development
```

Edit `.env.development` with:

```env
REACT_APP_PROTOCOL=https
REACT_APP_BASE_DOMAIN=cashflow.app
REACT_APP_PORT=3000
REACT_APP_API_PORT=8000
NODE_ENV=development
```

### 4. Python Virtual Environment Setup

```bash
cd ../backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 5. Frontend Dependencies

```bash
cd ../frontend
npm install
```

### 6. SSL Certificate Setup

```bash
# Install mkcert
sudo apt install -y libnss3-tools
curl -JLO "https://dl.filippo.io/mkcert/latest?for=linux/amd64"
chmod +x mkcert-v*-linux-amd64
sudo cp mkcert-v*-linux-amd64 /usr/local/bin/mkcert

# Generate certificates
mkdir -p ../caddy/certs
cd ../caddy/certs
mkcert -install
mkcert "*.cashflow.app" "cashflow.app" localhost 127.0.0.1 ::1

# Rename certificates
mv "_wildcard.cashflow.app+4.pem" "cashflow.app+4.pem"
mv "_wildcard.cashflow.app+4-key.pem" "cashflow.app+4-key.pem"
```

### 7. Local DNS Configuration

Add to `/etc/hosts`:

```
127.0.0.1 cashflow.app
127.0.0.1 test.cashflow.app
```

### 8. Passwordless Caddy Execution

```bash
echo "$USER ALL=(ALL) NOPASSWD: /usr/bin/caddy run --config ./Caddyfile.dev" | sudo tee /etc/sudoers.d/caddy
sudo chmod 0440 /etc/sudoers.d/caddy
```

```bash
echo "$USER ALL=(ALL) NOPASSWD: /usr/bin/pkill -f caddy" | sudo tee /etc/sudoers.d/caddy-pkill
sudo chmod 0440 /etc/sudoers.d/caddy-pkil
```

### 9. Start the Development Environment

```bash
# Start MailHog
mailhog

# Start Caddy
cd caddy
sudo caddy run --config ./Caddyfile.dev

# Start the Backend
cd ../backend
source venv/bin/activate
python -m app.app

# Start the Frontend
cd ../frontend
npm start
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**

   - Verify PostgreSQL is running: `sudo systemctl status postgresql`
   - Check database credentials in `.env`
   - Ensure the database and user exist: `psql -U cashgres -d cashflow -c "\dt"`
   - Verify database migrations: `cd backend && source venv/bin/activate && alembic upgrade head`

2. **Caddy fails to start**

   - Ensure SSL certificates exist in `caddy/certs`
   - Check Caddyfile.dev configuration
   - Verify sudo privileges

3. **Backend API not accessible**

   - Check backend is running on port 8000
   - Verify Caddyfile.dev reverse proxy configuration
   - Ensure virtual environment is activated
   - Check backend logs for errors

4. **Frontend not loading**
   - Ensure frontend development server is running
   - Check browser console for errors
   - Verify Node.js version is 20 or higher
   - Check frontend environment variables

### SSL Certificate Issues

If you encounter SSL certificate issues:

1. Regenerate certificates:
   ```bash
   mkcert -install
   mkcert "*.cashflow.app" "cashflow.app" localhost 127.0.0.1 ::1
   mv *.pem caddy/certs/
   ```
2. Restart Caddy:
   ```bash
   sudo caddy run --config caddy/Caddyfile.dev
   ```

## Additional Resources

- [Caddy Documentation](https://caddyserver.com/docs/)
- [MailHog Documentation](https://github.com/mailhog/MailHog)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
