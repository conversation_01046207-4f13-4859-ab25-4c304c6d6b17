# Cashflow Application

This is a full-stack application for managing cash flow and financial projections.

## Prerequisites

### Required Software

- Python 3.11 or higher
- Node.js 20 or higher
- PostgreSQL 13 or higher
- Git
- Caddy 2.7 or higher
- MailHog 1.0 or higher (for development)

### Development Tools

- Visual Studio Code (recommended)
- VS Code Extensions:
  - Python (Microsoft)
  - ESLint
  - Prettier
  - GitLens
  - Docker (optional)

## Initial Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd Cashflow
```

### 2. Database Setup

#### Linux

```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE cashflow;
CREATE USER cashgres WITH PASSWORD 'Traverse1-Defraud-Unloader';
GRANT ALL PRIVILEGES ON DATABASE cashflow TO cashgres;
\q
```

#### Windows

1. Download and install PostgreSQL from [https://www.postgresql.org/download/windows/](https://www.postgresql.org/download/windows/)
2. During installation:
   - Set password for postgres user
   - Keep default port (5432)
3. Open pgAdmin 4 (installed with PostgreSQL)
4. Create new database named 'cashflow'
5. Create new user 'cashgres' with password 'Traverse1-Defraud-Unloader'
6. Grant all privileges on database 'cashflow' to user 'cashgres'

### 3. Python Environment Setup

#### Linux

```bash
# Create and activate virtual environment
cd backend
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### Windows (PowerShell)

```powershell
# Create and activate virtual environment
cd backend
python -m venv venv
.\venv\Scripts\Activate.ps1

# Install dependencies
pip install -r requirements.txt
```

Note: The virtual environment is located in the `backend` directory since it's only needed for the Python backend. When working with the backend, make sure to:

1. Always `cd` into the `backend` directory first
2. Activate the virtual environment using `source venv/bin/activate` (Linux) or `.\venv\Scripts\Activate.ps1` (Windows)
3. The virtual environment will be automatically used by VS Code when debugging the backend (see the launch.json configuration)

### 4. Node.js Setup

#### Linux

```bash
# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Install frontend dependencies
cd frontend
npm install
```

#### Windows

1. Download and install Node.js from [https://nodejs.org/](https://nodejs.org/)
2. Open PowerShell and run:

```powershell
cd frontend
npm install
```

### 5. SSL Certificate Setup

#### Linux

```bash
# Install mkcert
sudo apt install mkcert

# Generate certificates with wildcard support
cd caddy/certs
mkcert -install
# Generate certificates for the main domain and all subdomains
mkcert cashflow.app "*.cashflow.app" localhost 127.0.0.1 ::1

# Verify the certificates were generated correctly
ls -l *.pem
```

#### Windows

1. Install mkcert using Chocolatey:

```powershell
choco install mkcert
```

2. Generate certificates with wildcard support:

```powershell
cd caddy/certs
mkcert -install
# Generate certificates for the main domain and all subdomains
mkcert cashflow.app "*.cashflow.app" localhost 127.0.0.1 ::1

# Verify the certificates were generated correctly
dir *.pem
```

#### Troubleshooting SSL Issues

1. If you see certificate errors in your browser:

   - Make sure mkcert is installed in your system's trust store: `mkcert -install`
   - Clear your browser's SSL cache and certificate store
   - For Chrome: Go to `chrome://net-internals/#hsts` and clear the HSTS cache
   - For Firefox: Go to `about:preferences#privacy` and clear SSL/TLS certificates

2. If subdomains aren't working:

   - Verify your hosts file has the wildcard entry: `127.0.0.1 *.cashflow.app`
   - Check that the certificates were generated with the wildcard domain: `*.cashflow.app`
   - Ensure your backend's `ALLOWED_ORIGINS` includes wildcard subdomains

3. If you need to regenerate certificates:
   ```bash
   # Remove old certificates
   rm caddy/certs/*.pem
   # Regenerate with wildcard support
   mkcert cashflow.app "*.cashflow.app" localhost 127.0.0.1 ::1
   ```

### 6. Caddy Setup (Development Proxy)

Caddy is used as a development proxy to handle subdomains and SSL termination. This setup allows you to access the application using custom subdomains (e.g., `test.cashflow.app`) during development.

#### Linux

```bash
# Install Caddy
sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
sudo apt update
sudo apt install caddy

# Configure hosts file for local development
echo "127.0.0.1 cashflow.app" | sudo tee -a /etc/hosts

# Start Caddy with the development configuration
cd /path/to/Cashflow
sudo caddy run --config caddy/Caddyfile.dev
```

#### Windows

1. Install Caddy using Chocolatey:

```powershell
choco install caddy
```

2. Add entry to your hosts file (`C:\Windows\System32\drivers\etc\hosts`):

```
127.0.0.1 cashflow.app
```

3. Start Caddy with the development configuration:

```powershell
cd C:\path\to\Cashflow
caddy run --config caddy/Caddyfile.dev
```

Note: You don't need to add individual subdomains to the hosts file. Caddy will handle all subdomains of cashflow.app automatically based on the `*.cashflow.app` pattern in the Caddyfile.

#### Production Deployment

For production deployment, use the production Caddyfile:

```bash
# Start Caddy with the production configuration
sudo caddy run --config caddy/Caddyfile.prod
```

The production configuration includes:

- Automatic HTTPS with Let's Encrypt
- Enhanced security headers
- HTTP/2 and HTTP/3 support
- Strict TLS configuration

#### Traffic Flow

The Caddy setup handles the following traffic flow:

1. Frontend Requests (e.g., `https://test.cashflow.app`):

   - Browser → Caddy (127.0.0.1:443) → React (127.0.0.1:3000)

2. API Requests (e.g., `https://test.cashflow.app/api/login`):

   - Browser → Caddy (127.0.0.1:443) → FastAPI (127.0.0.1:8000)

3. Other Domains:
   - Bypass Caddy entirely

#### Troubleshooting Caddy Issues

1. If Caddy fails to start:

   - Check if ports 80 and 443 are available
   - Verify SSL certificates are in the correct location
   - Check Caddy logs: `sudo journalctl -u caddy`

2. If subdomains aren't working:

   - Verify hosts file entry for cashflow.app
   - Check Caddy configuration in `caddy/Caddyfile.dev` or `caddy/Caddyfile.prod`
   - Ensure both frontend and backend servers are running

3. If you need to restart Caddy:

   ```bash
   # Development
   sudo caddy stop
   sudo caddy run --config caddy/Caddyfile.dev

   # Production
   sudo caddy stop
   sudo caddy run --config caddy/Caddyfile.prod
   ```

### 7. Environment Configuration

1. Copy the example environment files:

```bash
# Backend
cd backend
cp .env.example .env

# Frontend
cd frontend
cp .env.example .env.development
```

2. Update the environment files with your specific configurations.

## Running the Application

### Backend

#### Linux

```bash
cd backend
source venv/bin/activate
# Option 1: Using python -m (uses .env configuration including SSL)
python -m app.app

# Option 2: Using uvicorn directly (must specify SSL manually)
uvicorn app.app:app --reload --host 0.0.0.0 --port 8000 --ssl-keyfile ../caddy/certs/cashflow.app+4-key.pem --ssl-certfile ../caddy/certs/cashflow.app+4.pem
```

#### Windows (PowerShell)

```powershell
cd backend
..\venv\Scripts\Activate.ps1
# Option 1: Using python -m (uses .env configuration including SSL)
python -m app.app

# Option 2: Using uvicorn directly (must specify SSL manually)
uvicorn app.app:app --reload --host 0.0.0.0 --port 8000 --ssl-keyfile ../caddy/certs/cashflow.app+4-key.pem --ssl-certfile ../caddy/certs/cashflow.app+4.pem
```

Note: Option 1 (using `python -m app.app`) is recommended as it automatically uses the SSL configuration from your `.env` file. Option 2 requires manually specifying the SSL certificate paths.

### Frontend

#### Linux

```bash
cd frontend
npm start
```

#### Windows (PowerShell)

```powershell
cd frontend
npm start
```

## Debugging in VS Code

### Backend Debugging

1. Open VS Code in the project root
2. Create or update `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "module": "app.app",
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend"
      }
    }
  ]
}
```

3. Set breakpoints in your Python code
4. Press F5 or use the Run and Debug sidebar to start debugging

### Frontend Debugging

1. Install the "Debugger for Chrome" VS Code extension
2. Create or update `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "React: Chrome",
      "type": "chrome",
      "request": "launch",
      "url": "https://cashflow.app:3000",
      "webRoot": "${workspaceFolder}/frontend",
      "sourceMapPathOverrides": {
        "webpack:///src/*": "${webRoot}/src/*"
      }
    }
  ]
}
```

3. Set breakpoints in your JavaScript/TypeScript code
4. Press F5 or use the Run and Debug sidebar to start debugging

## Common Issues and Solutions

### SSL Certificate Issues

If you see SSL certificate errors:

1. Make sure mkcert is installed and certificates are generated
2. Verify certificates are in the correct location
3. Check that your browser trusts the certificates
4. On Windows, you might need to run:

```powershell
mkcert -install
```

### Database Connection Issues

If you can't connect to the database:

1. Verify PostgreSQL is running
2. Check database credentials in `.env`
3. On Windows, ensure PostgreSQL service is running:

```powershell
Get-Service postgresql*
Start-Service postgresql*
```

### Port Conflicts

If ports 3000 or 8000 are already in use:

1. Find the process using the port:
   - Linux: `sudo lsof -i :<port>`
   - Windows: `netstat -ano | findstr :<port>`
2. Kill the process or update the port in your environment files

## Development Workflow

1. Create a new branch for your feature:

```bash
git checkout -b feature/your-feature-name
```

2. Make your changes and commit them:

```bash
git add .
git commit -m "Description of your changes"
```

3. Push your changes:

```bash
git push origin feature/your-feature-name
```

4. Create a pull request on GitHub

## Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [VS Code Debugging Guide](https://code.visualstudio.com/docs/editor/debugging)

## Features

- Account management
- Fixed expenses tracking
- Miscellaneous expenses tracking
- Payroll expenses management
- Project management (current and anticipated)
- Purchase order tracking
- Invoice management
- Cashflow forecasting
- Snapshot saving and retrieval

## Architecture

The application consists of two main parts:

1. **Backend**: FastAPI application that provides API endpoints and handles data storage
2. **Frontend**: React/TypeScript application with Ant Design UI Framework that provides a modern user interface with type safety

## Project Structure

- `backend/`: FastAPI backend application
  - `app/`: Main application code
    - `auth/`: Authentication and authorization
    - `organizations/`: Organization management
    - `cash_flow/`: Core cash flow functionality
    - `common/`: Shared utilities and middleware
  - `alembic/`: Database migrations
  - `venv/`: Python virtual environment
  - `requirements.txt`: Backend dependencies
- `frontend/`: React/TypeScript frontend application
  - `src/components/`: Reusable UI components
  - `src/pages/`: Page components for each route
  - `src/services/`: API services for communicating with the backend
  - `src/utils/`: Utility functions and helpers
  - `src/types/`: TypeScript type definitions
  - `src/contexts/`: React context providers
- `caddy/`: Caddy server configuration
  - `Caddyfile.dev`: Development configuration
  - `Caddyfile.prod`: Production configuration
  - `certs/`: SSL certificates
- `docs/`: Project documentation

## Key Changes from Flask Version

1. **Pydantic Models**: Added Pydantic models for request validation
2. **Async Support**: All route handlers are now async functions
3. **Dependency Injection**: Used FastAPI's dependency injection for common operations
4. **Middleware**: Replaced Flask's before_request with FastAPI middleware
5. **Error Handling**: Used FastAPI's HTTPException for error handling
6. **Session Management**: Implemented session management with Starlette's SessionMiddleware
7. **Type Annotations**: Added type annotations throughout the codebase
8. **API Documentation**: Automatic API documentation with Swagger and ReDoc
9. **CORS Support**: Added CORS middleware for cross-origin requests
10. **Database Dependency**: Added a database dependency for better error handling

## Dependencies

- FastAPI: Modern, fast web framework for building APIs
- Pydantic: Data validation and settings management
- Uvicorn: ASGI server for running FastAPI applications
- Jinja2: Template engine for HTML rendering
- Starlette: ASGI framework used by FastAPI
- SQLAlchemy: SQL toolkit and ORM
- Matplotlib: Plotting library for Python
- Aiofiles: Async file operations
