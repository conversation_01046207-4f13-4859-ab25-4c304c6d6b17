# Production Deployment Guide

This guide outlines the steps to deploy the Cashflow application to a production server.

## Prerequisites

- Ubuntu 22.04 LTS or later
- Python 3.11 or higher
- Node.js 20 or higher
- PostgreSQL 16 or higher
- Caddy 2.7 or higher
- Git

## Server Setup

### 1. System Updates and Basic Setup

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required system packages
sudo apt install -y build-essential python3-dev python3-pip python3-venv \
    git curl wget ca-certificates software-properties-common
```

### 2. Install Node.js 20

```bash
# Install Node.js 20.x
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs
```

### 3. Install PostgreSQL 16

```bash
# Add PostgreSQL repository
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o /usr/share/keyrings/postgresql-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/postgresql-archive-keyring.gpg] http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
sudo apt update
sudo apt install -y postgresql-16
```

### 4. Install Caddy

```bash
# Install Caddy
sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
sudo apt update
sudo apt install caddy
```

### 5. Database Setup

```bash
# Create superuser
sudo -u postgres psql -d postgres <<EOF
CREATE ROLE supergres WITH LOGIN PASSWORD 'your_secure_supergres_password' SUPERUSER;
EOF

# Configure PostgreSQL
sudo sed -i "s/#password_encryption = .*/password_encryption = 'scram-sha-256'/" /etc/postgresql/16/main/postgresql.conf

# Update pg_hba.conf
sudo bash -c "cat > /etc/postgresql/16/main/pg_hba.conf <<EOF
# Local connections
local   all   supergres   scram-sha-256
local   all   cashgres   scram-sha-256
local   all   postgres   reject
local   all   all        scram-sha-256
# TCP/IP connections
host    all   all        127.0.0.1/32   scram-sha-256
host    all   all        ::1/128        scram-sha-256
EOF"

# Restart PostgreSQL
sudo systemctl restart postgresql

# Create application database and user
sudo -u postgres psql -d postgres <<EOF
CREATE DATABASE cashflow;
CREATE USER cashgres WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE cashflow TO cashgres;
\c cashflow
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO cashgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO cashgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO cashgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO cashgres;
EOF
```

## Application Deployment

### 1. Clone and Setup Application

```bash
# Create application directory
sudo mkdir -p /var/www/cashflow
sudo chown -R $USER:$USER /var/www/cashflow

# Clone repository
cd /var/www/cashflow
git clone <repository-url> .

# Create and activate Python virtual environment
cd backend
python3 -m venv venv
source venv/bin/activate

# Install backend dependencies
pip install -r requirements.txt
```

### 2. Frontend Build

```bash
# Install frontend dependencies
cd ../frontend
npm install

# Build frontend for production
npm run build
```

### 3. Environment Configuration

1. Create and configure backend environment:

```bash
cd ../backend
cp .env.example .env
```

Edit `.env` with production settings:

```env
# PostgreSQL database configuration
DATABASE_URL=postgresql://cashgres:your_secure_password@localhost:5432/cashflow

# FastAPI configuration
SECRET_KEY=your_secure_secret_key
DEBUG=False

# Server configuration
HOST=0.0.0.0
PORT=8000
BASE_DOMAIN=cashflow.app
ALLOWED_ORIGINS=https://cashflow.app,https://*.cashflow.app

# SSL Configuration
USE_SSL=true
SSL_KEYFILE=/etc/caddy/certs/cashflow.app.key
SSL_CERTFILE=/etc/caddy/certs/cashflow.app.crt

# Environment
ENVIRONMENT=production
EMAIL_BACKEND=sendgrid
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
```

2. Create and configure frontend environment:

```bash
cd ../frontend
cp .env.example .env.production
```

Edit `.env.production`:

```env
REACT_APP_PROTOCOL=https
REACT_APP_BASE_DOMAIN=cashflow.app
REACT_APP_PORT=
REACT_APP_API_PORT=
NODE_ENV=production
```

### 4. Caddy Configuration

1. Create production Caddyfile:

```bash
sudo cp /var/www/cashflow/caddy/Caddyfile.prod /etc/caddy/Caddyfile
```

2. Update Caddyfile with your domain:

```caddyfile
{
    # Global options
    admin off
    auto_https on
    servers {
        protocol {
            allow_h2c true
            experimental_http3
        }
    }
}

# Handle all subdomains of cashflow.app
*.cashflow.app {
    # Caddy will automatically:
    # 1. Obtain Let's Encrypt certificates for all subdomains
    # 2. Set up automatic HTTPS
    # 3. Handle certificate renewal
    tls {
        protocols tls1.2 tls1.3
        alpn h2 http/1.1
    }

    # Handle API requests
    handle /api/* {
        reverse_proxy localhost:8000 {
            header_up Host {upstream_hostport}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }

    # Handle all other requests (frontend)
    handle {
        root * /var/www/cashflow/frontend/build
        try_files {path} /index.html
    }

    # Production-specific security headers
    header {
        # Enable HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains"
        # Prevent clickjacking
        X-Frame-Options "SAMEORIGIN"
        # XSS protection
        X-XSS-Protection "1; mode=block"
        # Prevent MIME type sniffing
        X-Content-Type-Options "nosniff"
        # Referrer policy
        Referrer-Policy "strict-origin-when-cross-origin"
        # Content Security Policy
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"
    }
}

# Handle the main domain
cashflow.app {
    # Caddy will automatically:
    # 1. Obtain Let's Encrypt certificate for the main domain
    # 2. Set up automatic HTTPS
    # 3. Handle certificate renewal
    tls {
        protocols tls1.2 tls1.3
        alpn h2 http/1.1
    }

    # Handle API requests
    handle /api/* {
        reverse_proxy localhost:8000 {
            header_up Host {upstream_hostport}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }

    # Handle all other requests (frontend)
    handle {
        root * /var/www/cashflow/frontend/build
        try_files {path} /index.html
    }

    # Production-specific security headers
    header {
        # Enable HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains"
        # Prevent clickjacking
        X-Frame-Options "SAMEORIGIN"
        # XSS protection
        X-XSS-Protection "1; mode=block"
        # Prevent MIME type sniffing
        X-Content-Type-Options "nosniff"
        # Referrer policy
        Referrer-Policy "strict-origin-when-cross-origin"
        # Content Security Policy
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"
    }
}
```

3. DNS Configuration:

   - Configure DNS records:
     ```
     A     cashflow.app        → Your server's IP address
     CNAME *.cashflow.app      → cashflow.app
     ```
   - Wait for DNS propagation (can take up to 48 hours)

4. Let's Encrypt Requirements:

   - Port 80 must be accessible for HTTP-01 challenge
   - Port 443 must be accessible for HTTPS
   - Domain must be publicly accessible
   - Server must have a valid public IP address

### 5. Systemd Service Setup

1. Create backend service:

```bash
sudo nano /etc/systemd/system/cashflow-backend.service
```

Add the following content:

```ini
[Unit]
Description=Cashflow Backend Service
After=network.target postgresql.service

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/cashflow/backend
Environment="PATH=/var/www/cashflow/backend/venv/bin"
ExecStart=/var/www/cashflow/backend/venv/bin/python -m app.app
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

2. Enable and start the service:

```bash
sudo systemctl enable cashflow-backend
sudo systemctl start cashflow-backend
```

### 6. Start Caddy

```bash
# Test Caddy configuration
sudo caddy validate --config /etc/caddy/Caddyfile

# Start Caddy
sudo systemctl restart caddy
```

## Monitoring and Maintenance

### 1. View Logs

```bash
# Backend logs
sudo journalctl -u cashflow-backend -f

# Caddy logs
sudo journalctl -u caddy -f

# PostgreSQL logs
sudo journalctl -u postgresql -f
```

### 2. Backup Database

```bash
# Create backup
pg_dump -U cashgres cashflow > backup.sql

# Restore from backup
psql -U cashgres cashflow < backup.sql
```

### 3. Update Application

```bash
# Pull latest changes
cd /var/www/cashflow
git pull

# Update backend
cd backend
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl restart cashflow-backend

# Update frontend
cd ../frontend
npm install
npm run build
sudo systemctl restart caddy
```

## Security Considerations

1. **Firewall Configuration**:

   ```bash
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   sudo ufw enable
   ```

2. **Regular Updates**:

   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

3. **SSL Certificate Renewal**:

   - Caddy automatically handles SSL certificate renewal
   - Monitor Caddy logs for any certificate issues

4. **Database Security**:
   - Use strong passwords
   - Regularly backup the database
   - Keep PostgreSQL updated
   - Monitor database logs for suspicious activity

## Troubleshooting

1. **Application Not Starting**:

   - Check logs: `sudo journalctl -u cashflow-backend -f`
   - Verify environment variables
   - Check file permissions
   - Verify database connection

2. **SSL Issues**:

   - Check Caddy logs: `sudo journalctl -u caddy -f`
   - Verify domain DNS settings
   - Check firewall configuration
   - Verify Let's Encrypt certificate status

3. **Database Connection Issues**:
   - Verify PostgreSQL is running: `sudo systemctl status postgresql`
   - Check database credentials
   - Verify database user permissions
   - Check PostgreSQL logs

## Support

For support or issues:

1. Check the application logs
2. Review the [development configuration guide](developer-configuration.md)
3. Contact the development team
