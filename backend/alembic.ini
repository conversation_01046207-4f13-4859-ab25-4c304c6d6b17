[alembic]
script_location = alembic
prepend_sys_path = .
version_path_separator = os  # Use os.pathsep. Default configuration used for new projects.
sqlalchemy.url = postgresql://supergres:Defender-Shriek-Contrite-Bloomers-Botany-Primate3@localhost:5432/cashflow

[post_write_hooks]

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = DEBUG
handlers = console
qualname = 

[logger_sqlalchemy]
level = DEBUG
handlers = 
qualname = sqlalchemy.engine

[logger_alembic]
level = DEBUG
handlers = 
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S

