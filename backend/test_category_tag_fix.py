#!/usr/bin/env python3
"""
Simple test to verify that category_tag is being populated correctly
in purchase orders and invoices when they're included in project responses.
"""

import sys
import os
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_purchase_order_category_tag():
    """Test that purchase orders include category_tag in project responses."""
    try:
        from app.cash_flow.services.project_service import _create_project_response
        from app.cash_flow.models import ProjectTable, PurchaseOrderTable, TagTable
        from app.cash_flow.models import PurchaseOrderResponse, TagResponse

        # Create a mock tag for category
        category_tag = TagTable(id=1, name="Test Category", organization_id=1)

        # Create a mock purchase order with category_tag and all required fields
        purchase_order = PurchaseOrderTable(
            id=1,
            po_number="PO-001",
            project_id=1,
            organization_id=1,
            amount=1000.0,
            due_date=datetime(2024, 1, 1),
            issue_date=datetime(2024, 1, 1),
            lead_time=30,
            terms="Net 30",
            category_tag=category_tag,
            tags=[],
        )

        # Create a mock project with the purchase order
        project = ProjectTable(
            id=1,
            name="Test Project",
            organization_id=1,
            start_date=datetime(2024, 1, 1),
            duration=30,
            status="current",
            purchase_orders=[purchase_order],
            milestones=[],
            project_expenses=[],
            invoices=[],
            tags=[],
        )

        # Create the response
        response = _create_project_response(project)

        # Check that the purchase order has category_tag
        assert len(response.purchase_orders) == 1
        po_response = response.purchase_orders[0]

        print(f"Purchase Order ID: {po_response.id}")
        print(f"Purchase Order PO Number: {po_response.po_number}")
        print(f"Category Tag: {po_response.category_tag}")

        if po_response.category_tag:
            print(f"Category Tag Name: {po_response.category_tag.name}")
            print("✅ Purchase order category_tag is populated correctly!")
        else:
            print("❌ Purchase order category_tag is NOT populated!")
            return False

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False


def test_invoice_category_tag():
    """Test that invoices include category_tag in project responses."""
    try:
        from app.cash_flow.services.project_service import _create_project_response
        from app.cash_flow.models import ProjectTable, InvoiceTable, TagTable
        from app.cash_flow.models import InvoiceResponse, TagResponse

        # Create a mock tag for category
        category_tag = TagTable(id=1, name="Test Category", organization_id=1)

        # Create a mock invoice with category_tag and all required fields
        invoice = InvoiceTable(
            id=1,
            project_id=1,
            organization_id=1,
            amount=1000.0,
            due_date=datetime(2024, 1, 1),
            purchase_order_id=None,  # Optional field
            category_tag=category_tag,
            tags=[],
            po_number="INV-001",
        )

        # Create a mock project with the invoice
        project = ProjectTable(
            id=1,
            name="Test Project",
            organization_id=1,
            start_date=datetime(2024, 1, 1),
            duration=30,
            status="current",
            purchase_orders=[],
            milestones=[],
            project_expenses=[],
            invoices=[invoice],
            tags=[],
        )

        # Create the response
        response = _create_project_response(project)

        # Check that the invoice has category_tag
        assert len(response.invoices) == 1
        inv_response = response.invoices[0]

        print(f"Invoice ID: {inv_response.id}")
        print(f"Invoice PO Number: {inv_response.po_number}")
        print(f"Category Tag: {inv_response.category_tag}")

        if inv_response.category_tag:
            print(f"Category Tag Name: {inv_response.category_tag.name}")
            print("✅ Invoice category_tag is populated correctly!")
        else:
            print("❌ Invoice category_tag is NOT populated!")
            return False

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False


if __name__ == "__main__":
    print("Testing category_tag population in project responses...")
    print("=" * 60)

    success = True

    print("\n1. Testing Purchase Order category_tag:")
    if not test_purchase_order_category_tag():
        success = False

    print("\n2. Testing Invoice category_tag:")
    if not test_invoice_category_tag():
        success = False

    print("\n" + "=" * 60)
    if success:
        print("✅ All tests passed! Category_tag is being populated correctly.")
    else:
        print("❌ Some tests failed! Category_tag is not being populated correctly.")

    sys.exit(0 if success else 1)
