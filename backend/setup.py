from setuptools import setup, find_packages

setup(
    name="cashflow",
    version="0.1",
    packages=find_packages(where="backend"),
    package_dir={"": "backend"},
    install_requires=[
        "fastapi>=0.100.0",
        "uvicorn==0.15.0",
        "sqlalchemy>=2.0.14,<2.1.0",
        "pydantic>=2.0.0",
        "python-jose[cryptography]==3.3.0",
        "passlib[bcrypt]==1.7.4",
        "python-multipart==0.0.5",
        "python-dotenv>=0.21.0",
        "psycopg2-binary==2.9.1",
        "alembic==1.7.1",
        "PyJWT==2.10.1",
        "sendgrid==6.11.0",
        "itsdangerous==2.1.2",
        "sqlmodel>=0.0.24",
        "asyncpg==0.29.0",
        "pydantic-settings==2.2.1",
        "cachetools>=5.3.2",
    ],
)
