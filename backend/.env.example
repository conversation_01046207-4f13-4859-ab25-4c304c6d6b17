# PostgreSQL database configuration
DATABASE_URL=postgresql://cashgres:,94iZ3xWHS1tc^Qw@localhost:5432/cashflow

# FastAPI configuration
SECRET_KEY=your-secret-key-here
# Set to False in production
DEBUG=True
# Set to 0 in production
PYTHONASYNCIODEBUG=1

# Server configuration
HOST=0.0.0.0
PORT=8000
BASE_DOMAIN=cashflow.app

# Environment
ENVIRONMENT=development  # development, staging, or production

# Company configuration
COMPANY_NAME=cashflow

# Email Configuration
# Production (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>

# Development (MailHog)
MAILHOG_HOST=localhost
MAILHOG_PORT=1025

# Staging (Mailtrap)
MAILTRAP_HOST=sandbox.smtp.mailtrap.io
MAILTRAP_PORT=2525
MAILTRAP_USERNAME=your_mailtrap_username
MAILTRAP_PASSWORD=your_mailtrap_password
MAILTRAP_INBOX_ID=your_inbox_id

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-specific-password
DEFAULT_FROM_EMAIL=<EMAIL> 