from fastapi import <PERSON>AP<PERSON>
from fastapi.staticfiles import StaticFiles
from starlette.middleware.sessions import SessionMiddleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import locale
import uvicorn
import os
from dotenv import load_dotenv
from pathlib import Path
from .cash_flow.routes import (
    accounts_router,
    fixed_expenses_router,
    misc_expenses_router,
    payroll_expenses_router,
    projects_router,
    purchase_orders_router,
    invoices_router,
    snapshots_router,
    forecasts_router,
    milestones_router,
    project_expenses_router,
    tags_router,
)
from app.auth.routes import router as auth_router
from app.organizations.routes import router as org_router
from app.core.config import settings
from app.common.cache import CacheManager
from app.common.middleware import (
    RequestContextMiddleware,
    DatabaseSessionMiddleware,
    AuthenticationMiddleware,
    OrganizationValidationMiddleware,
    RequestLoggingMiddleware,
    CORSValidationMiddleware,
    FrontendRoutingMiddleware,
    ExceptionLoggingMiddleware,
)
from contextlib import asynccontextmanager
import logging
import sys

# Load environment variables
load_dotenv()

# Set locale for currency formatting
locale.setlocale(locale.LC_ALL, "en_US.UTF-8")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stdout,
)
logger = logging.getLogger("cashflow")

# Cache configuration
MAX_CACHE_SIZE = 1000  # Maximum number of organizations to cache
CACHE_TTL = 3600  # 1 hour in seconds

# Initialize caches
organization_cache = CacheManager.create_cache(
    name="organizations",
    capacity=MAX_CACHE_SIZE,
    ttl_seconds=CACHE_TTL,
    track_stats=True,  # Enable statistics tracking
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting up Cashflow API")
    yield
    # Shutdown
    logger.info("Shutting down Cashflow API")
    CacheManager.clear_all()


app = FastAPI(
    title="Cashflow API",
    lifespan=lifespan,
    # Enable FastAPI's built-in exception handlers
    debug=True if os.getenv("ENVIRONMENT") == "development" else False,
)


# Add health check endpoint
@app.get("/api/health", include_in_schema=False)
async def health_check():
    return {"status": "healthy"}


# Add middleware in the correct order (LIFO - Last In, First Out)

# 1. Frontend routing middleware
app.add_middleware(FrontendRoutingMiddleware)

# 2. CORS validation middleware
app.add_middleware(
    CORSValidationMiddleware,
    base_domain=settings.BASE_DOMAIN,
)

# 3. Request logging middleware
app.add_middleware(RequestLoggingMiddleware)

# 4. Organization validation middleware
app.add_middleware(OrganizationValidationMiddleware)

# 5. Authentication middleware
app.add_middleware(AuthenticationMiddleware)

# 6. Database session middleware
app.add_middleware(DatabaseSessionMiddleware)

# 7. Request context middleware (must be last since it's first in execution)
app.add_middleware(RequestContextMiddleware)

# 8. Session middleware (built-in)
app.add_middleware(
    SessionMiddleware,
    secret_key=settings.SECRET_KEY,
    session_cookie="session",
    max_age=3600,
)

# 9. GZip middleware (built-in)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 10. CORS middleware (built-in)
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add exception logging middleware last (will be executed first)
app.add_middleware(ExceptionLoggingMiddleware)

# Mount routers after all middleware and handlers are defined
app.include_router(auth_router, prefix="/api/auth", tags=["auth"])
app.include_router(org_router, prefix="/api", tags=["organizations"])
app.include_router(accounts_router, prefix="/api", tags=["cash-flow"])
app.include_router(fixed_expenses_router, prefix="/api", tags=["cash-flow"])
app.include_router(misc_expenses_router, prefix="/api", tags=["cash-flow"])
app.include_router(payroll_expenses_router, prefix="/api", tags=["cash-flow"])
app.include_router(projects_router, prefix="/api", tags=["cash-flow"])
app.include_router(purchase_orders_router, prefix="/api", tags=["cash-flow"])
app.include_router(invoices_router, prefix="/api", tags=["cash-flow"])
app.include_router(snapshots_router, prefix="/api", tags=["cash-flow"])
app.include_router(forecasts_router, prefix="/api", tags=["cash-flow"])
app.include_router(milestones_router, prefix="/api", tags=["cash-flow"])
app.include_router(project_expenses_router, prefix="/api", tags=["cash-flow"])
app.include_router(tags_router, prefix="/api", tags=["tags"])

if os.getenv("ENVIRONMENT") == "production":
    frontend_dist = Path("frontend/build")
    if frontend_dist.exists():
        app.mount("/frontend", StaticFiles(directory="frontend/build"), name="frontend")


@app.get("/")
async def root():
    return {"message": "Welcome to Cashflow API"}


if __name__ == "__main__":
    # Prepare uvicorn arguments
    uvicorn_args = {
        "app": "app.app:app",  # Use import string format
        "host": os.getenv("HOST", "0.0.0.0"),
        "port": int(os.getenv("PORT", "8000")),
        "reload": os.getenv("DEBUG", "false").lower() == "true",
    }

    # Run the server
    uvicorn.run(**uvicorn_args)
