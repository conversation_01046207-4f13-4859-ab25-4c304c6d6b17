from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # Database settings
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/cashflow"
    )

    # FastAPI settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Cashflow"

    # Company settings
    COMPANY_NAME: str = os.getenv("COMPANY_NAME", "PrevisioIQ")

    # Server settings
    BASE_DOMAIN: str = os.getenv("BASE_DOMAIN")  # Base domain for the application

    # JWT settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Email settings
    SENDGRID_API_KEY: Optional[str] = os.getenv("SENDGRID_API_KEY")
    SENDGRID_FROM_EMAIL: str = os.getenv("SENDGRID_FROM_EMAIL")

    # Environment
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")

    # Verification settings
    VERIFICATION_TOKEN_EXPIRE_HOURS: int = int(
        os.getenv("VERIFICATION_TOKEN_EXPIRE_HOURS", "72")
    )

    class Config:
        case_sensitive = True


settings = Settings()
