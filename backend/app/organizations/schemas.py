from typing import Optional
from pydantic import BaseModel
from datetime import datetime


class OrganizationBase(BaseModel):
    name: str
    subdomain: str
    is_active: bool = True


class OrganizationCreate(OrganizationBase):
    pass


class OrganizationUpdate(BaseModel):
    name: Optional[str] = None
    subdomain: Optional[str] = None
    is_active: Optional[bool] = None


class OrganizationResponse(OrganizationBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OrganizationUserResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str

    class Config:
        from_attributes = True


class InvitationBase(BaseModel):
    email: str
    organization_id: int


class InvitationCreate(InvitationBase):
    pass


class InvitationResponse(InvitationBase):
    id: int
    token: str
    status: str
    invited_by_id: int
    created_at: datetime
    expires_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
