import re
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.organizations.models import Organization, Invitation
from app.auth.models import User, OrganizationUser, CurrentOrganizationUser
from app.common.email import send_email
import secrets
from datetime import datetime, timedelta
from app.common.email_templates import get_invitation_email_template
from app.core.config import settings
from app.cash_flow.services import AccountService


def is_valid_subdomain(subdomain: str) -> bool:
    """Validate subdomain format."""
    if not subdomain:
        return False
    # Subdomain should only contain lowercase letters, numbers, and hyphens
    # Must start with a letter and be between 3-63 characters
    pattern = r"^[a-z][a-z0-9-]{1,61}[a-z0-9]$"
    return bool(re.match(pattern, subdomain))


async def is_subdomain_available(db: AsyncSession, subdomain: str) -> bool:
    """Check if a subdomain is available."""
    result = await db.execute(
        select(Organization).where(Organization.subdomain == subdomain)
    )
    return result.scalar_one_or_none() is None


async def create_organization(
    db: AsyncSession,
    name: str,
    subdomain: str,
    creator_user: User,
    creator_role: str = "admin",
) -> Optional[Organization]:
    """Create a new organization."""
    if not is_valid_subdomain(subdomain):
        return None
    if not await is_subdomain_available(db, subdomain):
        return None

    organization = Organization(name=name, subdomain=subdomain)
    db.add(organization)
    await db.commit()
    await db.refresh(organization)

    # Associate creator with organization
    org_user = OrganizationUser(
        user_id=creator_user.id,
        organization_id=organization.id,
        role=creator_role,
    )
    db.add(org_user)
    await db.commit()

    # Set as current organization
    current_org = CurrentOrganizationUser(
        user_id=creator_user.id, organization_id=organization.id
    )
    db.add(current_org)
    await db.commit()

    # Create default accounts
    account_service = AccountService(db)
    await account_service.create_default_accounts(organization.id)

    return organization


async def get_organization_by_subdomain(
    db: AsyncSession, subdomain: str
) -> Optional[Organization]:
    """Get organization by subdomain."""
    result = await db.execute(
        select(Organization).where(Organization.subdomain == subdomain)
    )
    return result.scalar_one_or_none()


async def get_organization_by_id(
    db: AsyncSession, org_id: int
) -> Optional[Organization]:
    """Get organization by ID."""
    result = await db.execute(select(Organization).where(Organization.id == org_id))
    return result.scalar_one_or_none()


async def update_organization(
    db: AsyncSession,
    org_id: int,
    name: Optional[str] = None,
    subdomain: Optional[str] = None,
    is_active: Optional[bool] = None,
) -> Optional[Organization]:
    """Update an organization's details."""
    organization = await get_organization_by_id(db, org_id)
    if not organization:
        return None

    if subdomain is not None:
        if not is_valid_subdomain(subdomain):
            return None
        if subdomain != organization.subdomain and not await is_subdomain_available(
            db, subdomain
        ):
            return None
        organization.subdomain = subdomain

    if name is not None:
        organization.name = name

    if is_active is not None:
        organization.is_active = is_active

    await db.commit()
    await db.refresh(organization)
    return organization


async def get_user_organizations(db: AsyncSession, user_id: int) -> List[Organization]:
    """Get all organizations a user belongs to."""
    result = await db.execute(
        select(Organization)
        .join(OrganizationUser)
        .where(OrganizationUser.user_id == user_id)
    )
    return result.scalars().all()


async def add_user_to_organization(
    db: AsyncSession, user_id: int, organization_id: int, role: str = "member"
) -> bool:
    """Add a user to an organization."""
    # Check if user is already in organization
    result = await db.execute(
        select(OrganizationUser).where(
            OrganizationUser.user_id == user_id,
            OrganizationUser.organization_id == organization_id,
        )
    )
    existing = result.scalar_one_or_none()

    if existing:
        return False

    org_user = OrganizationUser(
        user_id=user_id,
        organization_id=organization_id,
        role=role,
    )
    db.add(org_user)
    await db.commit()
    return True


async def remove_user_from_organization(
    db: AsyncSession, user_id: int, organization_id: int
) -> bool:
    """Remove a user from an organization."""
    result = await db.execute(
        select(OrganizationUser).where(
            OrganizationUser.user_id == user_id,
            OrganizationUser.organization_id == organization_id,
        )
    )
    org_user = result.scalar_one_or_none()

    if not org_user:
        return False

    await db.delete(org_user)
    await db.commit()
    return True


async def get_organization_users(db: AsyncSession, organization_id: int) -> List[User]:
    """Get all users in an organization."""
    result = await db.execute(
        select(User)
        .join(OrganizationUser)
        .where(OrganizationUser.organization_id == organization_id)
    )
    return result.scalars().all()


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """Get a user by their email address."""
    query = select(User).where(User.email == email)
    result = await db.execute(query)
    return result.scalar_one_or_none()


def generate_invite_token() -> str:
    """Generate a secure random token for invitations."""
    return secrets.token_urlsafe(32)


async def create_invitation(
    db: AsyncSession,
    email: str,
    organization_id: int,
    invited_by_id: int,
    expires_in_days: int = 7,
) -> Optional[Invitation]:
    """Create a new invitation."""
    # Check if there's already a pending invitation for this email
    result = await db.execute(
        select(Invitation).where(
            Invitation.email == email,
            Invitation.organization_id == organization_id,
            Invitation.status == "pending",
            Invitation.expires_at > datetime.utcnow(),
        )
    )
    if result.scalar_one_or_none():
        return None

    # Create new invitation
    invitation = Invitation(
        email=email,
        token=generate_invite_token(),
        organization_id=organization_id,
        invited_by_id=invited_by_id,
        expires_at=datetime.utcnow() + timedelta(days=expires_in_days),
    )
    db.add(invitation)
    await db.commit()
    # Load the organization relationship
    await db.refresh(invitation, ["organization", "invited_by"])

    return invitation


async def get_invitation_by_token(db: AsyncSession, token: str) -> Optional[Invitation]:
    """Get invitation by token."""
    result = await db.execute(
        select(Invitation).where(
            Invitation.token == token,
            Invitation.status == "pending",
            Invitation.expires_at > datetime.utcnow(),
        )
    )
    return result.scalar_one_or_none()


async def accept_invitation(db: AsyncSession, token: str, user: User) -> bool:
    """Accept an invitation and add user to organization."""
    invitation = await get_invitation_by_token(db, token)
    if not invitation:
        return False

    # Add user to organization
    success = await add_user_to_organization(db, user.id, invitation.organization_id)
    if not success:
        return False

    # Update invitation status
    invitation.status = "accepted"
    invitation.updated_at = datetime.utcnow()
    await db.commit()
    return True


async def decline_invitation(db: AsyncSession, token: str) -> bool:
    """Decline an invitation."""
    invitation = await get_invitation_by_token(db, token)
    if not invitation:
        return False

    invitation.status = "declined"
    invitation.updated_at = datetime.utcnow()
    await db.commit()
    return True


async def send_invitation_email(invitation: Invitation) -> bool:
    """Send invitation email to user."""
    try:
        # Get organization details
        organization = invitation.organization
        if not organization:
            return False

        # Create invitation URL
        invite_url = f"https://{organization.subdomain}.{settings.BASE_DOMAIN}/accept-invite/{invitation.token}"

        # Get email template
        email_template = get_invitation_email_template(
            inviter_name=invitation.invited_by.first_name,
            organization_name=organization.name,
            invite_url=invite_url,
        )
        # Send email
        await send_email(
            to_email=invitation.email,
            subject=email_template["subject"],
            html_content=email_template["html_content"],
            plain_text_content=email_template["plain_text_content"],
        )
        return True
    except Exception as e:
        print(f"Error sending invitation email: {e}")
        return False


async def get_current_organization(
    db: AsyncSession, user_id: int
) -> Optional[Organization]:
    """Get the current organization for a user."""
    # First try to get the current organization
    result = await db.execute(
        select(Organization)
        .join(CurrentOrganizationUser)
        .where(CurrentOrganizationUser.user_id == user_id)
    )
    current_org = result.scalar_one_or_none()

    if current_org:
        return current_org

    # If no current organization is set, try to get the first organization the user belongs to
    result = await db.execute(
        select(Organization)
        .join(OrganizationUser)
        .where(OrganizationUser.user_id == user_id)
        .limit(1)
    )
    first_org = result.scalar_one_or_none()

    if first_org:
        # Set this as the current organization
        await set_current_organization(db, user_id, first_org.id)
        return first_org

    return None


async def set_current_organization(
    db: AsyncSession, user_id: int, organization_id: int
) -> Organization:
    """Set the current organization for a user."""
    # First, remove any existing current organization
    await db.execute(
        select(CurrentOrganizationUser).where(
            CurrentOrganizationUser.user_id == user_id
        )
    ).delete()

    # Create new current organization
    current_org = CurrentOrganizationUser(
        user_id=user_id, organization_id=organization_id
    )
    db.add(current_org)
    await db.commit()
    await db.refresh(current_org)

    # Get the organization with all relationships loaded
    result = await db.execute(
        select(Organization).where(Organization.id == organization_id)
    )
    organization = result.scalar_one_or_none()

    if not organization:
        raise ValueError(f"Organization with id {organization_id} not found")

    return organization


async def get_organization_invitations(
    db: AsyncSession, organization_id: int
) -> List[Invitation]:
    """Get all pending invitations for an organization."""
    result = await db.execute(
        select(Invitation).where(
            Invitation.organization_id == organization_id,
            Invitation.status == "pending",
        )
    )
    return result.scalars().all()


async def invite_user(
    db: AsyncSession, organization_id: int, email: str, invited_by_id: int
) -> Optional[Invitation]:
    """Create and send an invitation to a user."""
    # Check if invitation already exists
    result = await db.execute(
        select(Invitation).where(
            Invitation.email == email,
            Invitation.organization_id == organization_id,
            Invitation.status == "pending",
        )
    )
    existing_invitation = result.scalar_one_or_none()

    if existing_invitation:
        return None

    # Create new invitation
    invitation = Invitation(
        email=email,
        token=secrets.token_urlsafe(32),
        organization_id=organization_id,
        invited_by_id=invited_by_id,
        expires_at=datetime.utcnow() + timedelta(days=7),
    )

    db.add(invitation)
    await db.commit()
    await db.refresh(invitation)

    # Send invitation email
    html_content, plain_text_content = get_invitation_email_template(invitation)
    await send_email(
        to_email=email,
        subject="You've been invited to join an organization",
        html_content=html_content,
        plain_text_content=plain_text_content,
    )

    return invitation


async def delete_organization(db: AsyncSession, organization_id: int) -> bool:
    """Delete an organization and all related records."""
    organization = await get_organization_by_id(db, organization_id)
    if not organization:
        return False

    # Delete related records
    await db.execute(
        select(OrganizationUser).where(
            OrganizationUser.organization_id == organization_id
        )
    ).delete()

    await db.execute(
        select(CurrentOrganizationUser).where(
            CurrentOrganizationUser.organization_id == organization_id
        )
    ).delete()

    await db.execute(
        select(Invitation).where(Invitation.organization_id == organization_id)
    ).delete()

    await db.delete(organization)
    await db.commit()
    return True


async def remove_user(db: AsyncSession, organization_id: int, user_id: int) -> bool:
    """Remove a user from an organization."""
    # Remove from organization users
    result = await db.execute(
        select(OrganizationUser).where(
            OrganizationUser.organization_id == organization_id,
            OrganizationUser.user_id == user_id,
        )
    )
    org_user = result.scalar_one_or_none()

    if not org_user:
        return False

    await db.delete(org_user)

    # Remove from current organization if set
    result = await db.execute(
        select(CurrentOrganizationUser).where(
            CurrentOrganizationUser.organization_id == organization_id,
            CurrentOrganizationUser.user_id == user_id,
        )
    )
    current_org = result.scalar_one_or_none()

    if current_org:
        await db.delete(current_org)

    await db.commit()
    return True
