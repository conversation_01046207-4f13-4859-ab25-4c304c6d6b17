from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship
from sqlalchemy import BigInteger, Identity

if TYPE_CHECKING:
    from app.auth.models import User, OrganizationUser, CurrentOrganizationUser


class OrganizationBase(SQLModel):
    name: str
    subdomain: str = Field(unique=True, index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = Field(default=True)


class Organization(OrganizationBase, table=True):
    __tablename__ = "organizations"
    id: Optional[int] = Field(
        default=None,
        primary_key=True,
        sa_column_kwargs={"server_default": Identity()},
        sa_type=BigInteger,
    )

    # Relationships with forward references
    organization_users: List["OrganizationUser"] = Relationship(
        back_populates="organization",
        sa_relationship_kwargs={"lazy": "select"},
    )
    current_organization_users: List["CurrentOrganizationUser"] = Relationship(
        back_populates="organization",
        sa_relationship_kwargs={"lazy": "select"},
    )
    invitations: List["Invitation"] = Relationship(
        back_populates="organization",
        sa_relationship_kwargs={"lazy": "select"},
    )


class InvitationBase(SQLModel):
    email: str
    token: str = Field(unique=True, index=True)
    status: str = Field(default="pending")  # pending, accepted, declined
    expires_at: datetime
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class Invitation(InvitationBase, table=True):
    __tablename__ = "invitations"
    id: Optional[int] = Field(
        default=None,
        primary_key=True,
        sa_column_kwargs={"server_default": Identity()},
        sa_type=BigInteger,
    )
    organization_id: int = Field(foreign_key="organizations.id")
    invited_by_id: int = Field(foreign_key="users.id")

    # Relationships with forward references
    organization: Optional["Organization"] = Relationship(back_populates="invitations")
    invited_by: Optional["User"] = Relationship()


class OrganizationResponse(OrganizationBase):
    id: int

    class Config:
        from_attributes = True
        json_encoders = {datetime: lambda v: v.isoformat()}
