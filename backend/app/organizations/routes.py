from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from app.common.database import get_session_fast_api
from app.auth.models import OrganizationUser
from app.organizations.service import (
    create_organization,
    get_user_organizations,
    add_user_to_organization,
    remove_user_from_organization,
    get_organization_users,
    is_subdomain_available,
    is_valid_subdomain,
    create_invitation,
    send_invitation_email,
    accept_invitation,
    decline_invitation,
    get_current_organization,
    set_current_organization,
    delete_organization,
    get_organization_invitations,
)
from pydantic import BaseModel
from app.organizations.schemas import (
    OrganizationCreate,
    OrganizationResponse,
    OrganizationUserResponse,
)

router = APIRouter()


class InviteUserRequest(BaseModel):
    email: str


@router.post("/organizations", response_model=OrganizationResponse)
async def create_new_organization(
    org_data: OrganizationCreate,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    if not await is_subdomain_available(db, org_data.subdomain):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Subdomain is already taken"
        )

    organization = await create_organization(
        db,
        name=org_data.name,
        subdomain=org_data.subdomain,
        creator_user=request.state.context.user,
    )

    if not organization:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid subdomain format"
        )

    return organization


@router.get("/organizations", response_model=List[OrganizationResponse])
async def list_user_organizations(
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    return await get_user_organizations(db, request.state.context.user.id)


@router.get(
    "/organizations/{org_id}/users", response_model=List[OrganizationUserResponse]
)
async def list_organization_users(
    org_id: int,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    return await get_organization_users(db, org_id)


@router.post("/organizations/{org_id}/users/{user_id}")
async def add_user_to_org(
    org_id: int,
    user_id: int,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    if not await add_user_to_organization(db, user_id, org_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already in this organization",
        )

    return {"message": "User added to organization successfully"}


@router.delete("/organizations/{org_id}/users/{user_id}")
async def remove_user_from_org(
    org_id: int,
    user_id: int,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    # Verify current user is admin
    org_user = (
        await db.get(OrganizationUser)
        .filter(
            OrganizationUser.organization_id == org_id,
            OrganizationUser.user_id == request.state.context.user.id,
        )
        .first()
    )

    if not org_user or org_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only organization admins can remove users",
        )

    success = await remove_user_from_organization(db, user_id, org_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in organization",
        )
    return {"message": "User removed from organization"}


@router.get("/organizations/check-subdomain/{subdomain}")
async def check_subdomain_availability(
    subdomain: str, db: AsyncSession = Depends(get_session_fast_api)
):
    """Check if a subdomain is available for use."""
    if not is_valid_subdomain(subdomain):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid subdomain format",
        )

    available = await is_subdomain_available(db, subdomain)
    return {"available": available}


@router.post("/organizations/{org_id}/invites")
async def send_organization_invite(
    org_id: int,
    invite_data: InviteUserRequest,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    # check if user belongs to organization and is admin
    is_admin = any(
        user.organization_id == org_id and user.role == "admin"
        for user in request.state.context.user.organization_users
    )
    if not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only organization admins can invite users",
        )

    # Create invitation
    invitation = await create_invitation(
        db,
        email=invite_data.email,
        organization_id=org_id,
        invited_by_id=request.state.context.user.id,
    )

    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="An invitation has already been sent to this email address",
        )

    # Send invitation email
    if not await send_invitation_email(invitation):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send invitation email",
        )

    return {"message": "Invitation sent successfully"}


@router.post("/organizations/invites/{token}/accept")
async def accept_invite(
    token: str,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    invitation = await accept_invitation(db, token, request.state.context.user.id)
    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired invitation",
        )
    return {"message": "Invitation accepted successfully"}


@router.post("/organizations/invites/{token}/decline")
async def decline_invite(
    token: str,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    success = await decline_invitation(db, token, request.state.context.user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired invitation",
        )
    return {"message": "Invitation declined successfully"}


@router.get("/organizations/current")
async def get_current_org(
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    current_org = await get_current_organization(db, request.state.context.user.id)
    if not current_org:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No current organization set",
        )
    return current_org


@router.post("/organizations/{org_id}/set-current")
async def set_current_org(
    org_id: int,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    success = await set_current_organization(db, request.state.context.user.id, org_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to set current organization",
        )
    return {"message": "Current organization set successfully"}


@router.delete("/organizations/{org_id}")
async def delete_org(
    org_id: int,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    # Verify current user is admin
    org_user = (
        await db.get(OrganizationUser)
        .filter(
            OrganizationUser.organization_id == org_id,
            OrganizationUser.user_id == request.state.context.user.id,
        )
        .first()
    )

    if not org_user or org_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only organization admins can delete organizations",
        )

    success = await delete_organization(db, org_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found",
        )
    return {"message": "Organization deleted successfully"}


@router.get("/organizations/{org_id}/invites")
async def list_organization_invites(
    org_id: int,
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    # Verify current user is admin
    org_user = (
        await db.get(OrganizationUser)
        .filter(
            OrganizationUser.organization_id == org_id,
            OrganizationUser.user_id == request.state.context.user.id,
        )
        .first()
    )

    if not org_user or org_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only organization admins can view invitations",
        )

    invites = await get_organization_invitations(db, org_id)
    return invites
