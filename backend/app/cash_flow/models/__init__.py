from .fixed_expense import (
    FixedExpenseBase,
    FixedExpenseTable,
    FixedExpenseUpsert,
    FixedExpenseResponse,
)

from .misc_expense import (
    MiscExpenseBase,
    MiscExpenseTable,
    MiscExpenseUpsert,
    MiscExpenseResponse,
)

from .payroll_expense import (
    PayrollExpenseBase,
    PayrollExpenseTable,
    PayrollExpenseUpsert,
    PayrollExpenseResponse,
)

from .project import (
    ProjectBase,
    ProjectTable,
    ProjectUpsert,
    ProjectResponse,
    DeleteProjectRequest,
)

from .milestone import (
    MilestoneBase,
    MilestoneTable,
    MilestoneUpsert,
    MilestoneResponse,
)

from .project_expense import (
    ProjectExpenseBase,
    ProjectExpenseTable,
    ProjectExpenseUpsert,
    ProjectExpenseResponse,
)

from .purchase_order import (
    PurchaseOrderBase,
    PurchaseOrderTable,
    PurchaseOrderUpsert,
    PurchaseOrderResponse,
    DeletePurchaseOrderRequest,
)

from .invoice import (
    InvoiceBase,
    InvoiceTable,
    InvoiceUpsert,
    InvoiceResponse,
)

from .account import (
    AccountBase,
    AccountTable,
    AccountUpsert,
    AccountResponse,
)

from .snapshot import (
    SnapshotBase,
    SnapshotTable,
    SnapshotUpsert,
    SnapshotResponse,
)

from .forecast import (
    ForecastRequest,
    ForecastResponse,
)

from .tag import (
    TagTable,
    TagMappingTable,
    EntityTagsResponse,
    TagResponse,
    TagMappingResponse,
    TagUpsert,
    TagMappingUpsert,
    EntityType,
)

from .requests import DeleteRequest

# For backward compatibility with existing code
FixedExpense = FixedExpenseTable
MiscExpense = MiscExpenseTable
PayrollExpense = PayrollExpenseTable
Project = ProjectTable
Milestone = MilestoneTable
ProjectExpense = ProjectExpenseTable
PurchaseOrder = PurchaseOrderTable
Invoice = InvoiceTable
Account = AccountTable
Snapshot = SnapshotTable
Tag = TagTable
TagMapping = TagMappingTable

__all__ = [
    # Fixed Expense Models
    "FixedExpenseBase",
    "FixedExpenseTable",
    "FixedExpenseUpsert",
    "FixedExpenseResponse",
    "FixedExpense",  # Backward compatibility
    # Misc Expense Models
    "MiscExpenseBase",
    "MiscExpenseTable",
    "MiscExpenseUpsert",
    "MiscExpenseResponse",
    "MiscExpense",  # Backward compatibility
    # Payroll Expense Models
    "PayrollExpenseBase",
    "PayrollExpenseTable",
    "PayrollExpenseUpsert",
    "PayrollExpenseResponse",
    "PayrollExpense",  # Backward compatibility
    # Project Models
    "ProjectBase",
    "ProjectTable",
    "ProjectUpsert",
    "ProjectResponse",
    "Project",  # Backward compatibility
    "DeleteProjectRequest",
    # Milestone Models
    "MilestoneBase",
    "MilestoneTable",
    "MilestoneUpsert",
    "MilestoneResponse",
    "Milestone",  # Backward compatibility
    # Project Expense Models
    "ProjectExpenseBase",
    "ProjectExpenseTable",
    "ProjectExpenseUpsert",
    "ProjectExpenseResponse",
    "ProjectExpense",  # Backward compatibility
    # Purchase Order Models
    "PurchaseOrderBase",
    "PurchaseOrderTable",
    "PurchaseOrderUpsert",
    "PurchaseOrderResponse",
    "PurchaseOrder",  # Backward compatibility
    "DeletePurchaseOrderRequest",
    # Invoice Models
    "InvoiceBase",
    "InvoiceTable",
    "InvoiceUpsert",
    "InvoiceResponse",
    "Invoice",  # Backward compatibility
    # Account Models
    "AccountBase",
    "AccountTable",
    "AccountUpsert",
    "AccountResponse",
    "Account",  # Backward compatibility
    # Snapshot Models
    "SnapshotBase",
    "SnapshotTable",
    "SnapshotUpsert",
    "SnapshotResponse",
    "Snapshot",  # Backward compatibility
    # Forecast Models
    "ForecastRequest",
    "ForecastResponse",
    # Generic Request Models
    "DeleteRequest",
    # Tag Models
    "TagTable",
    "TagMappingTable",
    "EntityTagsResponse",
    "TagResponse",
    "TagMappingResponse",
    "TagUpsert",
    "TagMappingUpsert",
    "EntityType",
]
