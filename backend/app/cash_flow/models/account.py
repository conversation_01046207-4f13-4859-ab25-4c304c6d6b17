from sqlmodel import SQLModel, <PERSON>
from pydantic import validator
from sqlalchemy import Identity, BigInteger
from .utils import parse_amount
from typing import Optional


class AccountBase(SQLModel):
    name: str
    account_type: str
    balance: float

    @validator("balance", pre=True)
    def parse_balance(cls, v):
        return parse_amount(v) if v is not None else 0.0


class AccountTable(AccountBase, table=True):
    __tablename__ = "accounts"
    id: int | None = Field(
        sa_type=BigInteger,
        default=None,
        primary_key=True,
        nullable=False,
        sa_column_kwargs={"server_default": Identity()},
    )
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")


class AccountUpsert(AccountBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None


class AccountResponse(AccountBase):
    id: int
    organization_id: int
