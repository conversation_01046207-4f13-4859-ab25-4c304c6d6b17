from datetime import datetime
from app.enums import EntityType, TagRole
from typing import List, Optional, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship
from sqlalchemy import (
    Enum as SQLEnum,
    UniqueConstraint,
    BigInteger,
    Identity,
)
from pydantic import field_validator

if TYPE_CHECKING:
    from .project_expense import ProjectExpenseTable
    from .purchase_order import PurchaseOrderTable
    from .invoice import InvoiceTable
    from .fixed_expense import FixedExpenseTable
    from .misc_expense import MiscExpenseTable
    from .payroll_expense import PayrollExpenseTable
    from .project import ProjectTable
    from .milestone import MilestoneTable


# Base Models
class TagBase(SQLModel):
    name: str

    @field_validator("name", mode="before")
    def parse_name(cls, v):
        stripped = v.strip()
        if not stripped:
            raise ValueError("Tag name cannot be empty")
        return stripped


class TagMappingBase(SQLModel):
    tag_id: int
    entity_type: EntityType
    entity_id: int
    tag_role: TagRole = TagRole.REGULAR


# Table Models


class TagMappingTable(TagMappingBase, table=True):
    __tablename__ = "tag_mappings"

    tag_id: int = Field(
        foreign_key="tags.id",
        primary_key=True,
        sa_type=BigInteger,
        default=None,
        nullable=False,
    )
    entity_type: EntityType = Field(
        sa_type=SQLEnum(EntityType, values_callable=lambda obj: [e.value for e in obj]),
        primary_key=True,
    )
    entity_id: int = Field(primary_key=True)
    tag_role: TagRole = Field(
        sa_type=SQLEnum(TagRole, values_callable=lambda obj: [e.value for e in obj]),
        default=TagRole.REGULAR,
    )
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    tag: "TagTable" = Relationship(back_populates="tag_mappings")

    # Foreign key constraints for entity_id based on entity_type
    __table_args__ = (
        UniqueConstraint("tag_id", "entity_type", "entity_id", name="uix_tag_mapping"),
    )


class TagTable(TagBase, table=True):
    __tablename__ = "tags"

    id: Optional[int] = Field(
        sa_type=BigInteger,
        default=None,
        primary_key=True,
        nullable=False,
        sa_column_kwargs={"server_default": Identity()},
    )
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column_kwargs={"onupdate": datetime.utcnow},
    )

    # Relationships
    tag_mappings: List["TagMappingTable"] = Relationship(back_populates="tag")
    project_expenses: List["ProjectExpenseTable"] = Relationship(
        back_populates="tags",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'project_expense')",
            "secondaryjoin": "ProjectExpenseTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings",
        },
    )
    project_category: List["ProjectExpenseTable"] = Relationship(
        back_populates="category_tag",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'project_expense', "
            "TagMappingTable.tag_role == 'category')",
            "secondaryjoin": "ProjectExpenseTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings,project_expenses",
        },
    )
    milestones: List["MilestoneTable"] = Relationship(
        back_populates="tags",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'milestone')",
            "secondaryjoin": "MilestoneTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings",
        },
    )
    purchase_orders: List["PurchaseOrderTable"] = Relationship(
        back_populates="tags",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'purchase_order')",
            "secondaryjoin": "PurchaseOrderTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings",
        },
    )
    purchase_order_categories: List["PurchaseOrderTable"] = Relationship(
        back_populates="category_tag",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'purchase_order', "
            "TagMappingTable.tag_role == 'category')",
            "secondaryjoin": "PurchaseOrderTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings,purchase_orders",
        },
    )
    invoices: List["InvoiceTable"] = Relationship(
        back_populates="tags",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'invoice')",
            "secondaryjoin": "InvoiceTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings",
        },
    )
    invoice_categories: List["InvoiceTable"] = Relationship(
        back_populates="category_tag",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'invoice', "
            "TagMappingTable.tag_role == 'category')",
            "secondaryjoin": "InvoiceTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings,invoices",
        },
    )
    fixed_expenses: List["FixedExpenseTable"] = Relationship(
        back_populates="tags",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'fixed_expense')",
            "secondaryjoin": "FixedExpenseTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings",
        },
    )
    misc_expenses: List["MiscExpenseTable"] = Relationship(
        back_populates="tags",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'misc_expense')",
            "secondaryjoin": "MiscExpenseTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings",
        },
    )
    payroll_expenses: List["PayrollExpenseTable"] = Relationship(
        back_populates="tags",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'payroll_expense')",
            "secondaryjoin": "PayrollExpenseTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings",
        },
    )
    projects: List["ProjectTable"] = Relationship(
        back_populates="tags",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(TagTable.id == TagMappingTable.tag_id, "
            "TagMappingTable.entity_type == 'project')",
            "secondaryjoin": "ProjectTable.id == TagMappingTable.entity_id",
            "overlaps": "tag_mappings",
        },
    )

    # Ensure tag names are unique per organization
    __table_args__ = (
        UniqueConstraint("name", "organization_id", name="uix_tag_name_org"),
    )


# Response Models
class TagResponse(TagBase):
    id: int
    organization_id: int
    created_at: datetime
    updated_at: datetime


class TagMappingResponse(TagMappingBase):
    created_at: datetime


class TagUpsert(TagBase):
    pass


class TagMappingUpsert(TagMappingBase):
    pass


class EntityTagsResponse(SQLModel):
    tags: List[TagResponse]
