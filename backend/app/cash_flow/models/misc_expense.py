from datetime import datetime
from typing import Optional, List, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship, BigInteger
from pydantic import validator
from .utils import parse_amount, parse_date_util
from .tag import TagResponse, TagMappingTable

if TYPE_CHECKING:
    from .tag import TagTable


class MiscExpenseBase(SQLModel):
    date: datetime
    description: str
    amount: float

    @validator("date", pre=True)
    def parse_date(cls, v):
        return parse_date_util(v)

    @validator("amount", pre=True)
    def parse_expense_amount(cls, v):
        return parse_amount(v) if v is not None else 0.0


class MiscExpenseTable(MiscExpenseBase, table=True):
    __tablename__ = "misc_expenses"
    id: Optional[int] = Field(sa_type=BigInteger, default=None, primary_key=True)
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")
    tags: List["TagTable"] = Relationship(
        back_populates="misc_expenses",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(MiscExpenseTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'misc_expense')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
        },
    )


class MiscExpenseUpsert(MiscExpenseBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None
    tag_ids: Optional[List[int]] = []


class MiscExpenseResponse(MiscExpenseBase):
    id: int
    organization_id: int
    tags: List["TagResponse"] = []
