from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship
from pydantic import validator
from sqlalchemy import Identity, BigInteger
from .milestone import MilestoneResponse
from .project_expense import ProjectExpenseResponse
from .tag import TagResponse, TagMappingTable
from .utils import parse_date_util
from .invoice import InvoiceResponse
from .purchase_order import PurchaseOrderResponse

if TYPE_CHECKING:
    from .milestone import MilestoneTable
    from .project_expense import ProjectExpenseTable
    from .purchase_order import PurchaseOrderTable
    from .invoice import InvoiceTable
    from .tag import TagTable


class DeleteProjectRequest(SQLModel):
    """Request model for deleting a project."""

    id: int


class ProjectBase(SQLModel):
    name: str = Field(unique=True)
    description: Optional[str] = None
    start_date: datetime
    duration: int
    savings_percentage: float = 0.0
    status: str = "current"
    archived: bool = False
    active: bool = True

    @validator("start_date", pre=True)
    def parse_date(cls, v):
        return parse_date_util(v)

    @validator("duration", pre=True)
    def parse_duration(cls, v):
        if isinstance(v, str) and v.strip():
            return int(v)
        return v

    @validator("savings_percentage", pre=True)
    def parse_savings(cls, v):
        if v is None:
            return 0.0
        if isinstance(v, str) and v.strip():
            return float(v)
        return float(v)


class ProjectTable(ProjectBase, table=True):
    __tablename__ = "projects"
    id: Optional[int] = Field(
        sa_type=BigInteger,
        default=None,
        primary_key=True,
        nullable=False,
        sa_column_kwargs={"server_default": Identity()},
    )
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")
    milestones: List["MilestoneTable"] = Relationship(
        back_populates="project",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    project_expenses: List["ProjectExpenseTable"] = Relationship(
        back_populates="project",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    purchase_orders: List["PurchaseOrderTable"] = Relationship(
        back_populates="project",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    invoices: List["InvoiceTable"] = Relationship(
        back_populates="project",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    tags: List["TagTable"] = Relationship(
        back_populates="projects",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(ProjectTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'project')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
            "overlaps": "milestones,project_expenses,purchase_orders,invoices",
        },
    )


class ProjectUpsert(ProjectBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None
    tag_ids: Optional[List[int]] = []


class ProjectResponse(ProjectBase):
    id: int
    milestones: List["MilestoneResponse"]
    project_expenses: List["ProjectExpenseResponse"]
    purchase_orders: List["PurchaseOrderResponse"]
    invoices: List["InvoiceResponse"]
    organization_id: int
    tags: List["TagResponse"] = []
