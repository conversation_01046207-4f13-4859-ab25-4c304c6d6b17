from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship
from pydantic import field_validator
from sqlalchemy import Identity, BigInteger
from .utils import parse_amount, parse_date_util
from .tag import TagTable, TagResponse
from .tag import TagMappingTable

if TYPE_CHECKING:
    from .project import ProjectTable
    from .purchase_order import PurchaseOrderTable


class ProjectExpenseBase(SQLModel):
    date: datetime
    description: str
    amount: float

    @field_validator("date", mode="before")
    def parse_date(cls, v):
        return parse_date_util(v)

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        return parse_amount(v) if v is not None else 0.0


class ProjectExpenseTable(ProjectExpenseBase, table=True):
    __tablename__ = "project_expenses"
    id: Optional[int] = Field(
        sa_type=BigInteger,
        default=None,
        primary_key=True,
        nullable=False,
        sa_column_kwargs={"server_default": Identity()},
    )
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")
    project_id: int = Field(sa_type=BigInteger, foreign_key="projects.id")
    project: Optional["ProjectTable"] = Relationship(back_populates="project_expenses")
    tags: List["TagTable"] = Relationship(
        back_populates="project_expenses",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(ProjectExpenseTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'project_expense', "
            "TagMappingTable.tag_role == 'regular')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
            "overlaps": "project,purchase_order,category_tag",
        },
    )
    category_tag: Optional["TagTable"] = Relationship(
        back_populates="project_category",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(ProjectExpenseTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'project_expense', "
            "TagMappingTable.tag_role == 'category')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
            "overlaps": "project,purchase_order,tags",
        },
    )


class ProjectExpenseUpsert(ProjectExpenseBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None
    project_id: int
    category_tag_id: Optional[int] = None
    tag_ids: Optional[List[int]] = []


class ProjectExpenseResponse(ProjectExpenseBase):
    id: int
    project_id: int
    organization_id: int
    category_tag: Optional["TagResponse"] = None
    tags: List["TagResponse"] = []
