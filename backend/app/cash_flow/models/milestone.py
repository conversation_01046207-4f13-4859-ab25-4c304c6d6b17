from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship
from pydantic import field_validator
from sqlalchemy import Identity, BigInteger
from .utils import parse_amount, parse_date_util
from .tag import TagTable, TagResponse
from .tag import TagMappingTable

if TYPE_CHECKING:
    from .project import ProjectTable


class MilestoneBase(SQLModel):
    description: str
    amount: float
    date: datetime

    @field_validator("date", mode="before")
    def parse_date(cls, v):
        return parse_date_util(v)

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        return parse_amount(v) if v is not None else 0.0


class MilestoneTable(MilestoneBase, table=True):
    __tablename__ = "milestones"
    id: Optional[int] = Field(
        sa_type=BigInteger,
        default=None,
        primary_key=True,
        nullable=False,
        sa_column_kwargs={"server_default": Identity()},
    )
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")
    project_id: int = Field(sa_type=BigInteger, foreign_key="projects.id")
    project: Optional["ProjectTable"] = Relationship(back_populates="milestones")
    tags: List["TagTable"] = Relationship(
        back_populates="milestones",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(MilestoneTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'milestone')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
            "overlaps": "project",
        },
    )


class MilestoneUpsert(MilestoneBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None
    project_id: int
    tag_ids: Optional[List[int]] = []


class MilestoneResponse(MilestoneBase):
    id: int
    project_id: int
    tags: List["TagResponse"] = []


# Failed to get accounts: Get accounts failed: Database operation failed: When initializing mapper Mapper[MilestoneTable(milestones)],
# expression "relationship("Optional['ProjectTable']")" seems to be using a generic class as the argument to relationship();
# please state the generic argument using an annotation, e.g. "project: Mapped[Optional['ProjectTable']] = relationship()"
