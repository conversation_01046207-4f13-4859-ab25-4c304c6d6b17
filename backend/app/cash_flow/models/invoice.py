from datetime import datetime
from typing import Optional, TYPE_CHECKING, List
from sqlmodel import SQLModel, Field, Relationship
from pydantic import field_validator
from sqlalchemy import Identity, BigInteger
from .utils import parse_amount, parse_date_util
from .tag import TagMappingTable
from .tag import TagResponse

if TYPE_CHECKING:
    from .project import ProjectTable
    from .purchase_order import PurchaseOrderTable
    from .tag import TagTable


class InvoiceBase(SQLModel):
    invoice_number: Optional[str] = None
    description: Optional[str] = None
    due_date: datetime
    amount: float
    status: str = "pending"

    @field_validator("due_date", mode="before")
    def parse_date(cls, v):
        return parse_date_util(v)

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        return parse_amount(v) if v is not None else 0.0


class InvoiceTable(InvoiceBase, table=True):
    __tablename__ = "invoices"
    id: Optional[int] = Field(
        sa_type=BigInteger,
        default=None,
        primary_key=True,
        nullable=False,
        sa_column_kwargs={"server_default": Identity()},
    )
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")
    project_id: int = Field(sa_type=BigInteger, foreign_key="projects.id")
    purchase_order_id: Optional[int] = Field(
        sa_type=BigInteger, foreign_key="purchase_orders.id"
    )
    project: Optional["ProjectTable"] = Relationship(back_populates="invoices")
    purchase_order: Optional["PurchaseOrderTable"] = Relationship(
        back_populates="invoices",
        sa_relationship_kwargs={
            "primaryjoin": "InvoiceTable.purchase_order_id == PurchaseOrderTable.id"
        },
    )
    tags: List["TagTable"] = Relationship(
        back_populates="invoices",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(InvoiceTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'invoice', "
            "TagMappingTable.tag_role == 'regular')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
            "overlaps": "project,purchase_order,category_tag",
        },
    )
    category_tag: Optional["TagTable"] = Relationship(
        back_populates="invoice_categories",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(InvoiceTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'invoice', "
            "TagMappingTable.tag_role == 'category')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
            "overlaps": "project,purchase_order,tags",
        },
    )

    @property
    def po_number(self) -> Optional[str]:
        """Get the PO number from the related purchase order."""
        return self.purchase_order.po_number if self.purchase_order else None


class InvoiceUpsert(InvoiceBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None
    project_id: Optional[int] = None
    purchase_order_id: Optional[int] = None
    category_tag_id: Optional[int] = None
    tag_ids: Optional[List[int]] = []


class InvoiceResponse(InvoiceBase):
    id: int
    project_id: int
    purchase_order_id: Optional[int]
    project_name: str  # Denormalized for convenience
    po_number: Optional[str]  # Include PO number in response
    organization_id: int
    category_tag: Optional["TagResponse"] = None
    tags: List["TagResponse"] = []
