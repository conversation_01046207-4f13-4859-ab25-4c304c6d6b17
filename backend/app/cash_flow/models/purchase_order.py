from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship
from pydantic import field_validator
from sqlalchemy import Identity, BigInteger
from .utils import parse_amount, parse_date_util
from .tag import TagMappingTable
from .tag import TagResponse

if TYPE_CHECKING:
    from .project import ProjectTable
    from .project_expense import ProjectExpenseTable
    from .invoice import InvoiceTable
    from .tag import TagTable


class DeletePurchaseOrderRequest(SQLModel):
    """Request model for deleting a purchase order."""

    po_number: str


class PurchaseOrderBase(SQLModel):
    po_number: str
    issue_date: datetime
    lead_time: int
    amount: float
    terms: str
    description: Optional[str] = None
    due_date: Optional[datetime] = None

    @field_validator("issue_date", mode="before")
    def parse_date(cls, v):
        return parse_date_util(v)

    @field_validator("lead_time", mode="before")
    def parse_lead_time(cls, v):
        if isinstance(v, str) and v.strip():
            return int(v)
        return v

    @field_validator("due_date", mode="before")
    def parse_due_date(cls, v):
        return parse_date_util(v)

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        return parse_amount(v) if v is not None else 0.0


class PurchaseOrderTable(PurchaseOrderBase, table=True):
    __tablename__ = "purchase_orders"
    id: Optional[int] = Field(
        sa_type=BigInteger,
        default=None,
        primary_key=True,
        nullable=False,
        sa_column_kwargs={"server_default": Identity()},
    )
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")
    po_number: str
    project_id: int = Field(sa_type=BigInteger, foreign_key="projects.id")
    project: Optional["ProjectTable"] = Relationship(back_populates="purchase_orders")
    invoices: List["InvoiceTable"] = Relationship(
        back_populates="purchase_order",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    tags: List["TagTable"] = Relationship(
        back_populates="purchase_orders",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(PurchaseOrderTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'purchase_order', "
            "TagMappingTable.tag_role == 'regular')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
            "overlaps": "project,project_expense,invoices,category_tag",
        },
    )
    category_tag: Optional["TagTable"] = Relationship(
        back_populates="purchase_order_categories",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(PurchaseOrderTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'purchase_order', "
            "TagMappingTable.tag_role == 'category')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
            "overlaps": "project,project_expense,invoices,tags",
        },
    )


class PurchaseOrderUpsert(PurchaseOrderBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None
    project_id: Optional[int] = None
    category_tag_id: Optional[int] = None
    tag_ids: List[int] = []


class PurchaseOrderResponse(PurchaseOrderBase):
    id: int
    po_number: str
    project_id: int
    project_name: str  # Denormalized for convenience
    organization_id: int
    category_tag: Optional["TagResponse"] = None
    tags: List["TagResponse"] = []
