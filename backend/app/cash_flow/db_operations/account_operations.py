from typing import List
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import AccountTable


class AccountOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_accounts(self, organization_id: int) -> List[AccountTable]:
        result = await self.session.execute(
            select(AccountTable).where(AccountTable.organization_id == organization_id)
        )
        accounts = result.scalars().all()
        return [account.model_copy(deep=True) for account in accounts]

    async def get_account_balance(
        self, account_id: int, organization_id: int
    ) -> AccountTable:
        statement = select(AccountTable).where(
            AccountTable.id == account_id,
            AccountTable.organization_id == organization_id,
        )
        result = await self.session.execute(statement)
        account = result.scalar_one_or_none()
        return account.model_copy(deep=True) if account else None

    async def add_account(self, account: AccountTable) -> AccountTable:
        account.id = None
        self.session.add(account)
        await self.session.commit()
        await self.session.refresh(account)
        return account.model_copy(deep=True)

    async def update_account(self, account: AccountTable) -> AccountTable:
        statement = select(AccountTable).where(
            AccountTable.id == account.id,
            AccountTable.organization_id == account.organization_id,
        )
        result = await self.session.execute(statement)
        db_account = result.scalar_one_or_none()
        if db_account:
            for field, value in account.model_dump(exclude={"id"}).items():
                setattr(db_account, field, value)
            await self.session.commit()
            await self.session.refresh(db_account)
            return db_account.model_copy(deep=True)
        return None
