from typing import List, Optional
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from app.cash_flow.models import InvoiceTable
from datetime import datetime


class InvoiceOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_invoices(self, organization_id: int) -> List[InvoiceTable]:
        statement = (
            select(InvoiceTable)
            .options(
                joinedload(InvoiceTable.project),
                joinedload(InvoiceTable.purchase_order),
                joinedload(InvoiceTable.tags),
                joinedload(InvoiceTable.category_tag),
            )
            .where(InvoiceTable.organization_id == organization_id)
        )
        result = await self.session.execute(statement)
        invoices = result.unique().scalars().all()
        return [invoice.model_copy(deep=True) for invoice in invoices]

    async def get_invoice_by_po_id_and_due_date(
        self,
        po_id: int,
        due_date: datetime,
        organization_id: int,
        id: Optional[int] = None,
    ) -> InvoiceTable:
        statement = select(InvoiceTable).where(
            InvoiceTable.purchase_order_id == po_id,
            InvoiceTable.due_date == due_date,
            InvoiceTable.organization_id == organization_id,
            InvoiceTable.id != id if id else True,
        )
        result = await self.session.execute(statement)
        invoice = result.unique().scalar_one_or_none()
        return invoice.model_copy(deep=True) if invoice else None

    async def get_invoice_by_id(
        self, invoice_id: int, organization_id: int
    ) -> InvoiceTable:
        statement = (
            select(InvoiceTable)
            .options(
                joinedload(InvoiceTable.project),
                joinedload(InvoiceTable.purchase_order),
                joinedload(InvoiceTable.tags),
                joinedload(InvoiceTable.category_tag),
            )
            .where(
                InvoiceTable.id == invoice_id,
                InvoiceTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        invoice = result.unique().scalar_one_or_none()
        return invoice.model_copy(deep=True) if invoice else None

    async def add_invoice(self, invoice: InvoiceTable) -> InvoiceTable:
        invoice.id = None
        self.session.add(invoice)
        await self.session.commit()
        await self.session.refresh(invoice)
        return invoice.model_copy(deep=True)

    async def update_invoice(self, invoice: InvoiceTable) -> InvoiceTable:
        statement = select(InvoiceTable).where(
            InvoiceTable.id == invoice.id,
            InvoiceTable.organization_id == invoice.organization_id,
        )
        result = await self.session.execute(statement)
        db_invoice = result.scalar_one_or_none()
        if db_invoice:
            update_data = invoice.model_dump(exclude_unset=True, exclude={"tags"})
            for key, value in update_data.items():
                setattr(db_invoice, key, value)
            await self.session.commit()
            await self.session.refresh(db_invoice)
            return db_invoice.model_copy(deep=True)
        return None

    async def delete_invoice(self, id_: int, organization_id: int) -> InvoiceTable:
        statement = select(InvoiceTable).where(
            InvoiceTable.id == id_, InvoiceTable.organization_id == organization_id
        )
        result = await self.session.execute(statement)
        invoice = result.scalar_one_or_none()
        if invoice:
            await self.session.delete(invoice)
            await self.session.commit()
            return invoice.model_copy(deep=True)
        return None
