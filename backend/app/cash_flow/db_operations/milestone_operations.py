from typing import List, Optional
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from app.cash_flow.models import MilestoneTable


class MilestoneOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_milestones(
        self, project_id: Optional[int] = None, organization_id: int = None
    ) -> List[MilestoneTable]:
        statement = (
            select(MilestoneTable)
            .options(joinedload(MilestoneTable.tags))
            .where(MilestoneTable.organization_id == organization_id)
        )
        if project_id is not None:
            statement = statement.where(MilestoneTable.project_id == project_id)
        result = await self.session.execute(statement)
        milestones = result.unique().scalars().all()
        return [milestone.model_copy(deep=True) for milestone in milestones]

    async def get_milestone_by_id(
        self, milestone_id: int, organization_id: int
    ) -> MilestoneTable:
        statement = (
            select(MilestoneTable)
            .options(joinedload(MilestoneTable.tags))
            .where(
                MilestoneTable.id == milestone_id,
                MilestoneTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        milestone = result.unique().scalar_one_or_none()
        return milestone.model_copy(deep=True) if milestone else None

    async def add_milestone(self, milestone: MilestoneTable) -> MilestoneTable:
        milestone.id = None
        self.session.add(milestone)
        await self.session.commit()
        await self.session.refresh(milestone)
        return milestone.model_copy(deep=True)

    async def update_milestone(self, milestone: MilestoneTable) -> MilestoneTable:
        statement = select(MilestoneTable).where(
            MilestoneTable.id == milestone.id,
            MilestoneTable.organization_id == milestone.organization_id,
        )
        result = await self.session.execute(statement)
        db_milestone = result.scalar_one_or_none()
        if db_milestone:
            for field, value in milestone.model_dump(exclude={"id"}).items():
                setattr(db_milestone, field, value)
            await self.session.commit()
            await self.session.refresh(db_milestone)
            return db_milestone.model_copy(deep=True)
        return None

    async def delete_milestone(self, id_: int, organization_id: int) -> MilestoneTable:
        statement = select(MilestoneTable).where(
            MilestoneTable.id == id_,
            MilestoneTable.organization_id == organization_id,
        )
        result = await self.session.execute(statement)
        milestone = result.scalar_one_or_none()
        if milestone:
            await self.session.delete(milestone)
            await self.session.commit()
            return milestone.model_copy(deep=True)
        return None
