from typing import List
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import SnapshotTable


class SnapshotOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def save_snapshot(self, snapshot: SnapshotTable) -> SnapshotTable:
        snapshot.id = None
        self.session.add(snapshot)
        await self.session.commit()
        await self.session.refresh(snapshot)
        return snapshot.model_copy(deep=True)

    async def get_snapshots(self, organization_id: int) -> List[SnapshotTable]:
        result = await self.session.execute(
            select(SnapshotTable).where(
                SnapshotTable.organization_id == organization_id
            )
        )
        snapshots = result.scalars().all()
        return [snapshot.model_copy(deep=True) for snapshot in snapshots]

    async def get_snapshot_by_id(self, id: int, organization_id: int) -> SnapshotTable:
        statement = select(SnapshotTable).where(
            SnapshotTable.id == id, SnapshotTable.organization_id == organization_id
        )
        result = await self.session.execute(statement)
        snapshot = result.scalar_one_or_none()
        return snapshot.model_copy(deep=True) if snapshot else None

    async def delete_snapshot(self, id: int, organization_id: int) -> SnapshotTable:
        statement = select(SnapshotTable).where(
            SnapshotTable.id == id, SnapshotTable.organization_id == organization_id
        )
        result = await self.session.execute(statement)
        snapshot = result.scalar_one_or_none()
        if snapshot:
            await self.session.delete(snapshot)
            await self.session.commit()
            return snapshot.model_copy(deep=True)
        return None
