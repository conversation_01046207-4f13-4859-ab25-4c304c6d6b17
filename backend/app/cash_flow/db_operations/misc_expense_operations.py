from typing import List, Optional
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from app.cash_flow.models import MiscExpenseTable


class MiscExpenseOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_misc_expenses(self, organization_id: int) -> List[MiscExpenseTable]:
        result = await self.session.execute(
            select(MiscExpenseTable)
            .options(joinedload(MiscExpenseTable.tags))
            .where(MiscExpenseTable.organization_id == organization_id)
        )
        expenses = result.unique().scalars().all()
        return [expense.model_copy(deep=True) for expense in expenses]

    async def get_misc_expense_by_id(
        self, expense_id: int, organization_id: int
    ) -> Optional[MiscExpenseTable]:
        statement = (
            select(MiscExpenseTable)
            .options(joinedload(MiscExpenseTable.tags))
            .where(
                MiscExpenseTable.id == expense_id,
                MiscExpenseTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        expense = result.unique().scalar_one_or_none()
        return expense.model_copy(deep=True) if expense else None

    async def add_misc_expense(self, expense: MiscExpenseTable) -> MiscExpenseTable:
        expense.id = None
        self.session.add(expense)
        await self.session.commit()
        await self.session.refresh(expense)
        return expense.model_copy(deep=True)

    async def update_misc_expense(self, expense: MiscExpenseTable) -> MiscExpenseTable:
        statement = select(MiscExpenseTable).where(
            MiscExpenseTable.id == expense.id,
            MiscExpenseTable.organization_id == expense.organization_id,
        )
        result = await self.session.execute(statement)
        db_expense = result.scalar_one_or_none()
        if db_expense:
            update_data = expense.model_dump(exclude_unset=True, exclude={"tags"})
            for key, value in update_data.items():
                setattr(db_expense, key, value)
            await self.session.commit()
            await self.session.refresh(db_expense)
            return db_expense.model_copy(deep=True)
        return None

    async def delete_misc_expense(
        self, id_: int, organization_id: int
    ) -> MiscExpenseTable:
        statement = select(MiscExpenseTable).where(
            MiscExpenseTable.id == id_,
            MiscExpenseTable.organization_id == organization_id,
        )
        result = await self.session.execute(statement)
        expense = result.scalar_one_or_none()
        if expense:
            await self.session.delete(expense)
            await self.session.commit()
            return expense.model_copy(deep=True)
        return None
