from typing import List
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from app.cash_flow.models import PurchaseOrderTable


class PurchaseOrderOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_purchase_orders(
        self, project_id: int = None, organization_id: int = None
    ) -> List[PurchaseOrderTable]:
        statement = (
            select(PurchaseOrderTable)
            .options(
                joinedload(PurchaseOrderTable.project),
                joinedload(PurchaseOrderTable.tags),
                joinedload(PurchaseOrderTable.category_tag),
            )
            .where(PurchaseOrderTable.organization_id == organization_id)
        )
        if project_id is not None:
            statement = statement.where(PurchaseOrderTable.project_id == project_id)
        result = await self.session.execute(statement)
        purchase_orders = result.unique().scalars().all()
        return [po.model_copy(deep=True) for po in purchase_orders]

    async def get_purchase_order_by_po_number(
        self, po_number: str, organization_id: int
    ) -> PurchaseOrderTable:
        statement = select(PurchaseOrderTable).where(
            PurchaseOrderTable.po_number == po_number,
            PurchaseOrderTable.organization_id == organization_id,
        )
        result = await self.session.execute(statement)
        po = result.unique().scalar_one_or_none()
        return po.model_copy(deep=True) if po else None

    async def get_purchase_order_by_id(
        self, po_id: int, organization_id: int
    ) -> PurchaseOrderTable:
        statement = (
            select(PurchaseOrderTable)
            .options(
                joinedload(PurchaseOrderTable.project),
                joinedload(PurchaseOrderTable.tags),
                joinedload(PurchaseOrderTable.category_tag),
            )
            .where(
                PurchaseOrderTable.id == po_id,
                PurchaseOrderTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        po = result.unique().scalar_one_or_none()
        return po.model_copy(deep=True) if po else None

    async def add_purchase_order(self, po: PurchaseOrderTable) -> PurchaseOrderTable:
        po.id = None
        self.session.add(po)
        await self.session.commit()
        await self.session.refresh(po)
        return po.model_copy(deep=True)

    async def update_purchase_order(self, po: PurchaseOrderTable) -> PurchaseOrderTable:
        # First, get the existing purchase order from the database
        statement = select(PurchaseOrderTable).where(
            PurchaseOrderTable.id == po.id,
            PurchaseOrderTable.organization_id == po.organization_id,
        )
        result = await self.session.execute(statement)
        db_po = result.scalar_one_or_none()

        if not db_po:
            return None

        # Update the existing object with new values, excluding id and organization_id
        update_data = po.model_dump(exclude={"id", "organization_id"})
        for field, value in update_data.items():
            if hasattr(db_po, field):
                setattr(db_po, field, value)

        await self.session.commit()
        await self.session.refresh(db_po)
        return db_po.model_copy(deep=True)

    async def delete_purchase_order(
        self, po_number: str, organization_id: int
    ) -> PurchaseOrderTable:
        statement = select(PurchaseOrderTable).where(
            PurchaseOrderTable.po_number == po_number,
            PurchaseOrderTable.organization_id == organization_id,
        )
        result = await self.session.execute(statement)
        po = result.scalar_one_or_none()
        if po:
            await self.session.delete(po)
            await self.session.commit()
            return po.model_copy(deep=True)
        return None
