from typing import List, Optional
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from app.cash_flow.models import ProjectExpenseTable


class ProjectExpenseOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_project_expenses(
        self, project_id: Optional[int] = None, organization_id: int = None
    ) -> List[ProjectExpenseTable]:
        statement = (
            select(ProjectExpenseTable)
            .options(
                joinedload(ProjectExpenseTable.tags),
                joinedload(ProjectExpenseTable.category_tag),
            )
            .where(ProjectExpenseTable.organization_id == organization_id)
        )
        if project_id is not None:
            statement = statement.where(ProjectExpenseTable.project_id == project_id)
        result = await self.session.execute(statement)
        project_expenses = result.unique().scalars().all()
        return [cost.model_copy(deep=True) for cost in project_expenses]

    async def get_project_expense_by_id(
        self, expense_id: int, organization_id: int
    ) -> Optional[ProjectExpenseTable]:
        statement = (
            select(ProjectExpenseTable)
            .options(
                joinedload(ProjectExpenseTable.tags),
                joinedload(ProjectExpenseTable.category_tag),
            )
            .where(
                ProjectExpenseTable.id == expense_id,
                ProjectExpenseTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        expense = result.unique().scalar_one_or_none()
        return expense.model_copy(deep=True) if expense else None

    async def add_project_expense(
        self, project_expense: ProjectExpenseTable
    ) -> ProjectExpenseTable:
        project_expense.id = None
        self.session.add(project_expense)
        await self.session.commit()
        await self.session.refresh(project_expense)
        return project_expense.model_copy(deep=True)

    async def update_project_expense(
        self, project_expense: ProjectExpenseTable
    ) -> ProjectExpenseTable:
        db_project_expense = await self.session.get(
            ProjectExpenseTable,
            project_expense.id,
        )

        if db_project_expense:
            update_data = project_expense.model_dump(
                exclude_unset=True, exclude={"tags", "category_tag"}
            )
            for key, value in update_data.items():
                setattr(db_project_expense, key, value)

            await self.session.commit()
            await self.session.refresh(db_project_expense)
            return db_project_expense.model_copy(deep=True)
        return None

    async def delete_project_expense(
        self, id_: int, organization_id: int
    ) -> ProjectExpenseTable:
        statement = select(ProjectExpenseTable).where(
            ProjectExpenseTable.id == id_,
            ProjectExpenseTable.organization_id == organization_id,
        )
        result = await self.session.execute(statement)
        project_expense = result.scalar_one_or_none()
        if project_expense:
            await self.session.delete(project_expense)
            await self.session.commit()
            return project_expense.model_copy(deep=True)
        return None
