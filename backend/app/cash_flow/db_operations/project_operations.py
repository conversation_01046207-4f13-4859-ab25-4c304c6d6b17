from typing import List, Optional
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from app.cash_flow.models import (
    ProjectTable,
    MilestoneTable,
    ProjectExpenseTable,
    PurchaseOrderTable,
    InvoiceTable,
)


class ProjectOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_projects(
        self, organization_id: int, statuses: List[str] = []
    ) -> List[ProjectTable]:
        statement = select(ProjectTable).where(
            ProjectTable.organization_id == organization_id,
            ProjectTable.status.in_(statuses),
            ProjectTable.archived.is_(False),
        )
        result = await self.session.execute(statement)
        projects = result.unique().scalars().all()
        return [project.model_copy(deep=True) for project in projects]

    async def get_current_projects(self, organization_id: int) -> List[ProjectTable]:
        statement = (
            select(ProjectTable)
            .options(
                joinedload(ProjectTable.tags),
                joinedload(ProjectTable.milestones).joinedload(MilestoneTable.tags),
                joinedload(ProjectTable.project_expenses).selectinload(
                    ProjectExpenseTable.tags
                ),
                joinedload(ProjectTable.project_expenses).selectinload(
                    ProjectExpenseTable.category_tag
                ),
                joinedload(ProjectTable.purchase_orders).joinedload(
                    PurchaseOrderTable.tags
                ),
                joinedload(ProjectTable.purchase_orders).joinedload(
                    PurchaseOrderTable.category_tag
                ),
                joinedload(ProjectTable.invoices).joinedload(InvoiceTable.tags),
                joinedload(ProjectTable.invoices).joinedload(InvoiceTable.category_tag),
                joinedload(ProjectTable.invoices).joinedload(
                    InvoiceTable.purchase_order
                ),
            )
            .where(
                ProjectTable.status == "current",
                ProjectTable.archived.is_(False),
                ProjectTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        projects = result.unique().scalars().all()
        return [project.model_copy(deep=True) for project in projects]

    async def get_anticipated_projects(
        self, organization_id: int
    ) -> List[ProjectTable]:
        statement = (
            select(ProjectTable)
            .options(
                joinedload(ProjectTable.tags),
                joinedload(ProjectTable.milestones).joinedload(MilestoneTable.tags),
                joinedload(ProjectTable.project_expenses).selectinload(
                    ProjectExpenseTable.tags
                ),
                joinedload(ProjectTable.project_expenses).selectinload(
                    ProjectExpenseTable.category_tag
                ),
                joinedload(ProjectTable.purchase_orders).joinedload(
                    PurchaseOrderTable.tags
                ),
                joinedload(ProjectTable.purchase_orders).joinedload(
                    PurchaseOrderTable.category_tag
                ),
                joinedload(ProjectTable.invoices).joinedload(InvoiceTable.tags),
                joinedload(ProjectTable.invoices).joinedload(InvoiceTable.category_tag),
                joinedload(ProjectTable.invoices).joinedload(
                    InvoiceTable.purchase_order
                ),
            )
            .where(
                ProjectTable.status == "anticipated",
                ProjectTable.archived.is_(False),
                ProjectTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        projects = result.unique().scalars().all()
        return [project.model_copy(deep=True) for project in projects]

    async def get_archived_projects(self, organization_id: int) -> List[ProjectTable]:
        statement = (
            select(ProjectTable)
            .options(
                joinedload(ProjectTable.tags),
                joinedload(ProjectTable.milestones).joinedload(MilestoneTable.tags),
                joinedload(ProjectTable.project_expenses).selectinload(
                    ProjectExpenseTable.tags
                ),
                joinedload(ProjectTable.project_expenses).selectinload(
                    ProjectExpenseTable.category_tag
                ),
                joinedload(ProjectTable.purchase_orders).joinedload(
                    PurchaseOrderTable.tags
                ),
                joinedload(ProjectTable.purchase_orders).joinedload(
                    PurchaseOrderTable.category_tag
                ),
                joinedload(ProjectTable.invoices).joinedload(InvoiceTable.tags),
                joinedload(ProjectTable.invoices).joinedload(InvoiceTable.category_tag),
                joinedload(ProjectTable.invoices).joinedload(
                    InvoiceTable.purchase_order
                ),
            )
            .where(
                ProjectTable.archived.is_(True),
                ProjectTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        projects = result.unique().scalars().all()
        return [project.model_copy(deep=True) for project in projects]

    async def get_project_by_id(
        self, project_id: int, organization_id: int
    ) -> Optional[ProjectTable]:
        statement = (
            select(ProjectTable)
            .options(
                joinedload(ProjectTable.tags),
                joinedload(ProjectTable.milestones).joinedload(MilestoneTable.tags),
                joinedload(ProjectTable.project_expenses).selectinload(
                    ProjectExpenseTable.tags
                ),
                joinedload(ProjectTable.project_expenses).selectinload(
                    ProjectExpenseTable.category_tag
                ),
                joinedload(ProjectTable.purchase_orders).joinedload(
                    PurchaseOrderTable.tags
                ),
                joinedload(ProjectTable.purchase_orders).joinedload(
                    PurchaseOrderTable.category_tag
                ),
                joinedload(ProjectTable.invoices).joinedload(InvoiceTable.tags),
                joinedload(ProjectTable.invoices).joinedload(InvoiceTable.category_tag),
                joinedload(ProjectTable.invoices).joinedload(
                    InvoiceTable.purchase_order
                ),
            )
            .where(
                ProjectTable.id == project_id,
                ProjectTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        project = result.unique().scalar_one_or_none()
        return project.model_copy(deep=True) if project else None

    async def add_current_project(self, project: ProjectTable) -> ProjectTable:
        project.id = None
        project.status = "current"
        project.archived = False
        project.active = True
        self.session.add(project)
        await self.session.commit()
        await self.session.refresh(project)
        return project.model_copy(deep=True)

    async def add_anticipated_project(self, project: ProjectTable) -> ProjectTable:
        project.id = None
        project.status = "anticipated"
        project.archived = False
        project.active = True
        self.session.add(project)
        await self.session.commit()
        await self.session.refresh(project)
        return project.model_copy(deep=True)

    async def update_project(self, project: ProjectTable) -> ProjectTable:
        statement = select(ProjectTable).where(
            ProjectTable.id == project.id,
            ProjectTable.organization_id == project.organization_id,
        )
        result = await self.session.execute(statement)
        db_project = result.scalar_one_or_none()
        if db_project:
            update_data = project.model_dump(
                exclude_unset=True,
                exclude={"id", "milestones", "project_expenses", "tags"},
            )
            for key, value in update_data.items():
                setattr(db_project, key, value)

            # Remove all old relationships
            for m in db_project.milestones:
                await self.session.delete(m)
            for h in db_project.project_expenses:
                await self.session.delete(h)
            await self.session.commit()

            # Add new relationships
            if project.milestones:
                for milestone in project.milestones:
                    milestone.project_name = db_project.name
                    db_project.milestones.append(milestone)
            if project.project_expenses:
                for projectExpense in project.project_expenses:
                    projectExpense.project_name = db_project.name
                    db_project.project_expenses.append(projectExpense)

            await self.session.commit()
            await self.session.refresh(db_project)
            return db_project.model_copy(deep=True)
        return None

    async def delete_project(self, id_: int, organization_id: int) -> ProjectTable:
        statement = select(ProjectTable).where(
            ProjectTable.id == id_, ProjectTable.organization_id == organization_id
        )
        result = await self.session.execute(statement)
        project = result.scalar_one_or_none()
        if project:
            await self.session.delete(project)
            await self.session.commit()
            return project.model_copy(deep=True)
        return None

    async def archive_project(self, id_: int, organization_id: int) -> ProjectTable:
        statement = select(ProjectTable).where(
            ProjectTable.id == id_, ProjectTable.organization_id == organization_id
        )
        result = await self.session.execute(statement)
        project = result.scalar_one_or_none()
        if project:
            project.archived = True
            await self.session.commit()
            return project.model_copy(deep=True)
        return None

    async def restore_project(self, id_: int, organization_id: int) -> ProjectTable:
        statement = select(ProjectTable).where(
            ProjectTable.id == id_, ProjectTable.organization_id == organization_id
        )
        result = await self.session.execute(statement)
        project = result.scalar_one_or_none()
        if project:
            project.archived = False
            await self.session.commit()
            return project.model_copy(deep=True)
        return None
