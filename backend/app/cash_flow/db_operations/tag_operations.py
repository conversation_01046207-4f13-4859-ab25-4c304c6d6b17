from typing import List, Optional
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_

from app.cash_flow.models import (
    TagTable,
    TagMappingTable,
    EntityType,
    ProjectExpenseTable,
)
from app.enums import TagRole


class TagOperations:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_tag(self, tag: TagTable) -> TagTable:
        """Create a new tag for an organization."""
        tag.id = None
        self.session.add(tag)
        try:
            await self.session.commit()
            await self.session.refresh(tag)
            return tag.model_copy(deep=True)
        except IntegrityError:
            await self.session.rollback()
            raise ValueError(
                f"Tag with name '{tag.name}' already exists in this organization"
            )

    async def get_tags_by_organization(self, organization_id: int) -> List[TagTable]:
        """Get all tags for an organization."""
        statement = select(TagTable).where(TagTable.organization_id == organization_id)
        result = await self.session.execute(statement)
        tags = result.scalars().all()
        return [tag.model_copy(deep=True) for tag in tags]

    async def get_tag_by_id(
        self, tag_id: int, organization_id: int
    ) -> Optional[TagTable]:
        """Get a tag by its ID."""
        statement = select(TagTable).where(
            TagTable.id == tag_id, TagTable.organization_id == organization_id
        )
        result = await self.session.execute(statement)
        tag = result.scalar_one_or_none()
        return tag.model_copy(deep=True) if tag else None

    async def update_tag(
        self, tag_id: int, name: str, organization_id: int
    ) -> Optional[TagTable]:
        """Update a tag's name."""
        tag = await self.get_tag_by_id(tag_id, organization_id)
        if not tag:
            return None
        tag.name = name
        try:
            await self.session.commit()
            await self.session.refresh(tag)
            return tag.model_copy(deep=True)
        except IntegrityError:
            await self.session.rollback()
            raise ValueError(
                f"Tag with name '{name}' already exists in this organization"
            )

    async def delete_tag(self, tag_id: int, organization_id: int) -> bool:
        """Delete a tag and all its mappings."""
        tag = await self.get_tag_by_id(tag_id, organization_id)
        if not tag:
            return True  # Tag not found, so it's already deleted

        # Delete all mappings first
        statement = select(TagMappingTable).where(TagMappingTable.tag_id == tag_id)
        result = await self.session.execute(statement)
        mappings = result.scalars().all()
        for mapping in mappings:
            await self.session.delete(mapping)

        # Delete the tag
        tag_to_delete = await self.session.get(TagTable, tag_id)
        if tag_to_delete:
            await self.session.delete(tag_to_delete)
            await self.session.commit()
            return True
        return False

    async def add_tag_to_entity(
        self,
        tag_id: int,
        entity_type: EntityType,
        entity_id: int,
        tag_role: TagRole = TagRole.REGULAR,
    ) -> TagMappingTable:
        """Add a tag to an entity."""
        mapping = TagMappingTable(
            tag_id=tag_id,
            entity_type=entity_type,
            entity_id=entity_id,
            tag_role=tag_role,
        )
        self.session.add(mapping)
        try:
            await self.session.commit()
            await self.session.refresh(mapping)
            return mapping.model_copy(deep=True)
        except IntegrityError:
            await self.session.rollback()
            raise ValueError("This tag is already applied to this entity")

    async def get_tags_by_ids(
        self, tag_ids: List[int], organization_id: int
    ) -> List[TagTable]:
        """Get tags by ids."""
        statement = select(TagTable).where(
            TagTable.id.in_(tag_ids), TagTable.organization_id == organization_id
        )
        result = await self.session.execute(statement)
        tags = result.scalars().all()
        return [tag.model_copy(deep=True) for tag in tags]

    async def remove_tag_from_entity(
        self, tag_id: int, entity_type: EntityType, entity_id: int
    ) -> bool:
        """Remove a tag from an entity."""
        statement = select(TagMappingTable).where(
            TagMappingTable.tag_id == tag_id,
            TagMappingTable.entity_type == entity_type,
            TagMappingTable.entity_id == entity_id,
        )
        result = await self.session.execute(statement)
        mapping = result.scalar_one_or_none()
        if not mapping:
            return True  # Tag not found, so it's already deleted
        await self.session.delete(mapping)
        await self.session.commit()
        return True

    async def get_entity_tags(
        self, entity_type: EntityType, entity_id: int, organization_id: int
    ) -> List[TagTable]:
        """Get all tags for a specific entity."""
        statement = (
            select(TagTable)
            .join(TagMappingTable)
            .where(
                TagMappingTable.entity_type == entity_type,
                TagMappingTable.entity_id == entity_id,
                TagTable.organization_id == organization_id,
            )
        )
        result = await self.session.execute(statement)
        tags = result.scalars().all()
        return [tag.model_copy(deep=True) for tag in tags]

    async def get_tagged_entities(
        self, tag_id: int, entity_type: Optional[EntityType] = None
    ) -> List[TagMappingTable]:
        """Get all entities tagged with a specific tag."""
        statement = select(TagMappingTable).where(TagMappingTable.tag_id == tag_id)
        if entity_type:
            statement = statement.where(TagMappingTable.entity_type == entity_type)
        result = await self.session.execute(statement)
        mappings = result.scalars().all()
        return [mapping.model_copy(deep=True) for mapping in mappings]

    async def set_entity_tags(
        self,
        entity_id: int,
        entity_type: EntityType,
        tag_ids: List[int],
        tag_role: TagRole = TagRole.REGULAR,
    ) -> bool:
        """Set the tags for a specific entity, removing old ones for the specified role."""
        # First, remove all existing tag mappings for this entity and role
        delete_statement = select(TagMappingTable).where(
            TagMappingTable.entity_id == entity_id,
            TagMappingTable.entity_type == entity_type,
            TagMappingTable.tag_role == tag_role,
        )
        result = await self.session.execute(delete_statement)
        for mapping in result.scalars().all():
            await self.session.delete(mapping)

        # Then, add the new tag mappings
        # For category tags, only take the first tag_id since there can only be one
        if tag_role == TagRole.CATEGORY:
            tag_ids = tag_ids[:1] if tag_ids else []

        for tag_id in tag_ids:
            mapping = TagMappingTable(
                tag_id=tag_id,
                entity_type=entity_type,
                entity_id=entity_id,
                tag_role=tag_role,
            )
            self.session.add(mapping)

        try:
            await self.session.commit()
            return True
        except IntegrityError as e:
            await self.session.rollback()
            # This could happen if a tag_id doesn't exist.
            # The service layer should validate tags before calling this.
            raise ValueError(f"Error setting tags: {e}")

    async def get_project_category_tags(
        self, project_id: int, organization_id: int
    ) -> List[TagTable]:
        """Get all category tags used by project expenses in a specific project."""
        statement = (
            select(TagTable)
            .select_from(TagTable)
            .join(
                TagMappingTable,
                and_(
                    TagTable.id == TagMappingTable.tag_id,
                    TagMappingTable.entity_type == EntityType.PROJECT_EXPENSE,
                    TagMappingTable.tag_role == TagRole.CATEGORY,
                ),
            )
            .join(
                ProjectExpenseTable,
                and_(
                    TagMappingTable.entity_id == ProjectExpenseTable.id,
                    ProjectExpenseTable.project_id == project_id,
                    ProjectExpenseTable.organization_id == organization_id,
                ),
            )
            .where(TagTable.organization_id == organization_id)
            .distinct()
        )
        result = await self.session.execute(statement)
        tags = result.unique().scalars().all()
        return [tag.model_copy(deep=True) for tag in tags]
