from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import PurchaseOrderOperations
from app.cash_flow.services.tag_service import TagService
from app.cash_flow.models import (
    PurchaseOrderTable,
    PurchaseOrderUpsert,
    PurchaseOrderResponse,
    DeleteRequest,
    TagResponse,
)
from app.enums import EntityType, TagRole


def _create_po_response(po: PurchaseOrderTable) -> PurchaseOrderResponse:
    return PurchaseOrderResponse(
        **po.model_dump(exclude={"project"}),
        project_name=po.project.name if po.project else "Unknown",
        tags=[TagResponse.model_validate(tag) for tag in po.tags],
        category_tag=(
            TagResponse.model_validate(po.category_tag) if po.category_tag else None
        ),
    )


class PurchaseOrderService:
    def __init__(self, session: AsyncSession):
        self.db = PurchaseOrderOperations(session)
        self.tag_service = TagService(session)

    async def get_purchase_orders(
        self, project_id: Optional[int] = None, organization_id: int = None
    ) -> List[PurchaseOrderResponse]:
        """Get all purchase orders, optionally filtered by project_id."""
        purchase_orders = await self.db.get_purchase_orders(project_id, organization_id)
        return [_create_po_response(po) for po in purchase_orders]

    async def check_po_exists(self, po_number: str, organization_id: int) -> str:
        """Check if a purchase order number already exists."""
        po = await self.db.get_purchase_order_by_po_number(po_number, organization_id)
        if po is not None:
            return f"Purchase order number {po_number} has already been used on another purchase order"
        return None

    async def add_purchase_order(
        self, po: PurchaseOrderUpsert, organization_id: int
    ) -> PurchaseOrderResponse:
        """Add a new purchase order."""
        # Check if PO number already exists
        error_message = await self.check_po_exists(po.po_number, organization_id)
        if error_message:
            raise ValueError(error_message)

        po.organization_id = organization_id
        tag_ids = po.tag_ids or []
        category_tag_id = po.category_tag_id

        db_po = PurchaseOrderTable.model_validate(
            po.model_dump(exclude={"tag_ids", "category_tag_id"})
        )
        created_po = await self.db.add_purchase_order(db_po)

        if tag_ids:
            await self.tag_service.set_entity_tags(
                created_po.id,
                EntityType.PURCHASE_ORDER,
                tag_ids,
                organization_id,
                TagRole.REGULAR,
            )

        if category_tag_id:
            await self.tag_service.set_entity_tags(
                created_po.id,
                EntityType.PURCHASE_ORDER,
                [category_tag_id],
                organization_id,
                TagRole.CATEGORY,
            )

        full_po = await self.db.get_purchase_order_by_id(created_po.id, organization_id)
        if not full_po:
            raise ValueError("Could not retrieve created purchase order")
        return _create_po_response(full_po)

    async def update_purchase_order(
        self, po_update: PurchaseOrderUpsert, organization_id: int
    ) -> PurchaseOrderResponse:
        """Update an existing purchase order."""
        current_po = await self.db.get_purchase_order_by_id(
            po_update.id, organization_id
        )
        if not current_po:
            raise ValueError(f"Purchase order {po_update.id} not found")

        # Check if the new PO number already exists (if it's being changed)
        if current_po.po_number != po_update.po_number:
            error_message = await self.check_po_exists(
                po_update.po_number, organization_id
            )
            if error_message:
                raise ValueError(error_message)

        try:
            # Store tag information before updating
            tag_ids = po_update.tag_ids
            category_tag_id = po_update.category_tag_id

            # Create a new PurchaseOrderTable with the updated data
            # Merge the update data with current data, using update data where not None
            update_data = po_update.model_dump(exclude={"tag_ids", "category_tag_id"})
            current_data = current_po.model_dump()

            # Merge the data, preferring update_data where not None
            merged_data = {}
            for field in update_data:
                if update_data[field] is not None:
                    merged_data[field] = update_data[field]
                else:
                    merged_data[field] = current_data.get(field)

            # Ensure organization_id is set correctly
            merged_data["organization_id"] = organization_id

            # Create the updated purchase order object
            db_po = PurchaseOrderTable.model_validate(merged_data)

            # Update in database
            updated_po = await self.db.update_purchase_order(db_po)
            if not updated_po:
                raise ValueError(f"Purchase order {po_update.id} not found")

            # Update tags if provided
            if tag_ids is not None:
                await self.tag_service.set_entity_tags(
                    updated_po.id,
                    EntityType.PURCHASE_ORDER,
                    tag_ids,
                    organization_id,
                    TagRole.REGULAR,
                )

            if category_tag_id is not None:
                await self.tag_service.set_entity_tags(
                    updated_po.id,
                    EntityType.PURCHASE_ORDER,
                    [category_tag_id] if category_tag_id else [],
                    organization_id,
                    TagRole.CATEGORY,
                )

            # Get the fully updated purchase order with all relationships
            full_po = await self.db.get_purchase_order_by_id(
                updated_po.id, organization_id
            )
            if not full_po:
                raise ValueError("Could not retrieve updated purchase order")
            return _create_po_response(full_po)
        except Exception as e:
            raise ValueError(f"Failed to update purchase order: {str(e)}")

    async def delete_purchase_order(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> List[PurchaseOrderResponse]:
        """Delete a purchase order."""
        try:
            deleted = await self.db.delete_purchase_order(
                delete_req.id, organization_id
            )
            if not deleted:
                raise ValueError(f"Purchase order {delete_req.id} not found")
            return await self.get_purchase_orders(organization_id=organization_id)
        except Exception as e:
            raise ValueError(f"Failed to delete purchase order: {str(e)}")
