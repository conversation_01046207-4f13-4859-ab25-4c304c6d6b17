from app.cash_flow.models import (
    ProjectTable,
    ProjectResponse,
    TagResponse,
    PurchaseOrderResponse,
    InvoiceResponse,
    MilestoneResponse,
    ProjectExpenseResponse,
)


def create_project_response(project: ProjectTable) -> ProjectResponse:
    """Helper function to create a ProjectResponse from a ProjectTable."""
    milestones = [
        MilestoneResponse(
            **m.model_dump(exclude={"tags"}),
            project_name=project.name,
            tags=[TagResponse.model_validate(tag) for tag in m.tags],
        )
        for m in project.milestones
    ]

    project_expenses = [
        ProjectExpenseResponse(
            **pe.model_dump(exclude={"tags", "category_tag"}),
            project_name=project.name,
            tags=[TagResponse.model_validate(tag) for tag in pe.tags],
            category_tag=(
                TagResponse.model_validate(pe.category_tag) if pe.category_tag else None
            ),
        )
        for pe in project.project_expenses
    ]

    purchase_orders = [
        PurchaseOrderResponse(
            **po.model_dump(exclude={"tags", "category_tag"}),
            project_name=project.name,
            tags=[TagResponse.model_validate(tag) for tag in po.tags],
            category_tag=(
                TagResponse.model_validate(po.category_tag) if po.category_tag else None
            ),
        )
        for po in project.purchase_orders
    ]

    invoices = [
        InvoiceResponse(
            **inv.model_dump(exclude={"tags", "category_tag", "purchase_order"}),
            project_name=project.name,
            tags=[TagResponse.model_validate(tag) for tag in inv.tags],
            category_tag=(
                TagResponse.model_validate(inv.category_tag) if inv.category_tag else None
            ),
            po_number=inv.purchase_order.po_number if inv.purchase_order else None,
        )
        for inv in project.invoices
    ]

    return ProjectResponse(
        **project.model_dump(
            exclude={
                "tags",
                "milestones",
                "project_expenses",
                "purchase_orders",
                "invoices",
            }
        ),
        tags=[TagResponse.model_validate(tag) for tag in project.tags],
        milestones=milestones,
        project_expenses=project_expenses,
        purchase_orders=purchase_orders,
        invoices=invoices,
    )
