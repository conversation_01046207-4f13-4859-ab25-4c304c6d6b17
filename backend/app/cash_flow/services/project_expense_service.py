from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import ProjectExpenseOperations
from app.cash_flow.models import (
    ProjectExpenseTable,
    ProjectExpenseUpsert,
    ProjectExpenseResponse,
    DeleteRequest,
    TagResponse,
)
from app.enums import EntityType, TagRole
from app.cash_flow.services.tag_service import TagService


class ProjectExpenseService:
    def __init__(self, session: AsyncSession):
        self.db = ProjectExpenseOperations(session)
        self.tag_service = TagService(session)
        self.session = session

    async def get_project_expenses(
        self, project_id: Optional[int] = None, organization_id: int = None
    ) -> List[ProjectExpenseResponse]:
        """Get expenses, optionally filtered by project_id."""
        project_expenses = await self.db.get_project_expenses(
            project_id, organization_id
        )
        return [
            ProjectExpenseResponse(
                **expense.model_dump(exclude={"project"}),
                tags=[TagResponse.model_validate(tag) for tag in expense.tags],
                category_tag=(
                    TagResponse.model_validate(expense.category_tag)
                    if expense.category_tag
                    else None
                ),
            )
            for expense in project_expenses
        ]

    async def add_project_expense(
        self, project_expense: ProjectExpenseUpsert, organization_id: int
    ) -> ProjectExpenseResponse:
        """Add a new project expense."""
        project_expense.organization_id = organization_id
        tag_ids = project_expense.tag_ids or []
        category_tag_id = project_expense.category_tag_id

        db_project_expense = ProjectExpenseTable.model_validate(
            project_expense.model_dump(exclude={"tag_ids", "category_tag_id"})
        )

        created_expense = await self.db.add_project_expense(db_project_expense)

        if tag_ids:
            await self.tag_service.set_entity_tags(
                entity_id=created_expense.id,
                entity_type=EntityType.PROJECT_EXPENSE,
                tag_ids=tag_ids,
                organization_id=organization_id,
                tag_role=TagRole.REGULAR,
            )

        if category_tag_id:
            await self.tag_service.set_entity_tags(
                entity_id=created_expense.id,
                entity_type=EntityType.PROJECT_EXPENSE,
                tag_ids=[category_tag_id],
                organization_id=organization_id,
                tag_role=TagRole.CATEGORY,
            )

        full_expense = await self.db.get_project_expense_by_id(
            created_expense.id, organization_id
        )
        if not full_expense:
            raise ValueError("Could not retrieve created project expense")

        return ProjectExpenseResponse(
            **full_expense.model_dump(exclude={"project"}),
            tags=[TagResponse.model_validate(tag) for tag in full_expense.tags],
            category_tag=(
                TagResponse.model_validate(full_expense.category_tag)
                if full_expense.category_tag
                else None
            ),
        )

    async def update_project_expense(
        self, project_expense: ProjectExpenseUpsert, organization_id: int
    ) -> ProjectExpenseResponse:
        """Update an existing project expense."""
        project_expense.organization_id = organization_id
        tag_ids = project_expense.tag_ids
        category_tag_id = project_expense.category_tag_id

        db_project_expense = ProjectExpenseTable.model_validate(
            project_expense.model_dump(
                exclude={"tag_ids", "category_tag_id"}, exclude_unset=True
            )
        )

        updated_expense = await self.db.update_project_expense(db_project_expense)
        if not updated_expense:
            raise ValueError(f"Project expense with ID {project_expense.id} not found")

        if tag_ids is not None:
            await self.tag_service.set_entity_tags(
                entity_id=updated_expense.id,
                entity_type=EntityType.PROJECT_EXPENSE,
                tag_ids=tag_ids,
                organization_id=organization_id,
                tag_role=TagRole.REGULAR,
            )

        if category_tag_id is not None:
            await self.tag_service.set_entity_tags(
                entity_id=updated_expense.id,
                entity_type=EntityType.PROJECT_EXPENSE,
                tag_ids=[category_tag_id] if category_tag_id else [],
                organization_id=organization_id,
                tag_role=TagRole.CATEGORY,
            )

        full_expense = await self.db.get_project_expense_by_id(
            updated_expense.id, organization_id
        )
        if not full_expense:
            raise ValueError("Could not retrieve updated project expense")

        return ProjectExpenseResponse(
            **full_expense.model_dump(exclude={"project"}),
            tags=[TagResponse.model_validate(tag) for tag in full_expense.tags],
            category_tag=(
                TagResponse.model_validate(full_expense.category_tag)
                if full_expense.category_tag
                else None
            ),
        )

    async def delete_project_expense(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> List[ProjectExpenseResponse]:
        """Delete a project expense."""
        deleted = await self.db.delete_project_expense(delete_req.id, organization_id)
        if not deleted:
            raise ValueError(f"Project expense with ID {delete_req.id} not found")
        return await self.get_project_expenses(organization_id=organization_id)
