from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import MilestoneOperations
from app.cash_flow.models import (
    MilestoneTable,
    MilestoneUpsert,
    MilestoneResponse,
    DeleteRequest,
    TagResponse,
)
from app.enums import EntityType
from app.cash_flow.services.tag_service import TagService


class MilestoneService:
    def __init__(self, session: AsyncSession):
        self.db = MilestoneOperations(session)
        self.tag_service = TagService(session)

    async def get_milestones(
        self, project_id: Optional[int] = None, organization_id: int = None
    ) -> List[MilestoneResponse]:
        """Get milestones, optionally filtered by project_id."""
        milestones = await self.db.get_milestones(project_id, organization_id)
        return [
            MilestoneResponse(
                **milestone.model_dump(exclude={"tags"}),
                tags=[TagResponse.model_validate(tag) for tag in milestone.tags],
            )
            for milestone in milestones
        ]

    async def add_milestone(
        self, milestone: MilestoneUpsert, organization_id: int
    ) -> MilestoneResponse:
        """Add a new milestone."""
        milestone.organization_id = organization_id
        tag_ids = milestone.tag_ids or []
        db_milestone = MilestoneTable.model_validate(
            milestone.model_dump(exclude={"tag_ids"})
        )

        created_milestone = await self.db.add_milestone(db_milestone)

        await self.tag_service.set_entity_tags(
            entity_id=created_milestone.id,
            entity_type=EntityType.MILESTONE,
            tag_ids=tag_ids,
            organization_id=organization_id,
        )

        full_milestone = await self.db.get_milestone_by_id(
            created_milestone.id, organization_id
        )
        if not full_milestone:
            raise ValueError("Could not retrieve created milestone")

        return MilestoneResponse(
            **full_milestone.model_dump(exclude={"tags"}),
            tags=[TagResponse.model_validate(tag) for tag in full_milestone.tags],
        )

    async def update_milestone(
        self, milestone: MilestoneUpsert, organization_id: int
    ) -> MilestoneResponse:
        """Update an existing milestone."""
        milestone.organization_id = organization_id
        db_milestone = MilestoneTable.model_validate(
            milestone.model_dump(exclude={"tag_ids"})
        )
        updated_milestone = await self.db.update_milestone(db_milestone)
        if not updated_milestone:
            raise ValueError(f"Milestone with ID {milestone.id} not found")

        await self.tag_service.set_entity_tags(
            entity_id=updated_milestone.id,
            entity_type=EntityType.MILESTONE,
            tag_ids=milestone.tag_ids or [],
            organization_id=organization_id,
        )

        full_milestone = await self.db.get_milestone_by_id(
            updated_milestone.id, organization_id
        )
        if not full_milestone:
            raise ValueError("Could not retrieve updated milestone")

        return MilestoneResponse(
            **full_milestone.model_dump(exclude={"tags"}),
            tags=[TagResponse.model_validate(tag) for tag in full_milestone.tags],
        )

    async def delete_milestone(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> List[MilestoneResponse]:
        """Delete a milestone."""
        deleted = await self.db.delete_milestone(delete_req.id, organization_id)
        if not deleted:
            raise ValueError(f"Milestone with ID {delete_req.id} not found")
        return await self.get_milestones(organization_id=organization_id)
