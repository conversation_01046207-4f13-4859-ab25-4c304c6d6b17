from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import TagOperations
from app.cash_flow.models import (
    TagTable,
    TagUpsert,
    TagMappingUpsert,
    TagResponse,
    TagMappingResponse,
    EntityTagsResponse,
    EntityType,
    DeleteRequest,
)
from app.enums import TagRole


class TagService:
    def __init__(self, session: AsyncSession):
        self.db = TagOperations(session)

    async def get_tags(self, organization_id: int) -> List[TagResponse]:
        """Get all tags for an organization."""
        tags = await self.db.get_tags_by_organization(organization_id)
        return [TagResponse.model_validate(tag) for tag in tags]

    async def get_tags_by_ids(
        self, tag_ids: List[int], organization_id: int
    ) -> List[TagResponse]:
        """Get tags by ids."""
        tags = await self.db.get_tags_by_ids(tag_ids, organization_id)
        return [TagResponse.model_validate(tag) for tag in tags]

    async def create_tag(self, tag: TagUpsert, organization_id: int) -> TagResponse:
        """Create a new tag for an organization."""
        db_tag = TagTable(name=tag.name, organization_id=organization_id)
        created_tag = await self.db.create_tag(db_tag)
        return TagResponse.model_validate(created_tag)

    async def update_tag(
        self, tag_id: int, tag: TagUpsert, organization_id: int
    ) -> TagResponse:
        """Update a tag's name."""
        db_tag = await self.db.get_tag_by_id(tag_id, organization_id)
        if not db_tag:
            raise ValueError("Tag not found")

        updated_tag = await self.db.update_tag(tag_id, tag.name, organization_id)
        if not updated_tag:
            raise ValueError("Tag not found during update")
        return TagResponse.model_validate(updated_tag)

    async def delete_tag(self, delete_req: DeleteRequest, organization_id: int) -> bool:
        """Delete a tag and all its mappings."""
        return await self.db.delete_tag(delete_req.id, organization_id)

    async def add_tag_to_entity(
        self, mapping: TagMappingUpsert, organization_id: int
    ) -> TagMappingResponse:
        """Add a tag to an entity."""
        tag = await self.db.get_tag_by_id(mapping.tag_id, organization_id)
        if not tag:
            raise ValueError("Tag not found")

        db_mapping = await self.db.add_tag_to_entity(
            mapping.tag_id, mapping.entity_type, mapping.entity_id
        )
        return TagMappingResponse.model_validate(db_mapping)

    async def remove_tag_from_entity(
        self, tag_id: int, entity_type: EntityType, entity_id: int
    ) -> bool:
        """Remove a tag from an entity."""
        # remove tag from entity without checking if it exists because regardless if the tag exists or not
        # if there is an entity with the tag number passed in we want to remove the tag from the entity.
        return await self.db.remove_tag_from_entity(tag_id, entity_type, entity_id)

    async def get_entity_tags(
        self, entity_type: EntityType, entity_id: int
    ) -> EntityTagsResponse:
        """Get all tags for a specific entity."""
        tags = await self.db.get_entity_tags(entity_type, entity_id)
        return EntityTagsResponse(
            tags=[TagResponse.model_validate(tag) for tag in tags]
        )

    async def set_entity_tags(
        self,
        entity_id: int,
        entity_type: EntityType,
        tag_ids: List[int],
        organization_id: int,
        tag_role: TagRole = TagRole.REGULAR,
    ) -> bool:
        """Set the tags for an entity, ensuring tags belong to the organization."""
        if not tag_ids:
            # If there are no tags, just clear existing ones for this role.
            return await self.db.set_entity_tags(entity_id, entity_type, [], tag_role)

        # Verify all tags belong to the organization
        tags = await self.db.get_tags_by_ids(tag_ids, organization_id)
        if len(tags) != len(tag_ids):
            raise ValueError("One or more tags not found.")

        return await self.db.set_entity_tags(entity_id, entity_type, tag_ids, tag_role)

    async def get_project_category_tags(
        self, project_id: int, organization_id: int
    ) -> List[TagResponse]:
        """Get all category tags used by project expenses in a specific project."""
        category_tags = await self.db.get_project_category_tags(
            project_id, organization_id
        )
        return [TagResponse.model_validate(tag) for tag in category_tags]
