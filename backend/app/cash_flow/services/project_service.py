from typing import Dict, List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import ProjectOperations
from app.cash_flow.models import (
    ProjectTable,
    ProjectUpsert,
    ProjectResponse,
    DeleteRequest,
    DeleteProjectRequest,
    TagResponse,
    PurchaseOrderResponse,
    InvoiceResponse,
    MilestoneResponse,
    ProjectExpenseResponse,
)
from app.enums import EntityType
from app.cash_flow.services.tag_service import TagService
from app.cash_flow.services.project_utils import create_project_response


def _create_project_response(project: ProjectTable) -> ProjectResponse:
    """Helper function to create a ProjectResponse from a ProjectTable."""
    return create_project_response(project)


class ProjectService:
    def __init__(self, session: AsyncSession):
        self.db = ProjectOperations(session)
        self.tag_service = TagService(session)

    async def get_current_projects(self, organization_id: int) -> List[ProjectResponse]:
        """Get all current projects."""
        projects = await self.db.get_current_projects(organization_id)
        return [_create_project_response(project) for project in projects]

    async def add_current_project(
        self, project: ProjectUpsert, organization_id: int
    ) -> ProjectResponse:
        """Add a new current project."""
        project.organization_id = organization_id
        project.status = "current"
        project.archived = False
        project.active = True
        tag_ids = project.tag_ids or []
        db_project = ProjectTable.model_validate(
            project.model_dump(exclude={"tag_ids"})
        )
        created_project = await self.db.add_current_project(db_project)

        if tag_ids:
            await self.tag_service.set_entity_tags(
                entity_id=created_project.id,
                entity_type=EntityType.PROJECT,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_project = await self.db.get_project_by_id(
            created_project.id, organization_id
        )
        if not full_project:
            raise ValueError("Could not retrieve created project")

        return _create_project_response(full_project)

    async def update_current_project(
        self, project: ProjectUpsert, organization_id: int
    ) -> ProjectResponse:
        """Update an existing current project."""
        project.organization_id = organization_id
        project.status = "current"
        project.archived = False
        project.active = True
        tag_ids = project.tag_ids
        db_project = ProjectTable.model_validate(
            project.model_dump(exclude={"tag_ids"})
        )
        updated_project = await self.db.update_project(db_project)
        if not updated_project:
            raise ValueError(f"Project with ID {project.id} not found")

        if tag_ids is not None:
            await self.tag_service.set_entity_tags(
                entity_id=updated_project.id,
                entity_type=EntityType.PROJECT,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_project = await self.db.get_project_by_id(
            updated_project.id, organization_id
        )
        if not full_project:
            raise ValueError("Could not retrieve updated project")

        return _create_project_response(full_project)

    async def delete_current_project(
        self, delete_req: DeleteProjectRequest, organization_id: int
    ) -> List[ProjectResponse]:
        """Delete a current project."""
        deleted = await self.db.delete_project(delete_req.id, organization_id)
        if not deleted:
            raise ValueError(f"Project {delete_req.id} not found")
        return await self.get_current_projects(organization_id)

    async def get_anticipated_projects(
        self, organization_id: int
    ) -> Dict[str, List[ProjectResponse]]:
        """Get all anticipated projects and archived projects."""
        projects = await self.db.get_anticipated_projects(organization_id)
        archived = await self.db.get_archived_projects(organization_id)
        return {
            "projects": [_create_project_response(p) for p in projects],
            "archived": [_create_project_response(p) for p in archived],
        }

    async def add_anticipated_project(
        self, project: ProjectUpsert, organization_id: int
    ) -> ProjectResponse:
        """Add a new anticipated project."""
        project.organization_id = organization_id
        project.status = "anticipated"
        project.archived = False
        project.active = True
        tag_ids = project.tag_ids or []
        db_project = ProjectTable.model_validate(
            project.model_dump(exclude={"tag_ids"})
        )
        created_project = await self.db.add_anticipated_project(db_project)

        if tag_ids:
            await self.tag_service.set_entity_tags(
                entity_id=created_project.id,
                entity_type=EntityType.PROJECT,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_project = await self.db.get_project_by_id(
            created_project.id, organization_id
        )
        if not full_project:
            raise ValueError("Could not retrieve created project")

        return _create_project_response(full_project)

    async def update_anticipated_project(
        self, project: ProjectUpsert, organization_id: int
    ) -> ProjectResponse:
        """Update an existing anticipated project."""
        project.organization_id = organization_id
        project.status = "anticipated"
        project.archived = False
        project.active = True
        tag_ids = project.tag_ids
        db_project = ProjectTable.model_validate(
            project.model_dump(exclude={"tag_ids"})
        )
        updated_project = await self.db.update_project(db_project)
        if not updated_project:
            raise ValueError(f"Project with ID {project.id} not found")

        if tag_ids is not None:
            await self.tag_service.set_entity_tags(
                entity_id=updated_project.id,
                entity_type=EntityType.PROJECT,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_project = await self.db.get_project_by_id(
            updated_project.id, organization_id
        )
        if not full_project:
            raise ValueError("Could not retrieve updated project")

        return _create_project_response(full_project)

    async def delete_anticipated_project(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> Dict[str, List[ProjectResponse]]:
        """Delete an anticipated project."""
        deleted = await self.db.delete_project(delete_req.id, organization_id)
        if not deleted:
            raise ValueError(f"Project {delete_req.id} not found")
        return await self.get_anticipated_projects(organization_id)

    async def archive_project(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> Dict[str, List[ProjectResponse]]:
        """Archive a project."""
        archived = await self.db.archive_project(delete_req.id, organization_id)
        if not archived:
            raise ValueError(f"Project {delete_req.id} not found")
        return await self.get_anticipated_projects(organization_id)

    async def restore_project(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> Dict[str, List[ProjectResponse]]:
        """Restore an archived project."""
        restored = await self.db.restore_project(delete_req.id, organization_id)
        if not restored:
            raise ValueError(f"Project {delete_req.id} not found")
        return await self.get_anticipated_projects(organization_id)
