from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import (
    FixedExpenseOperations,
    MiscExpenseOperations,
    PayrollExpenseOperations,
)
from app.cash_flow.models import (
    FixedExpenseTable,
    MiscExpenseTable,
    PayrollExpenseTable,
    FixedExpenseUpsert,
    MiscExpenseUpsert,
    PayrollExpenseUpsert,
    FixedExpenseResponse,
    MiscExpenseResponse,
    PayrollExpenseResponse,
    DeleteRequest,
    TagResponse,
)
from app.enums import EntityType
from app.cash_flow.services.tag_service import TagService


class ExpenseService:
    def __init__(self, session: AsyncSession):
        self.fixed_db = FixedExpenseOperations(session)
        self.misc_db = MiscExpenseOperations(session)
        self.payroll_db = PayrollExpenseOperations(session)
        self.tag_service = TagService(session)

    # Fixed Expenses
    async def get_fixed_expenses(
        self, organization_id: int
    ) -> List[FixedExpenseResponse]:
        """Get all fixed expenses."""
        expenses = await self.fixed_db.get_fixed_expenses(organization_id)
        return [
            FixedExpenseResponse(
                **expense.model_dump(exclude={"tags"}),
                tags=[TagResponse.model_validate(tag) for tag in expense.tags],
            )
            for expense in expenses
        ]

    async def add_fixed_expense(
        self, expense: FixedExpenseUpsert, organization_id: int
    ) -> FixedExpenseResponse:
        """Add a new fixed expense."""
        expense.organization_id = organization_id
        tag_ids = expense.tag_ids or []
        db_expense = FixedExpenseTable.model_validate(
            expense.model_dump(exclude={"tag_ids"})
        )
        created_expense = await self.fixed_db.add_fixed_expense(db_expense)

        if tag_ids:
            await self.tag_service.set_entity_tags(
                entity_id=created_expense.id,
                entity_type=EntityType.FIXED_EXPENSE,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_expense = await self.fixed_db.get_fixed_expense_by_id(
            created_expense.id, organization_id
        )
        if not full_expense:
            raise ValueError("Could not retrieve created fixed expense")

        return FixedExpenseResponse(
            **full_expense.model_dump(exclude={"tags"}),
            tags=[TagResponse.model_validate(tag) for tag in full_expense.tags],
        )

    async def update_fixed_expense(
        self, expense: FixedExpenseUpsert, organization_id: int
    ) -> FixedExpenseResponse:
        """Update an existing fixed expense."""
        expense.organization_id = organization_id
        tag_ids = expense.tag_ids
        db_expense = FixedExpenseTable.model_validate(
            expense.model_dump(exclude={"tag_ids"}, exclude_unset=True)
        )
        updated_expense = await self.fixed_db.update_fixed_expense(db_expense)
        if not updated_expense:
            raise ValueError(f"Fixed expense with ID {expense.id} not found")

        if tag_ids is not None:
            await self.tag_service.set_entity_tags(
                entity_id=updated_expense.id,
                entity_type=EntityType.FIXED_EXPENSE,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_expense = await self.fixed_db.get_fixed_expense_by_id(
            updated_expense.id, organization_id
        )
        if not full_expense:
            raise ValueError("Could not retrieve updated fixed expense")

        return FixedExpenseResponse(
            **full_expense.model_dump(exclude={"tags"}),
            tags=[TagResponse.model_validate(tag) for tag in full_expense.tags],
        )

    async def delete_fixed_expense(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> List[FixedExpenseResponse]:
        """Delete a fixed expense."""
        deleted = await self.fixed_db.delete_fixed_expense(
            delete_req.id, organization_id
        )
        if not deleted:
            raise ValueError(f"Fixed expense with ID {delete_req.id} not found")
        return await self.get_fixed_expenses(organization_id)

    # Misc Expenses
    async def get_misc_expenses(
        self, organization_id: int
    ) -> List[MiscExpenseResponse]:
        """Get all miscellaneous expenses."""
        expenses = await self.misc_db.get_misc_expenses(organization_id)
        return [
            MiscExpenseResponse(
                **expense.model_dump(exclude={"tags"}),
                tags=[TagResponse.model_validate(tag) for tag in expense.tags],
            )
            for expense in expenses
        ]

    async def add_misc_expense(
        self, expense: MiscExpenseUpsert, organization_id: int
    ) -> MiscExpenseResponse:
        """Add a new miscellaneous expense."""
        expense.organization_id = organization_id
        tag_ids = expense.tag_ids or []
        db_expense = MiscExpenseTable.model_validate(
            expense.model_dump(exclude={"tag_ids"})
        )
        created_expense = await self.misc_db.add_misc_expense(db_expense)

        if tag_ids:
            await self.tag_service.set_entity_tags(
                entity_id=created_expense.id,
                entity_type=EntityType.MISC_EXPENSE,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_expense = await self.misc_db.get_misc_expense_by_id(
            created_expense.id, organization_id
        )
        if not full_expense:
            raise ValueError("Could not retrieve created misc expense")

        return MiscExpenseResponse(
            **full_expense.model_dump(exclude={"tags"}),
            tags=[TagResponse.model_validate(tag) for tag in full_expense.tags],
        )

    async def update_misc_expense(
        self, expense: MiscExpenseUpsert, organization_id: int
    ) -> MiscExpenseResponse:
        """Update an existing miscellaneous expense."""
        expense.organization_id = organization_id
        tag_ids = expense.tag_ids
        db_expense = MiscExpenseTable.model_validate(
            expense.model_dump(exclude={"tag_ids"}, exclude_unset=True)
        )
        updated_expense = await self.misc_db.update_misc_expense(db_expense)
        if not updated_expense:
            raise ValueError(f"Misc expense with ID {expense.id} not found")

        if tag_ids is not None:
            await self.tag_service.set_entity_tags(
                entity_id=updated_expense.id,
                entity_type=EntityType.MISC_EXPENSE,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_expense = await self.misc_db.get_misc_expense_by_id(
            updated_expense.id, organization_id
        )
        if not full_expense:
            raise ValueError("Could not retrieve updated misc expense")

        return MiscExpenseResponse(
            **full_expense.model_dump(exclude={"tags"}),
            tags=[TagResponse.model_validate(tag) for tag in full_expense.tags],
        )

    async def delete_misc_expense(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> List[MiscExpenseResponse]:
        """Delete a miscellaneous expense."""
        deleted = await self.misc_db.delete_misc_expense(delete_req.id, organization_id)
        if not deleted:
            raise ValueError(f"Misc expense with ID {delete_req.id} not found")
        return await self.get_misc_expenses(organization_id)

    # Payroll Expenses
    async def get_payroll_expenses(
        self, organization_id: int
    ) -> List[PayrollExpenseResponse]:
        """Get all payroll expenses."""
        expenses = await self.payroll_db.get_payroll_expenses(organization_id)
        return [
            PayrollExpenseResponse(
                **expense.model_dump(exclude={"tags"}),
                tags=[TagResponse.model_validate(tag) for tag in expense.tags],
            )
            for expense in expenses
        ]

    async def add_payroll_expense(
        self, expense: PayrollExpenseUpsert, organization_id: int
    ) -> PayrollExpenseResponse:
        """Add a new payroll expense."""
        expense.organization_id = organization_id
        tag_ids = expense.tag_ids or []
        db_expense = PayrollExpenseTable.model_validate(
            expense.model_dump(exclude={"tag_ids"})
        )
        created_expense = await self.payroll_db.add_payroll_expense(db_expense)

        if tag_ids:
            await self.tag_service.set_entity_tags(
                entity_id=created_expense.id,
                entity_type=EntityType.PAYROLL_EXPENSE,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_expense = await self.payroll_db.get_payroll_expense_by_id(
            created_expense.id, organization_id
        )
        if not full_expense:
            raise ValueError("Could not retrieve created payroll expense")

        return PayrollExpenseResponse(
            **full_expense.model_dump(exclude={"tags"}),
            tags=[TagResponse.model_validate(tag) for tag in full_expense.tags],
        )

    async def update_payroll_expense(
        self, expense: PayrollExpenseUpsert, organization_id: int
    ) -> PayrollExpenseResponse:
        """Update an existing payroll expense."""
        expense.organization_id = organization_id
        tag_ids = expense.tag_ids
        db_expense = PayrollExpenseTable.model_validate(
            expense.model_dump(exclude={"tag_ids"}, exclude_unset=True)
        )
        updated_expense = await self.payroll_db.update_payroll_expense(db_expense)
        if not updated_expense:
            raise ValueError(f"Payroll expense with ID {expense.id} not found")

        if tag_ids is not None:
            await self.tag_service.set_entity_tags(
                entity_id=updated_expense.id,
                entity_type=EntityType.PAYROLL_EXPENSE,
                tag_ids=tag_ids,
                organization_id=organization_id,
            )

        full_expense = await self.payroll_db.get_payroll_expense_by_id(
            updated_expense.id, organization_id
        )
        if not full_expense:
            raise ValueError("Could not retrieve updated payroll expense")

        return PayrollExpenseResponse(
            **full_expense.model_dump(exclude={"tags"}),
            tags=[TagResponse.model_validate(tag) for tag in full_expense.tags],
        )

    async def delete_payroll_expense(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> List[PayrollExpenseResponse]:
        """Delete a payroll expense."""
        deleted = await self.payroll_db.delete_payroll_expense(
            delete_req.id, organization_id
        )
        if not deleted:
            raise ValueError(f"Payroll expense with ID {delete_req.id} not found")
        return await self.get_payroll_expenses(organization_id)
