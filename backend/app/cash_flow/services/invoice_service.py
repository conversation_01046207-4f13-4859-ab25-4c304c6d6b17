from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import InvoiceOperations
from app.cash_flow.models import (
    InvoiceTable,
    InvoiceUpsert,
    InvoiceResponse,
    DeleteRequest,
    TagResponse,
)
from app.enums import EntityType, TagRole
from app.cash_flow.services.tag_service import TagService


def _create_invoice_response(invoice: InvoiceTable) -> InvoiceResponse:
    return InvoiceResponse(
        **invoice.model_dump(exclude={"project", "purchase_order"}),
        project_name=invoice.project.name if invoice.project else "Unknown",
        po_number=invoice.po_number,
        category_tag=(
            TagResponse.model_validate(invoice.category_tag)
            if invoice.category_tag
            else None
        ),
        tags=[TagResponse.model_validate(tag) for tag in invoice.tags],
    )


class InvoiceService:
    def __init__(self, session: AsyncSession):
        self.db = InvoiceOperations(session)
        self.tag_service = TagService(session)
        self.session = session

    async def get_invoices(
        self, project_id: Optional[int] = None, organization_id: int = None
    ) -> List[InvoiceResponse]:
        """Get all invoices, optionally filtered by project_id."""
        invoices = await self.db.get_invoices(organization_id)
        if project_id is not None:
            invoices = [inv for inv in invoices if inv.project_id == project_id]
        return [_create_invoice_response(invoice) for invoice in invoices]

    async def check_invoice_exists(
        self,
        purchase_order_id: int,
        invoice_number: str,
        organization_id: int,
    ) -> bool:
        """Check if an invoice already exists for the given PO and pay date."""
        invoices = await self.db.get_invoices(organization_id)
        return any(
            inv.purchase_order_id == purchase_order_id
            and inv.invoice_number == invoice_number
            for inv in invoices
        )

    def _validate_invoice_association(self, invoice: InvoiceUpsert) -> None:
        """Validate that invoice has either expense category or purchase order, but not both."""
        has_category = invoice.category_tag_id is not None
        has_purchase_order = invoice.purchase_order_id is not None

        if not has_category and not has_purchase_order:
            raise ValueError(
                "Invoice must have either an expense category or a purchase order associated with it"
            )

        if has_category and has_purchase_order:
            raise ValueError(
                "Invoice cannot have both an expense category and a purchase order. Please choose one."
            )

    async def add_invoice(
        self, invoice: InvoiceUpsert, organization_id: int
    ) -> InvoiceResponse:
        """Add a new invoice."""
        # Validate invoice association
        self._validate_invoice_association(invoice)

        # Check for duplicate invoice if purchase order is set
        if invoice.purchase_order_id:
            if await self.check_invoice_exists(
                invoice.purchase_order_id,
                invoice.invoice_number,
                organization_id,
            ):
                raise ValueError(
                    f"Invoice already exists for PO ID {invoice.purchase_order_id} with invoice number {invoice.invoice_number}  "
                )

        invoice.organization_id = organization_id
        tag_ids = invoice.tag_ids or []
        category_tag_id = invoice.category_tag_id
        db_invoice = InvoiceTable.model_validate(
            invoice.model_dump(exclude={"tag_ids", "category_tag_id"})
        )

        created_invoice = await self.db.add_invoice(db_invoice)

        if tag_ids:
            await self.tag_service.set_entity_tags(
                entity_id=created_invoice.id,
                entity_type=EntityType.INVOICE,
                tag_ids=tag_ids,
                organization_id=organization_id,
                tag_role=TagRole.REGULAR,
            )

        if category_tag_id:
            await self.tag_service.set_entity_tags(
                entity_id=created_invoice.id,
                entity_type=EntityType.INVOICE,
                tag_ids=[category_tag_id],
                organization_id=organization_id,
                tag_role=TagRole.CATEGORY,
            )

        full_invoice = await self.db.get_invoice_by_id(
            created_invoice.id, organization_id
        )
        if not full_invoice:
            raise ValueError("Could not retrieve created invoice")
        return _create_invoice_response(full_invoice)

    async def update_invoice(
        self, invoice_update: InvoiceUpsert, organization_id: int
    ) -> InvoiceResponse:
        """Update an existing invoice."""
        # Validate invoice association
        self._validate_invoice_association(invoice_update)

        current_invoice = await self.db.get_invoice_by_id(
            invoice_update.id, organization_id
        )
        if not current_invoice:
            raise ValueError(f"Invoice with ID {invoice_update.id} not found")

        try:
            # Store tag information before updating
            tag_ids = invoice_update.tag_ids
            category_tag_id = invoice_update.category_tag_id

            # Create a new InvoiceTable with the updated data
            # Merge the update data with current data, using update data where not None
            update_data = invoice_update.model_dump(
                exclude={"tag_ids", "category_tag_id"}
            )
            current_data = current_invoice.model_dump()

            # Merge the data, preferring update_data where not None
            merged_data = {}
            for field in update_data:
                if update_data[field] is not None:
                    merged_data[field] = update_data[field]
                else:
                    merged_data[field] = current_data.get(field)

            # Ensure organization_id is set correctly
            merged_data["organization_id"] = organization_id

            # Create the updated invoice object
            db_invoice = InvoiceTable.model_validate(merged_data)

            updated_invoice = await self.db.update_invoice(db_invoice)
            if not updated_invoice:
                raise ValueError(f"Invoice with ID {invoice_update.id} not found")

            if tag_ids is not None:
                await self.tag_service.set_entity_tags(
                    entity_id=updated_invoice.id,
                    entity_type=EntityType.INVOICE,
                    tag_ids=tag_ids,
                    organization_id=organization_id,
                    tag_role=TagRole.REGULAR,
                )

            if category_tag_id is not None:
                await self.tag_service.set_entity_tags(
                    entity_id=updated_invoice.id,
                    entity_type=EntityType.INVOICE,
                    tag_ids=[category_tag_id] if category_tag_id else [],
                    organization_id=organization_id,
                    tag_role=TagRole.CATEGORY,
                )

            full_invoice = await self.db.get_invoice_by_id(
                updated_invoice.id, organization_id
            )
            if not full_invoice:
                raise ValueError("Could not retrieve updated invoice")
            return _create_invoice_response(full_invoice)
        except Exception as e:
            raise ValueError(f"Failed to update invoice: {str(e)}")

    async def delete_invoice(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> List[InvoiceResponse]:
        """Delete an invoice."""
        try:
            deleted = await self.db.delete_invoice(delete_req.id, organization_id)
            if not deleted:
                raise ValueError(f"Invoice with ID {delete_req.id} not found")
            return await self.get_invoices(organization_id=organization_id)
        except Exception as e:
            raise ValueError(f"Failed to delete invoice: {str(e)}")
