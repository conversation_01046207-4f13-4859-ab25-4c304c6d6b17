from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import AccountOperations
from app.cash_flow.models import (
    AccountTable,
    AccountUpsert,
    AccountResponse,
)


class AccountService:
    def __init__(self, session: AsyncSession):
        self.db = AccountOperations(session)

    async def get_accounts(self, organization_id: int) -> List[AccountResponse]:
        """Get all account balances."""
        accounts = await self.db.get_accounts(organization_id)
        return [AccountResponse.model_validate(account) for account in accounts]

    async def get_account_balance(
        self, account_id: int, organization_id: int
    ) -> Optional[AccountResponse]:
        """Get balance for a specific account."""
        account = await self.db.get_account_balance(account_id, organization_id)
        return AccountResponse.model_validate(account) if account else None

    async def update_accounts(
        self, account_data: AccountUpsert, organization_id: int
    ) -> AccountResponse:
        account_data.organization_id = organization_id
        account_data.balance = float(
            str(account_data.balance).replace("$", "").replace(",", "").strip()
        )
        db_account = AccountTable.model_validate(account_data)

        updated_account = await self.db.update_account(db_account)
        return AccountResponse.model_validate(updated_account)

    async def add_account(
        self, account_data: AccountUpsert, organization_id: int
    ) -> AccountResponse:
        account_data.organization_id = organization_id
        account_data.balance = float(
            str(account_data.balance).replace("$", "").replace(",", "").strip()
        )
        db_account = AccountTable.model_validate(account_data)
        created_account = await self.db.add_account(db_account)
        return AccountResponse.model_validate(created_account)

    async def create_default_accounts(
        self, organization_id: int
    ) -> List[AccountResponse]:
        """Create default accounts for an organization if they don't exist."""
        # Get existing accounts
        existing_accounts = await self.db.get_accounts(organization_id)
        existing_types = {account.account_type for account in existing_accounts}

        # Define default accounts
        default_accounts = [
            AccountUpsert(name="Checking", account_type="Checking", balance=0.0),
            AccountUpsert(name="Savings", account_type="Savings", balance=0.0),
            AccountUpsert(name="Credit Card", account_type="Credit Card", balance=0.0),
            AccountUpsert(
                name="Line of Credit", account_type="Line of Credit", balance=0.0
            ),
        ]

        # Create accounts that don't exist
        for account in default_accounts:
            if account.account_type not in existing_types:
                await self.add_account(account, organization_id)

        return await self.get_accounts(organization_id)
