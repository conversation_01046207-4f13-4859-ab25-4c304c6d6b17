from fastapi import APIRouter, Depends, Request
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import MilestoneUpsert, MilestoneResponse, DeleteRequest
from app.cash_flow.services import MilestoneService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/milestones", tags=["milestones"])


async def get_milestone_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> MilestoneService:
    return MilestoneService(db)


@router.get("", response_model=List[MilestoneResponse])
@handle_exceptions("Failed to get milestones")
async def get_milestones(
    request: Request,
    project_id: Optional[int] = None,
    service: MilestoneService = Depends(get_milestone_service),
):
    return await service.get_milestones(
        project_id, request.state.context.user.current_organization.id
    )


@router.post("", response_model=MilestoneResponse)
@handle_exceptions("Failed to add milestone")
async def add_milestone(
    milestone: MilestoneUpsert,
    request: Request,
    service: MilestoneService = Depends(get_milestone_service),
):
    return await service.add_milestone(
        milestone, request.state.context.user.current_organization.id
    )


@router.put("", response_model=MilestoneResponse)
@handle_exceptions("Failed to update milestone")
async def update_milestone(
    milestone: MilestoneUpsert,
    request: Request,
    service: MilestoneService = Depends(get_milestone_service),
):
    return await service.update_milestone(
        milestone, request.state.context.user.current_organization.id
    )


@router.delete("", response_model=List[MilestoneResponse])
@handle_exceptions("Failed to delete milestone")
async def delete_milestone(
    delete_req: DeleteRequest,
    request: Request,
    service: MilestoneService = Depends(get_milestone_service),
):
    return await service.delete_milestone(
        delete_req, request.state.context.user.current_organization.id
    )
