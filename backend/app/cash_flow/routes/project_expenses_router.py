from fastapi import APIRouter, Depends, Query, Request
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import (
    ProjectExpenseUpsert,
    ProjectExpenseResponse,
    DeleteRequest,
)
from app.cash_flow.services import ProjectExpenseService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/project-expenses", tags=["project_expenses"])


async def get_project_expense_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> ProjectExpenseService:
    return ProjectExpenseService(db)


@router.get("", response_model=List[ProjectExpenseResponse])
@handle_exceptions("Failed to get expenses")
async def get_project_expenses(
    request: Request,
    project_id: Optional[int] = Query(None),
    service: ProjectExpenseService = Depends(get_project_expense_service),
):
    return await service.get_project_expenses(
        project_id, request.state.context.user.current_organization.id
    )


@router.post("", response_model=ProjectExpenseResponse)
@handle_exceptions("Failed to add project expense")
async def add_project_expense(
    project_expense: ProjectExpenseUpsert,
    request: Request,
    service: ProjectExpenseService = Depends(get_project_expense_service),
):
    return await service.add_project_expense(
        project_expense,
        request.state.context.user.current_organization.id,
    )


@router.put("", response_model=ProjectExpenseResponse)
@handle_exceptions("Failed to update project expense")
async def update_project_expense(
    project_expense: ProjectExpenseUpsert,
    request: Request,
    service: ProjectExpenseService = Depends(get_project_expense_service),
):
    return await service.update_project_expense(
        project_expense,
        request.state.context.user.current_organization.id,
    )


@router.delete("", response_model=List[ProjectExpenseResponse])
@handle_exceptions("Failed to delete project expense")
async def delete_project_expense(
    delete_req: DeleteRequest,
    request: Request,
    service: ProjectExpenseService = Depends(get_project_expense_service),
):
    return await service.delete_project_expense(
        delete_req, request.state.context.user.current_organization.id
    )
