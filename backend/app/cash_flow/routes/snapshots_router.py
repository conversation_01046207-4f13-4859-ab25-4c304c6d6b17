from fastapi import APIRouter, Depends, Request
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import (
    SnapshotUpsert,
    SnapshotResponse,
    DeleteRequest,
)
from app.cash_flow.services import SnapshotService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="", tags=["snapshots"])


async def get_forecast_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> SnapshotService:
    return SnapshotService(db)


@router.post("/snapshots", response_model=SnapshotResponse)
@handle_exceptions("Failed to add snapshot")
async def add_snapshot(
    snapshot: SnapshotUpsert,
    request: Request,
    service: SnapshotService = Depends(get_forecast_service),
):
    return await service.save_snapshot(
        snapshot, request.state.context.user.current_organization.id
    )


@router.put("/snapshots", response_model=SnapshotResponse)
@handle_exceptions("Failed to update snapshot")
async def update_snapshot(
    snapshot: SnapshotUpsert,
    request: Request,
    service: SnapshotService = Depends(get_forecast_service),
):
    return await service.save_snapshot(
        snapshot, request.state.context.user.current_organization.id
    )


@router.delete("/snapshots")
@handle_exceptions("Failed to delete snapshot")
async def delete_snapshot(
    delete_req: DeleteRequest,
    request: Request,
    service: SnapshotService = Depends(get_forecast_service),
):
    await service.delete_snapshot(
        delete_req, request.state.context.user.current_organization.id
    )
    return {"message": "Snapshot deleted successfully"}


@router.get("/snapshots", response_model=List[SnapshotResponse])
@handle_exceptions("Failed to get forecast snapshots")
async def get_snapshots(
    request: Request,
    service: SnapshotService = Depends(get_forecast_service),
):
    return await service.get_snapshots(
        request.state.context.user.current_organization.id
    )
