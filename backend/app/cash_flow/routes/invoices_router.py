from fastapi import APIRouter, Depends, Request
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import InvoiceUpsert, InvoiceResponse, DeleteRequest
from app.cash_flow.services import InvoiceService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/invoices", tags=["invoices"])


async def get_invoice_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> InvoiceService:
    return InvoiceService(db)


@router.get("", response_model=List[InvoiceResponse])
@handle_exceptions("Failed to get invoices")
async def get_invoices(
    request: Request,
    project_id: Optional[int] = None,
    service: InvoiceService = Depends(get_invoice_service),
):
    return await service.get_invoices(
        project_id, request.state.context.user.current_organization.id
    )


@router.post("", response_model=InvoiceResponse)
@handle_exceptions("Failed to add invoice")
async def add_invoice(
    invoice: InvoiceUpsert,
    request: Request,
    service: InvoiceService = Depends(get_invoice_service),
):
    return await service.add_invoice(
        invoice, request.state.context.user.current_organization.id
    )


@router.put("", response_model=InvoiceResponse)
@handle_exceptions("Failed to update invoice")
async def update_invoice(
    invoice: InvoiceUpsert,
    request: Request,
    service: InvoiceService = Depends(get_invoice_service),
):
    return await service.update_invoice(
        invoice, request.state.context.user.current_organization.id
    )


@router.delete("", response_model=List[InvoiceResponse])
@handle_exceptions("Failed to delete invoice")
async def delete_invoice(
    delete_req: DeleteRequest,
    request: Request,
    service: InvoiceService = Depends(get_invoice_service),
):
    return await service.delete_invoice(
        delete_req, request.state.context.user.current_organization.id
    )
