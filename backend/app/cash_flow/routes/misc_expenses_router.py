from fastapi import APIRouter, Depends, Request
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import MiscExpenseUpsert, MiscExpenseResponse, DeleteRequest
from app.cash_flow.services import ExpenseService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/misc-expenses", tags=["misc_expenses"])


async def get_expense_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> ExpenseService:
    return ExpenseService(db)


@router.get("", response_model=List[MiscExpenseResponse])
@handle_exceptions("Failed to get misc expenses")
async def get_misc_expenses(
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.get_misc_expenses(
        request.state.context.user.current_organization.id
    )


@router.post("", response_model=MiscExpenseResponse)
@handle_exceptions("Failed to add misc expense")
async def add_misc_expense(
    expense: MiscExpenseUpsert,
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.add_misc_expense(
        expense, request.state.context.user.current_organization.id
    )


@router.put("", response_model=MiscExpenseResponse)
@handle_exceptions("Failed to update misc expense")
async def update_misc_expense(
    expense: MiscExpenseUpsert,
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.update_misc_expense(
        expense, request.state.context.user.current_organization.id
    )


@router.delete("", response_model=List[MiscExpenseResponse])
@handle_exceptions("Failed to delete misc expense")
async def delete_misc_expense(
    delete_req: DeleteRequest,
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.delete_misc_expense(
        delete_req, request.state.context.user.current_organization.id
    )
