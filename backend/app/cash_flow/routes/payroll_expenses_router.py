from fastapi import APIRouter, Depends, Request
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import (
    PayrollExpenseUpsert,
    PayrollExpenseResponse,
    DeleteRequest,
)
from app.cash_flow.services import ExpenseService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/payroll-expenses", tags=["payroll_expenses"])


async def get_expense_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> ExpenseService:
    return ExpenseService(db)


@router.get("", response_model=List[PayrollExpenseResponse])
@handle_exceptions("Failed to get payroll expenses")
async def get_payroll_expenses(
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.get_payroll_expenses(
        request.state.context.user.current_organization.id
    )


@router.post("", response_model=PayrollExpenseResponse)
@handle_exceptions("Failed to add payroll expense")
async def add_payroll_expense(
    expense: PayrollExpenseUpsert,
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.add_payroll_expense(
        expense, request.state.context.user.current_organization.id
    )


@router.put("", response_model=PayrollExpenseResponse)
@handle_exceptions("Failed to update payroll expense")
async def update_payroll_expense(
    expense: PayrollExpenseUpsert,
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.update_payroll_expense(
        expense, request.state.context.user.current_organization.id
    )


@router.delete("", response_model=List[PayrollExpenseResponse])
@handle_exceptions("Failed to delete payroll expense")
async def delete_payroll_expense(
    delete_req: DeleteRequest,
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.delete_payroll_expense(
        delete_req, request.state.context.user.current_organization.id
    )
