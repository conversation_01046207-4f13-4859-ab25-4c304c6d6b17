from fastapi import APIRouter, Depends, HTTPException, Request
from typing import List, Dict, Union
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import (
    ProjectUpsert,
    ProjectResponse,
    DeleteRequest,
    DeleteProjectRequest,
)
from app.cash_flow.services import ProjectService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api


router = APIRouter(prefix="", tags=["projects"])


async def get_project_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> ProjectService:
    return ProjectService(db)


# Current Projects
@router.get("/current-projects", response_model=List[ProjectResponse])
@handle_exceptions("Failed to get current projects")
async def get_current_projects(
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.get_current_projects(
        request.state.context.user.current_organization.id
    )


@router.post("/current-projects", response_model=ProjectResponse)
@handle_exceptions("Failed to add current project")
async def add_current_project(
    project: ProjectUpsert,
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.add_current_project(
        project, request.state.context.user.current_organization.id
    )


@router.put("/current-projects", response_model=ProjectResponse)
@handle_exceptions("Failed to update current project")
async def update_current_project(
    project: ProjectUpsert,
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.update_current_project(
        project, request.state.context.user.current_organization.id
    )


@router.delete("/current-projects", response_model=List[ProjectResponse])
@handle_exceptions("Failed to delete current project")
async def delete_current_project(
    delete_req: DeleteProjectRequest,
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.delete_current_project(
        delete_req, request.state.context.user.current_organization.id
    )


# Anticipated Projects
@router.get("/anticipated-projects", response_model=Dict[str, List[ProjectResponse]])
@handle_exceptions("Failed to get anticipated projects")
async def get_anticipated_projects(
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.get_anticipated_projects(
        request.state.context.user.current_organization.id
    )


@router.post("/anticipated-projects", response_model=ProjectResponse)
@handle_exceptions("Failed to add anticipated project")
async def add_anticipated_project(
    project: ProjectUpsert,
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.add_anticipated_project(
        project, request.state.context.user.current_organization.id
    )


@router.put("/anticipated-projects", response_model=ProjectResponse)
@handle_exceptions("Failed to update anticipated project")
async def update_anticipated_project(
    project: ProjectUpsert,
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.update_anticipated_project(
        project, request.state.context.user.current_organization.id
    )


@router.delete("/anticipated-projects", response_model=Dict[str, List[ProjectResponse]])
@handle_exceptions("Failed to delete anticipated project")
async def delete_anticipated_project(
    delete_req: DeleteRequest,
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.delete_anticipated_project(
        delete_req, request.state.context.user.current_organization.id
    )


# Project Archive/Restore
@router.post("/archive-project", response_model=Dict[str, List[ProjectResponse]])
@handle_exceptions("Failed to archive project")
async def archive_project(
    delete_req: DeleteRequest,
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.archive_project(
        delete_req, request.state.context.user.current_organization.id
    )


@router.post("/restore-project", response_model=Dict[str, List[ProjectResponse]])
@handle_exceptions("Failed to restore project")
async def restore_project(
    delete_req: DeleteRequest,
    request: Request,
    service: ProjectService = Depends(get_project_service),
):
    return await service.restore_project(
        delete_req, request.state.context.user.current_organization.id
    )


@router.get("/purchase-order-projects")
async def api_purchase_order_projects(
    request: Request,
    service: ProjectService = Depends(get_project_service),
) -> List[Dict[str, Union[int, str]]]:
    """Get all projects available for purchase orders"""
    try:
        current_projects = await service.get_current_projects(
            request.state.context.user.current_organization.id
        )
        anticipated_projects = (
            await service.get_anticipated_projects(
                request.state.context.user.current_organization.id
            )
        )["projects"]
        projects = [
            {"id": proj.id, "name": proj.name}
            for proj in current_projects + anticipated_projects
        ]
        return projects
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get projects: {str(e)}")
