from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import (
    ForecastRequest,
    ForecastResponse,
)
from app.cash_flow.services import ForecastService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="", tags=["forecasts"])


async def get_forecast_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> ForecastService:
    return ForecastService(db)


@router.post("/forecast", response_model=ForecastResponse)
@handle_exceptions("Failed to generate forecast")
async def generate_forecast(
    forecast_req: ForecastRequest,
    request: Request,
    service: ForecastService = Depends(get_forecast_service),
):
    return await service.generate_forecast(
        forecast_req.start_date,
        forecast_req.end_date,
        request.state.context.user.current_organization.id,
    )
