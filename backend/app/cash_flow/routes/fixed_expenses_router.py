from fastapi import APIRouter, Depends, HTTPException, Request
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import FixedExpenseUpsert, FixedExpenseResponse, DeleteRequest
from app.cash_flow.services import ExpenseService
from app.common.utils.error_handling import handle_exceptions
from app.organizations.service import get_organization_by_subdomain
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/fixed-expenses", tags=["fixed_expenses"])


async def get_expense_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> ExpenseService:
    return ExpenseService(db)


async def get_organization_id(
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
) -> int:
    print(f"Requestsds: {request.state}")
    subdomain = request.state.context.subdomain
    organization = await get_organization_by_subdomain(db, subdomain)
    if not organization:
        raise HTTPException(status_code=404, detail="Organization not found")
    return organization.id


@router.get("", response_model=List[FixedExpenseResponse])
@handle_exceptions("Failed to get fixed expenses")
async def get_fixed_expenses(
    request: Request,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.get_fixed_expenses(
        request.state.context.user.current_organization.id
    )


@router.post("", response_model=FixedExpenseResponse)
@handle_exceptions("Failed to add fixed expense")
async def add_fixed_expense(
    request: Request,
    expense: FixedExpenseUpsert,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.add_fixed_expense(
        expense, request.state.context.user.current_organization.id
    )


@router.put("", response_model=FixedExpenseResponse)
@handle_exceptions("Failed to update fixed expense")
async def update_fixed_expense(
    request: Request,
    expense: FixedExpenseUpsert,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.update_fixed_expense(
        expense, request.state.context.user.current_organization.id
    )


@router.delete("", response_model=List[FixedExpenseResponse])
@handle_exceptions("Failed to delete fixed expense")
async def delete_fixed_expense(
    request: Request,
    delete_req: DeleteRequest,
    service: ExpenseService = Depends(get_expense_service),
):
    return await service.delete_fixed_expense(
        delete_req, request.state.context.user.current_organization.id
    )
