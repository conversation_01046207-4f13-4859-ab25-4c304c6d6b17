from fastapi import APIRouter, Depends, Request
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import (
    PurchaseOrderUpsert,
    PurchaseOrderResponse,
    DeletePurchaseOrderRequest,
)
from app.cash_flow.services import PurchaseOrderService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/purchase-orders", tags=["purchase_orders"])


async def get_purchase_order_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> PurchaseOrderService:
    return PurchaseOrderService(db)


@router.get("", response_model=List[PurchaseOrderResponse])
@handle_exceptions("Failed to get purchase orders")
async def get_purchase_orders(
    request: Request,
    project_id: Optional[int] = None,
    service: PurchaseOrderService = Depends(get_purchase_order_service),
):
    return await service.get_purchase_orders(
        project_id, request.state.context.user.current_organization.id
    )


@router.post("", response_model=PurchaseOrderResponse)
@handle_exceptions("Failed to add purchase order")
async def add_purchase_order(
    po: PurchaseOrderUpsert,
    request: Request,
    service: PurchaseOrderService = Depends(get_purchase_order_service),
):
    return await service.add_purchase_order(
        po, request.state.context.user.current_organization.id
    )


@router.put("", response_model=PurchaseOrderResponse)
@handle_exceptions("Failed to update purchase order")
async def update_purchase_order(
    po: PurchaseOrderUpsert,
    request: Request,
    service: PurchaseOrderService = Depends(get_purchase_order_service),
):
    return await service.update_purchase_order(
        po, request.state.context.user.current_organization.id
    )


@router.delete("", response_model=List[PurchaseOrderResponse])
@handle_exceptions("Failed to delete purchase order")
async def delete_purchase_order(
    delete_req: DeletePurchaseOrderRequest,
    request: Request,
    service: PurchaseOrderService = Depends(get_purchase_order_service),
):
    return await service.delete_purchase_order(
        delete_req, request.state.context.user.current_organization.id
    )
