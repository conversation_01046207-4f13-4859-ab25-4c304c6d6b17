from typing import List
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.cash_flow.models import (
    EntityType,
    TagUpsert,
    TagMappingUpsert,
    TagResponse,
    TagMappingResponse,
    EntityTagsResponse,
    DeleteRequest,
)
from app.cash_flow.services import TagService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/tags", tags=["tags"])


async def get_tag_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> TagService:
    return TagService(db)


@router.post("", response_model=TagResponse)
@handle_exceptions("Failed to create tag")
async def create_tag(
    tag: TagUpsert,
    request: Request,
    service: TagService = Depends(get_tag_service),
):
    """Create a new tag for the current user's organization."""
    return await service.create_tag(
        tag, request.state.context.user.current_organization.id
    )


@router.get("", response_model=List[TagResponse])
@handle_exceptions("Failed to get tags")
async def get_tags(
    request: Request,
    service: TagService = Depends(get_tag_service),
):
    """Get all tags for the current user's organization."""
    return await service.get_tags(request.state.context.user.current_organization.id)


@router.put("", response_model=TagResponse)
@handle_exceptions("Failed to update tag")
async def update_tag(
    tag_id: int,
    tag: TagUpsert,
    request: Request,
    service: TagService = Depends(get_tag_service),
):
    """Update a tag's name."""
    return await service.update_tag(
        tag_id, tag, request.state.context.user.current_organization.id
    )


@router.delete("")
@handle_exceptions("Failed to delete tag")
async def delete_tag(
    delete_req: DeleteRequest,
    request: Request,
    service: TagService = Depends(get_tag_service),
):
    """Delete a tag and all its mappings."""
    if await service.delete_tag(
        delete_req, request.state.context.user.current_organization.id
    ):
        return {"message": "Tag deleted successfully"}
    raise ValueError("Tag not found")


@router.post("/mappings", response_model=TagMappingResponse)
@handle_exceptions("Failed to add tag to entity")
async def add_tag_to_entity(
    mapping: TagMappingUpsert,
    request: Request,
    service: TagService = Depends(get_tag_service),
):
    """Add a tag to an entity."""
    return await service.add_tag_to_entity(
        mapping, request.state.context.user.current_organization.id
    )


@router.delete("/mappings/{tag_id}/{entity_type}/{entity_id}")
@handle_exceptions("Failed to remove tag from entity")
async def remove_tag_from_entity(
    tag_id: int,
    entity_type: EntityType,
    entity_id: int,
    service: TagService = Depends(get_tag_service),
):
    """Remove a tag from an entity."""
    if await service.remove_tag_from_entity(
        tag_id,
        entity_type,
        entity_id,
    ):
        return {"message": "Tag removed from entity successfully"}
    raise ValueError("Tag mapping not found")


@router.get("/entities/{entity_type}/{entity_id}", response_model=EntityTagsResponse)
@handle_exceptions("Failed to get entity tags")
async def get_entity_tags(
    entity_type: EntityType,
    entity_id: int,
    service: TagService = Depends(get_tag_service),
):
    """Get all tags for a specific entity."""
    return await service.get_entity_tags(entity_type, entity_id)


@router.get("/project-categories/{project_id}", response_model=List[TagResponse])
@handle_exceptions("Failed to get project category tags")
async def get_project_category_tags(
    project_id: int,
    request: Request,
    service: TagService = Depends(get_tag_service),
):
    """Get all category tags used by project expenses in a specific project."""
    return await service.get_project_category_tags(
        project_id, request.state.context.user.current_organization.id
    )
