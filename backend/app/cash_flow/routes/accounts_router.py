from fastapi import APIRouter, Depends, Request
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.models import AccountUpsert, AccountResponse
from app.cash_flow.services import AccountService
from app.common.utils.error_handling import handle_exceptions
from app.common.database import get_session_fast_api

router = APIRouter(prefix="/accounts", tags=["accounts"])


async def get_account_service(
    db: AsyncSession = Depends(get_session_fast_api),
) -> AccountService:
    return AccountService(db)


@router.get("", response_model=List[AccountResponse])
@handle_exceptions("Failed to get accounts")
async def get_accounts(
    request: Request,
    service: AccountService = Depends(get_account_service),
):
    return await service.get_accounts(
        request.state.context.user.current_organization.id
    )


@router.post("", response_model=AccountResponse)
@handle_exceptions("Failed to update accounts")
async def update_accounts(
    request: Request,
    accounts_data: AccountUpsert,
    service: AccountService = Depends(get_account_service),
):
    return await service.update_accounts(
        accounts_data, request.state.context.user.current_organization.id
    )
