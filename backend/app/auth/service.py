from datetime import datetime, timedelta
from typing import Optional
from jose import jwt
from passlib.context import CryptContext
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.auth.models import User, CurrentOrganizationUser, OrganizationUser
from app.organizations.models import Organization
from app.common.email import send_email
from app.common.email_templates import get_verification_email_template
import os
import secrets
from app.core.config import settings

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def authenticate_user(
    db: AsyncSession, email: str, password: str
) -> Optional[User]:
    statement = select(User).where(User.email == email)
    result = await db.execute(statement)
    user = result.scalar_one_or_none()
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
    statement = select(User).where(User.id == user_id)
    result = await db.execute(statement)
    return result.scalar_one_or_none()


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    statement = select(User).where(User.email == email)
    result = await db.execute(statement)
    return result.scalar_one_or_none()


def generate_verification_token() -> str:
    """Generate a secure random token for email verification."""
    return secrets.token_urlsafe(32)


async def send_verification_email(user: User) -> bool:
    """Send verification email to user."""
    try:
        # Create verification URL
        verification_url = (
            f"https://{settings.BASE_DOMAIN}/verify-email/{user.verification_token}"
        )

        # Get email template
        email_template = get_verification_email_template(verification_url)

        # Send email
        return await send_email(
            to_email=user.email,
            subject=email_template["subject"],
            html_content=email_template["html_content"],
            plain_text_content=email_template["plain_text_content"],
        )
    except Exception as e:
        print(f"Error sending verification email: {e}")
        return False


async def create_user(
    db: AsyncSession,
    email: str,
    password: str,
    first_name: str,
    last_name: str,
) -> User:
    """Create a new user with verification token."""
    hashed_password = pwd_context.hash(password)
    verification_token = generate_verification_token()
    verification_token_expires = datetime.utcnow() + timedelta(
        hours=settings.VERIFICATION_TOKEN_EXPIRE_HOURS
    )

    user = User(
        email=email,
        hashed_password=hashed_password,
        first_name=first_name,
        last_name=last_name,
        verification_token=verification_token,
        verification_token_expires=verification_token_expires,
    )
    db.add(user)
    await db.commit()
    await db.refresh(user)

    # Send verification email
    await send_verification_email(user)

    return user


async def verify_email(db: AsyncSession, token: str) -> Optional[User]:
    """Verify user's email using token."""
    result = await db.execute(
        select(User).where(
            User.verification_token == token,
            User.verification_token_expires > datetime.utcnow(),
            User.is_verified.is_(False),
        )
    )
    user = result.scalar_one_or_none()

    if user:
        user.is_verified = True
        user.verification_token = None
        user.verification_token_expires = None
        await db.commit()
        await db.refresh(user)

    return user


async def get_user_organizations(db: AsyncSession, user_id: int) -> list[Organization]:
    statement = (
        select(Organization)
        .join(OrganizationUser)
        .where(OrganizationUser.user_id == user_id)
    )
    result = await db.execute(statement)
    return result.scalars().all()


async def set_current_organization(
    db: AsyncSession, user_id: int, organization_id: int
) -> bool:
    # Upsert current organization
    current_org = CurrentOrganizationUser(
        user_id=user_id, organization_id=organization_id
    )
    await db.merge(current_org)
    await db.commit()
    return True


async def resend_verification_email(db: AsyncSession, email: str) -> Optional[User]:
    """Resend verification email to user."""
    user = await get_user_by_email(db, email)
    if not user or user.is_verified:
        return None

    # Generate new verification token
    user.verification_token = generate_verification_token()
    user.verification_token_expires = datetime.utcnow() + timedelta(
        hours=settings.VERIFICATION_TOKEN_EXPIRE_HOURS
    )
    await db.commit()
    await db.refresh(user)

    # Send new verification email
    await send_verification_email(user)
    return user
