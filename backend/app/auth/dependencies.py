from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession
from jose import JWTError, jwt
from app.common.database import get_session_fast_api
from app.auth.models import User, UserResponse
from app.organizations.service import (
    get_organization_by_subdomain,
)
from app.auth.service import get_user_by_email, set_current_organization, get_user_by_id
import os

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token", auto_error=False)


async def get_token_from_cookie(request: Request) -> str:
    # Log all request details
    print("Request headers:", dict(request.headers))
    print("Request cookies:", dict(request.cookies))
    print("Request host:", request.headers.get("host"))
    print("Request origin:", request.headers.get("origin"))

    # First try to get token from cookie
    token = request.cookies.get("token")
    print(f"Cookie token: {token}")  # Debug log
    if token:
        return token

    # If no cookie, try to get from Authorization header (for backward compatibility)
    auth_header = request.headers.get("Authorization", "")
    print(f"Auth header: {auth_header}")  # Debug log
    if auth_header.startswith("Bearer "):
        return auth_header.split(" ")[1]

    print("No token found in cookie or header")  # Debug log
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )


async def get_current_user(
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
) -> UserResponse:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        token = await get_token_from_cookie(request)
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user: User = await get_user_by_email(db, email)

    return UserResponse.model_validate(user)


async def validate_organization_access(
    request: Request,
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_session_fast_api),
) -> UserResponse:
    """
    Dependency function to validate that the current user has access to the organization
    specified by the subdomain in the request.
    """
    subdomain = request.headers.get("X-Organization-Subdomain")

    # Skip validation if no subdomain
    if not subdomain:
        return current_user

    # Verify organization exists
    organization = await get_organization_by_subdomain(db, subdomain)
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
        )

    # Check if user belongs to this organization
    if not any(org.id == organization.id for org in current_user.organizations):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User does not have access to this organization",
        )
    result_user = current_user
    if current_user.current_organization.id != organization.id:
        # Set current organization
        await set_current_organization(db, current_user.id, organization.id)
        # Reload user with relationships after setting current organization
        user: User = await get_user_by_id(db, current_user.id)
        result_user = UserResponse.model_validate(user)

    return result_user
