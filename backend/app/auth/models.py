from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship
from sqlalchemy import BigInteger
from pydantic import computed_field, EmailStr
from app.organizations.models import OrganizationResponse

if TYPE_CHECKING:
    from app.organizations.models import Organization


class UserBase(SQLModel):
    email: EmailStr
    first_name: str
    last_name: str
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)
    is_verified: bool = Field(default=False)
    verification_token: Optional[str] = Field(default=None)
    verification_token_expires: Optional[datetime] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class OrganizationUser(SQLModel, table=True):
    __tablename__ = "organization_users"
    user_id: int = Field(foreign_key="users.id", primary_key=True)
    organization_id: int = Field(foreign_key="organizations.id", primary_key=True)
    role: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Add relationships with explicit back_populates
    user: "User" = Relationship(back_populates="organization_users")
    organization: "Organization" = Relationship(
        back_populates="organization_users", sa_relationship_kwargs={"lazy": "selectin"}
    )


class CurrentOrganizationUser(SQLModel, table=True):
    __tablename__ = "current_organization_users"
    user_id: int = Field(foreign_key="users.id", primary_key=True)
    organization_id: int = Field(foreign_key="organizations.id")
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Add relationships with explicit back_populates
    user: "User" = Relationship(back_populates="current_organization_user")
    organization: "Organization" = Relationship(
        back_populates="current_organization_users",
        sa_relationship_kwargs={"lazy": "selectin"},
    )


class User(UserBase, table=True):
    __tablename__ = "users"
    id: Optional[int] = Field(default=None, primary_key=True, sa_type=BigInteger)
    hashed_password: str

    # Relationships with explicit back_populates
    organization_users: Optional[List["OrganizationUser"]] = Relationship(
        back_populates="user", sa_relationship_kwargs={"lazy": "selectin"}
    )
    current_organization_user: Optional["CurrentOrganizationUser"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"lazy": "selectin"}
    )


class UserResponse(UserBase):
    id: int
    organization_users: Optional[List["OrganizationUser"]]
    current_organization_user: Optional["CurrentOrganizationUser"]

    @computed_field
    def organizations(self) -> List["OrganizationResponse"]:
        return [org_user.organization for org_user in self.organization_users or []]

    @computed_field
    def current_organization(self) -> Optional["OrganizationResponse"]:
        return (
            self.current_organization_user.organization
            if self.current_organization_user
            else None
        )

    class Config:
        from_attributes = True
        json_encoders = {datetime: lambda v: v.isoformat()}
        exclude = {"organization_users", "current_organization_user"}


class UserCreate(UserBase):
    password: str
