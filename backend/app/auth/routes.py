from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from app.common.database import get_session_fast_api
from app.auth.service import (
    authenticate_user,
    create_access_token,
    create_user,
    get_user_by_email,
    set_current_organization,
    get_user_by_id,
    verify_email,
    resend_verification_email,
)
from app.auth.models import UserResponse, UserCreate
from app.organizations.service import get_organization_by_subdomain
import os
from app.common.domain import DomainUtils
from pydantic import BaseModel, EmailStr

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


async def get_token(request: Request) -> str:
    # First try to get token from Authorization header
    auth_header = request.headers.get("Authorization", "")
    if auth_header.startswith("Bearer "):
        token = auth_header.split(" ")[1]
        return token

    # If no header, try to get from form data
    form_data = await request.form()
    token = form_data.get("access_token", "")
    return token


@router.post("/token", response_model=UserResponse)
async def login_for_access_token(
    request: Request,
    response: Response,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_session_fast_api),
):
    # Extract subdomain from header
    subdomain = (
        request.headers.get("X-Organization-Subdomain")
        if request
        else form_data.client_id
    )

    print("Login request details:")
    print(f"Subdomain: {subdomain}")
    print(f"Origin: {request.headers.get('origin') if request else 'None'}")

    # Authenticate user first
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user is verified
    if not user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please verify your email address before logging in",
        )

    # Get the base domain from the origin for cookie setting
    origin = request.headers.get("origin", "") if request else ""
    base_domain = DomainUtils.get_base_domain(origin)
    print(f"Setting cookie for domain: {base_domain}")
    # if base domain contains localhost add a period remove domain restriction
    if "localhost" in base_domain:
        base_domain = "." + base_domain

    # If no subdomain provided, just return token without organization
    if not subdomain:
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email},
            expires_delta=access_token_expires,
        )
        # Set the token in a cookie
        response.set_cookie(
            key="token",
            value=access_token,
            httponly=True,
            secure=True,  # Set to False for localhost
            samesite="lax",  # Protect against CSRF
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # Convert minutes to seconds
            path="/",  # Ensure cookie is available for all paths
            domain=base_domain,  # Remove domain restriction for localhost
        )
        print("Setting cookie for base domain")
        return user

    # Verify organization exists
    organization = await get_organization_by_subdomain(db, subdomain)
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
        )

    # Set current organization
    await set_current_organization(db, user.id, organization.id)

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email, "org_id": organization.id},
        expires_delta=access_token_expires,
    )
    # Set the token in a cookie
    response.set_cookie(
        key="token",
        value=access_token,
        httponly=True,
        secure=True,  # Set to False for localhost
        samesite="lax",  # Protect against CSRF
        max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # Convert minutes to seconds
        path="/",  # Ensure cookie is available for all paths
        domain=base_domain,  # Remove domain restriction for localhost
    )
    print("Setting cookie for subdomain")
    user = await get_user_by_id(db, user.id)
    return user


@router.post("/logout")
async def logout(response: Response, request: Request):
    origin = request.headers.get("origin", "")
    base_domain = DomainUtils.get_base_domain(origin)
    # Clear the token cookie
    response.delete_cookie(
        key="token",
        domain=f".{base_domain}",  # Match the domain used when setting the cookie
    )
    return {"message": "Successfully logged out"}


@router.post("/register", response_model=UserResponse)
async def register_user(
    user_data: UserCreate, db: AsyncSession = Depends(get_session_fast_api)
):
    # Check if user already exists
    if await get_user_by_email(db, user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered"
        )

    # Create new user
    user = await create_user(
        db,
        email=user_data.email,
        password=user_data.password,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
    )
    return user


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    request: Request,
    db: AsyncSession = Depends(get_session_fast_api),
):
    return request.state.context.user


@router.get("/verify-email/{token}")
async def verify_email_endpoint(
    token: str,
    db: AsyncSession = Depends(get_session_fast_api),
):
    user = await verify_email(db, token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token",
        )
    return {"message": "Email verified successfully"}


class ResendVerificationRequest(BaseModel):
    email: EmailStr


@router.post("/resend-verification")
async def resend_verification(
    request: ResendVerificationRequest,
    db: AsyncSession = Depends(get_session_fast_api),
):
    user = await resend_verification_email(db, request.email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User not found or already verified",
        )
    return {"message": "Verification email sent successfully"}
