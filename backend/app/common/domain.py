from app.core.config import settings


class DomainUtils:
    @staticmethod
    def is_base_domain(host: str) -> bool:
        base_domain = DomainUtils.get_base_domain(host)
        """Check if the host is the base domain."""
        return base_domain == settings.BASE_DOMAIN

    @staticmethod
    def get_configured_base_domain() -> str:
        """Get the base domain from the config."""
        return settings.BASE_DOMAIN

    @staticmethod
    def get_base_domain(host: str) -> str:
        """Get the base domain from a host string.

        Args:
            host: The host string (e.g., 'sub.example.com' or 'example.com')

        Returns:
            The base domain (e.g., 'example.com')
        """
        # hand if host is empty, blank string or None
        if not host:
            return None

        # Remove protocol if present
        if "://" in host:
            host = host.split("://")[1]

        # Remove any path and query parameters
        host = host.split("/")[0]

        # Split by dots and get all parts
        parts = host.split(".")

        # If we have less than 2 parts, return the host as is
        if len(parts) < 2:
            return host

        # Return the last two parts (domain and TLD)
        return ".".join(parts[-2:])
