import os
from typing import Optional
from sendgrid import SendGridAP<PERSON>lient
from sendgrid.helpers.mail import (
    Mail,
    Email,
    To,
    HtmlContent,
    PlainTextContent,
)
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from .email_templates import get_invitation_email_template
from app.core.config import settings

# Initialize SendGrid client if in production
if settings.ENVIRONMENT != "development":
    sg = SendGridAPIClient(settings.SENDGRID_API_KEY)


async def send_email(
    to_email: str,
    subject: str,
    html_content: str,
    plain_text_content: Optional[str] = None,
    from_email: Optional[str] = None,
) -> bool:
    """
    Send an email using either SendGrid (production), Mailtrap (staging), or MailHog (local development).

    Args:
        to_email: Recipient email address
        subject: Email subject
        html_content: HTML content of the email
        plain_text_content: Optional plain text content of the email
        from_email: Optional sender email address

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        from_email = from_email or settings.SENDGRID_FROM_EMAIL
        if not from_email:
            raise ValueError("SENDGRID_FROM_EMAIL environment variable is not set")

        if settings.ENVIRONMENT == "development":
            # Use MailHog in local development
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = from_email
            msg["To"] = to_email

            # Attach both HTML and plain text versions
            msg.attach(MIMEText(plain_text_content or html_content, "plain"))
            msg.attach(MIMEText(html_content, "html"))

            # Connect to MailHog
            with smtplib.SMTP(
                os.getenv("MAILHOG_HOST", "localhost"),
                int(os.getenv("MAILHOG_PORT", "1025")),
            ) as server:
                server.send_message(msg)

            print(f"Development email sent to {to_email}")
            print("View in MailHog: http://localhost:8025")
            return True

        elif settings.ENVIRONMENT == "staging":
            # Use Mailtrap in staging
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = from_email
            msg["To"] = to_email

            msg.attach(MIMEText(plain_text_content or html_content, "plain"))
            msg.attach(MIMEText(html_content, "html"))

            with smtplib.SMTP(
                os.getenv("MAILTRAP_HOST", "sandbox.smtp.mailtrap.io"),
                int(os.getenv("MAILTRAP_PORT", "2525")),
            ) as server:
                server.starttls()
                server.login(
                    os.getenv("MAILTRAP_USERNAME"), os.getenv("MAILTRAP_PASSWORD")
                )
                server.send_message(msg)

            print(f"Staging email sent to {to_email}")
            print(
                f"View in Mailtrap: https://mailtrap.io/inboxes/{os.getenv('MAILTRAP_INBOX_ID')}/messages"
            )
            return True

        else:
            # Use SendGrid in production
            mail = Mail(
                from_email=Email(from_email),
                to_emails=To(to_email),
                subject=subject,
                html_content=HtmlContent(html_content),
                plain_text_content=(
                    PlainTextContent(plain_text_content) if plain_text_content else None
                ),
            )

            response = sg.send(mail)
            return response.status_code == 202

    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False


async def send_invitation_email(
    to_email: str, inviter_name: str, organization_name: str, invite_url: str
) -> bool:
    """
    Send an organization invitation email.

    Args:
        to_email: Recipient email address
        inviter_name: Name of the person sending the invitation
        organization_name: Name of the organization
        invite_url: URL to accept/decline the invitation

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    template = get_invitation_email_template(
        inviter_name, organization_name, invite_url
    )

    return await send_email(
        to_email=to_email,
        subject=template["subject"],
        html_content=template["html_content"],
        plain_text_content=template["plain_text_content"],
    )
