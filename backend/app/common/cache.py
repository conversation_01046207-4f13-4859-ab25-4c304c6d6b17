from typing import <PERSON><PERSON><PERSON>, Optional, Dict, Any, Union
from cachetools import <PERSON><PERSON><PERSON><PERSON>, LR<PERSON>ache, LFUCache, RRCache
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

T = TypeVar("T")

# Default TTL in seconds (1 hour)
DEFAULT_TTL = 3600


class CachePolicy(Enum):
    """Cache eviction policies."""

    LRU = "lru"  # Least Recently Used
    TTL = "ttl"  # Time To Live
    LFU = "lfu"  # Least Frequently Used
    RR = "rr"  # Random Replacement


@dataclass
class CacheStats:
    """Statistics for a cache instance."""

    hits: int = 0
    misses: int = 0
    evictions: int = 0
    last_cleared: Optional[datetime] = None
    created_at: datetime = datetime.utcnow()
    size: int = 0


class CacheConfig:
    """Configuration for a cache instance."""

    def __init__(
        self,
        name: str,
        capacity: int,
        policy: CachePolicy = CachePolicy.LRU,
        ttl_seconds: Optional[int] = None,
        track_stats: bool = True,
    ):
        self.name = name
        self.capacity = capacity
        self.policy = policy
        # For LRU policy, always use TTL with default if not specified
        if policy == CachePolicy.LRU:
            self.ttl_seconds = ttl_seconds or DEFAULT_TTL
        else:
            self.ttl_seconds = ttl_seconds
        self.track_stats = track_stats


class BaseCache:
    """Base class for all cache implementations."""

    def __init__(self, config: CacheConfig):
        self.config = config
        self.stats = CacheStats() if config.track_stats else None
        self._cache = self._create_cache()

    def _create_cache(self) -> Union[TTLCache, LRUCache, LFUCache, RRCache]:
        """Create the appropriate cache instance based on configuration."""
        if self.config.policy == CachePolicy.TTL and self.config.ttl_seconds:
            return TTLCache(maxsize=self.config.capacity, ttl=self.config.ttl_seconds)
        elif self.config.policy == CachePolicy.LFU:
            return LFUCache(maxsize=self.config.capacity)
        elif self.config.policy == CachePolicy.RR:
            return RRCache(maxsize=self.config.capacity)
        else:
            # For LRU, always use TTLCache with the configured TTL
            return TTLCache(maxsize=self.config.capacity, ttl=self.config.ttl_seconds)

    def get(self, key: str) -> Optional[Any]:
        """Get a value from the cache."""
        value = self._cache.get(key)
        if value is None:
            if self.stats:
                self.stats.misses += 1
            return None
        if self.stats:
            self.stats.hits += 1
        return value

    def put(self, key: str, value: Any) -> None:
        """Put a value in the cache."""
        self._cache[key] = value

    def clear(self) -> None:
        """Clear all items from the cache."""
        self._cache.clear()
        if self.stats:
            self.stats.last_cleared = datetime.utcnow()
            self.stats.size = 0

    def get_stats(self) -> Optional[CacheStats]:
        """Get cache statistics if tracking is enabled."""
        if not self.stats:
            return None

        # Update current size
        self.stats.size = len(self._cache)
        return self.stats

    def __len__(self) -> int:
        """Return the current number of items in the cache."""
        return len(self._cache)

    def __contains__(self, key: str) -> bool:
        """Check if a key exists in the cache."""
        return key in self._cache


class CacheManager:
    """
    A manager class to handle multiple caches in the application.
    Provides a centralized way to create and access different caches.
    """

    _instance = None
    _caches: Dict[str, BaseCache] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(CacheManager, cls).__new__(cls)
        return cls._instance

    @classmethod
    def create_cache(
        cls,
        name: str,
        capacity: int,
        policy: CachePolicy = CachePolicy.LRU,
        ttl_seconds: Optional[int] = None,
        track_stats: bool = True,
    ) -> BaseCache:
        """
        Create a new cache with the specified configuration.

        Args:
            name: Unique name for the cache
            capacity: Maximum number of items the cache can hold
            policy: Cache eviction policy
            ttl_seconds: Time to live in seconds for cache entries
            track_stats: Whether to track cache statistics

        Returns:
            The created cache instance

        Note:
            If a cache with the same name already exists, it will be returned
            instead of creating a new one.
        """
        # If cache exists, return it
        if name in cls._caches:
            return cls._caches[name]

        config = CacheConfig(
            name=name,
            capacity=capacity,
            policy=policy,
            ttl_seconds=ttl_seconds,
            track_stats=track_stats,
        )

        cache = BaseCache(config)
        cls._caches[name] = cache
        return cache

    @classmethod
    def get_cache(cls, name: str) -> Optional[BaseCache]:
        """
        Get an existing cache by name.

        Args:
            name: Name of the cache to retrieve

        Returns:
            The cache instance if found, None otherwise
        """
        return cls._caches.get(name)

    @classmethod
    def clear_all(cls) -> None:
        """Clear all caches in the manager."""
        for cache in cls._caches.values():
            cache.clear()

    @classmethod
    def remove_cache(cls, name: str) -> None:
        """
        Remove a cache by name.

        Args:
            name: Name of the cache to remove
        """
        if name in cls._caches:
            del cls._caches[name]

    @classmethod
    def get_cache_names(cls) -> list[str]:
        """Get a list of all cache names."""
        return list(cls._caches.keys())

    @classmethod
    def get_cache_stats(cls, name: str) -> Optional[CacheStats]:
        """
        Get statistics for a specific cache.

        Args:
            name: Name of the cache to get stats for

        Returns:
            CacheStats if tracking is enabled, None otherwise
        """
        cache = cls.get_cache(name)
        if cache:
            return cache.get_stats()
        return None

    @classmethod
    def get_all_cache_stats(cls) -> Dict[str, Optional[CacheStats]]:
        """
        Get statistics for all caches.

        Returns:
            Dictionary mapping cache names to their statistics
        """
        return {name: cache.get_stats() for name, cache in cls._caches.items()}
