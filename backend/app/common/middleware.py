from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse, FileResponse
from datetime import datetime
from typing import Optional, Callable, List
from dataclasses import dataclass
from app.common.cache import <PERSON>ache<PERSON>anager
from app.common.database import get_session
from sqlalchemy.ext.asyncio import AsyncSession
from urllib.parse import urlparse
from starlette.middleware.base import BaseHTTPMiddleware
from app.auth.dependencies import get_current_user, validate_organization_access
from app.auth.models import UserResponse
import time
import uuid
import os
from jose import JWTError
from app.auth.models import OrganizationResponse
import logging
import sys

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stdout,
)
logger = logging.getLogger(__name__)

# Public endpoints that don't require authentication
PUBLIC_ENDPOINTS = [
    "/api/auth/token",
    "/api/auth/register",
    "/docs",
    "/openapi.json",
    "/api/health",
    "/api/auth/verify-email",
    "/api/auth/resend-verification",
]


def is_public_endpoint(url_path: str) -> bool:
    """Check if the given URL path contains any of the public paths.

    Args:
        url_path: The URL path to check

    Returns:
        bool: True if the URL path contains any of the public paths, False otherwise
    """
    return any(path in url_path for path in PUBLIC_ENDPOINTS)


@dataclass
class RequestContext:
    """Stores request-specific context data."""

    request_id: str
    start_time: datetime
    user: Optional[UserResponse] = None
    current_organization_id: Optional[str] = None
    subdomain: Optional[str] = None
    cache: Optional[CacheManager] = None
    db_session: Optional[AsyncSession] = None
    processing_time: Optional[float] = None
    current_organization: Optional[OrganizationResponse] = None
    organizations: Optional[List[OrganizationResponse]] = None


class RequestContextMiddleware(BaseHTTPMiddleware):
    """Middleware to manage request context and timing."""

    async def dispatch(self, request: Request, call_next: Callable):
        # Initialize request context
        print("Initializing request context")
        request.state.context = RequestContext(
            request_id=str(uuid.uuid4()), start_time=datetime.utcnow()
        )

        try:
            # Add request ID to response headers
            response = await call_next(request)
            response.headers["X-Request-ID"] = request.state.context.request_id

            # Calculate processing time
            request.state.context.processing_time = (
                time.time() - request.state.context.start_time.timestamp()
            )
            response.headers["X-Processing-Time"] = str(
                request.state.context.processing_time
            )

            return response

        finally:
            print("Cleaning up request context")


class DatabaseSessionMiddleware(BaseHTTPMiddleware):
    """Middleware to manage database sessions."""

    async def dispatch(self, request: Request, call_next: Callable):
        # Create a database session for the request
        async with get_session() as session:
            request.state.context.db_session = session
            return await call_next(request)


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Middleware to handle authentication."""

    async def dispatch(self, request: Request, call_next: Callable):
        # Skip authentication for public endpoints
        if is_public_endpoint(request.url.path):
            return await call_next(request)

        try:
            # Get token from cookie or header
            token = request.cookies.get("token")
            if not token:
                auth_header = request.headers.get("Authorization", "")
                if auth_header.startswith("Bearer "):
                    token = auth_header.split(" ")[1]

            if not token:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Not authenticated",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # Get user from database
            user = await get_current_user(request, request.state.context.db_session)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # Store user in request context
            request.state.context.user = user
            return await call_next(request)

        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )


class OrganizationValidationMiddleware(BaseHTTPMiddleware):
    """Middleware to validate organization access."""

    async def dispatch(self, request: Request, call_next: Callable):
        # Skip validation for public endpoints
        if is_public_endpoint(request.url.path):
            return await call_next(request)

        # Get organization from header
        subdomain = request.headers.get("X-Organization-Subdomain")
        if not subdomain:
            return await call_next(request)

        # Validate organization access
        user = await validate_organization_access(
            request, request.state.context.user, request.state.context.db_session
        )
        if not user:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User does not have access to this organization",
            )

        request.state.context.subdomain = subdomain

        return await call_next(request)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log request details."""

    async def dispatch(self, request: Request, call_next: Callable):
        print("Logging request details")
        # Log request details
        print(f"Request: {request.method} {request.url}")

        if request.url.path not in [
            "/api/auth/me",
            "/api/auth/token",
            "/api/auth/register",
            "/docs",
            "/openapi.json",
        ] and not request.url.path.startswith("/api/organizations/invites/"):
            print(f"Request ID: {request.state.context.request_id}")
            print(
                f"Organization: {request.state.context.user.current_organization.id if request.state.context.user and request.state.context.user.current_organization else request.state.context.subdomain}"
            )
            print(
                f"User: {request.state.context.user.email if request.state.context.user else None}"
            )
        else:
            print("No request context available")

        response = await call_next(request)

        # Log response details
        print(f"Response Status: {response.status_code}")
        print(f"Processing Time: {request.state.context.processing_time}")
        print("Finished logging request details")
        return response


class CORSValidationMiddleware(BaseHTTPMiddleware):
    """Middleware to validate CORS origins and set headers."""

    def __init__(self, app, base_domain: str):
        super().__init__(app)
        self.base_domain = base_domain

    async def dispatch(self, request: Request, call_next: Callable):
        origin = request.headers.get("origin")

        # If no origin (same-origin request), proceed
        if not origin:
            return await call_next(request)

        # Parse origin to extract domain and handle ports
        parsed_origin = urlparse(origin)
        hostname = parsed_origin.hostname

        # Check if hostname matches BASE_DOMAIN or is a subdomain
        is_allowed = (
            hostname == self.base_domain  # Exact match with BASE_DOMAIN
            or hostname.endswith(f".{self.base_domain}")  # Subdomain of BASE_DOMAIN
        )
        if not is_allowed:
            raise HTTPException(status_code=403, detail="Not allowed origin")

        # Process request and set specific CORS headers
        response = await call_next(request)
        cors_headers = {
            "Access-Control-Allow-Origin": origin,
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Allow-Headers": "Authorization, Content-Type, X-Organization-Subdomain, X-Organization-Id, Accept",
            "Access-Control-Expose-Headers": "Set-Cookie",
        }
        response.headers.update(cors_headers)
        return response


class FrontendRoutingMiddleware(BaseHTTPMiddleware):
    """Middleware to handle frontend routing."""

    async def dispatch(self, request: Request, call_next: Callable):
        if request.url.path.startswith(("/api")) or request.url.path in [
            "/docs",
            "/openapi.json",
        ]:
            return await call_next(request)
        if os.getenv("ENVIRONMENT") != "production":
            return JSONResponse(
                content={
                    "message": "Frontend is served separately in development mode. Please access it at https://cashflow.app"
                }
            )
        return FileResponse("frontend/build/index.html")


class ExceptionLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log all exceptions that occur during request processing."""

    async def dispatch(self, request: Request, call_next: Callable):
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Get request context
            request_id = getattr(request.state, "context", None)
            request_id = request_id.request_id if request_id else "N/A"

            # Log request details
            logger.error(f"Request ID: {request_id}")
            logger.error(f"Method: {request.method}")
            logger.error(f"URL: {request.url}")
            logger.error(f"Headers: {dict(request.headers)}")

            # Log the full exception with traceback
            logger.error(f"Exception during request processing: {str(e)}")
            logger.error("Traceback:", exc_info=True)

            # Handle different types of exceptions
            if isinstance(e, HTTPException):
                # FastAPI's HTTPException
                return JSONResponse(
                    status_code=e.status_code, content={"detail": e.detail}
                )
            elif isinstance(e, ValueError):
                # Validation errors
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST, content={"detail": str(e)}
                )
            elif isinstance(e, JWTError):
                # JWT authentication errors
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Invalid authentication credentials"},
                )
            else:
                # Unhandled exceptions
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={"detail": "Internal server error"},
                )
