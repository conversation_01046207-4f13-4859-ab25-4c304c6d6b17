from functools import wraps
from fastapi import HTTPException
from typing import Type, Callable, TypeVar, ParamSpec, Dict, Tuple


P = ParamSpec("P")
T = TypeVar("T")


def handle_exceptions(
    error_message: str,
    value_error_message: str = "Invalid input provided",
    value_error_status: int = 400,
    general_error_status: int = 500,
    extra_exceptions: Dict[Type[Exception], Tuple[int, str]] = None,
) -> Callable[[Callable[P, T]], Callable[P, T]]:
    """
    Decorator to handle exceptions in route handlers with logging.

    Args:
        error_message: Message for general exceptions
        value_error_message: Message for ValueError exceptions (default: generic message)
        value_error_status: HTTP status code for ValueError (default: 400)
        general_error_status: HTTP status code for general exceptions (default: 500)
        extra_exceptions: Optional dict mapping exception types to (status_code, message)
    """

    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @wraps(func)
        async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            try:
                return await func(*args, **kwargs)
            except ValueError as e:
                message = str(e) or value_error_message
                print(f"ValueError: {message}")
                raise HTTPException(status_code=value_error_status, detail=message)
            except Exception as e:
                print(f"Exception: {e}")
                if extra_exceptions and type(e) in extra_exceptions:
                    status, msg = extra_exceptions[type(e)]
                    raise HTTPException(status_code=status, detail=msg)
                raise HTTPException(
                    status_code=general_error_status,
                    detail=f"{error_message}: {str(e)}",
                )

        return wrapper

    return decorator
