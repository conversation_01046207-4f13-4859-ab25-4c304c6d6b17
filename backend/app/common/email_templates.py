from typing import Dict, Any
from app.core.config import settings


def get_invitation_email_template(
    inviter_name: str, organization_name: str, invite_url: str
) -> Dict[str, Any]:
    """Generate the invitation email template."""
    return {
        "subject": f"Exciting News: You're Invited to Join {organization_name} on {settings.COMPANY_NAME}!",
        "html_content": f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>🎉 You're Invited to Join {organization_name}! 🎉</h2>
                <p>Hello there!</p>
                <p>Great news! {inviter_name} has invited you to join {organization_name} on {settings.COMPANY_NAME} - and we couldn't be more excited to have you on board!</p>
                <p>By joining {organization_name}, you'll be able to:</p>
                <ul style="color: #333;">
                    <li>Collaborate seamlessly with your team members</li>
                    <li>Access powerful tools and resources</li>
                    <li>Stay connected and productive with your organization</li>
                </ul>
                <p>Ready to get started? Click the button below to accept your invitation:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{invite_url}" 
                       style="background-color: #1890ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">
                        Join {organization_name} Now
                    </a>
                </div>
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">{invite_url}</p>
                <p><em>This invitation will expire in 7 days, so don't wait too long!</em></p>
                <p>We're looking forward to having you join us!</p>
                <p>Best regards,<br>{settings.COMPANY_NAME} Team</p>
            </div>
        """,
        "plain_text_content": f"""
            🎉 You're Invited to Join {organization_name}! 🎉

            Hello there!

            Great news! {inviter_name} has invited you to join {organization_name} on {settings.COMPANY_NAME} - and we couldn't be more excited to have you on board!

            By joining {organization_name}, you'll be able to:
            * Collaborate seamlessly with your team members
            * Access powerful tools and resources
            * Stay connected and productive with your organization

            Ready to get started? Click the link below to accept your invitation:
            {invite_url}

            This invitation will expire in 7 days, so don't wait too long!

            We're looking forward to having you join us!

            Best regards,
            {settings.COMPANY_NAME} Team
        """,
    }


def get_verification_email_template(verification_url: str) -> Dict[str, Any]:
    """Generate the email verification template."""
    return {
        "subject": f"Welcome to {settings.COMPANY_NAME} - Let's Get You Started!",
        "html_content": f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>🎉 Welcome to {settings.COMPANY_NAME}! 🎉</h2>
                <p>Hello there!</p>
                <p>We're thrilled to have you join {settings.COMPANY_NAME}! You're just one step away from unlocking all the amazing features we have to offer.</p>
                <p>To get started and access your account, please verify your email address by clicking the button below:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{verification_url}" 
                       style="background-color: #1890ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">
                        Verify My Email
                    </a>
                </div>
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">{verification_url}</p>
                <p><em>This verification link will expire in {settings.VERIFICATION_TOKEN_EXPIRE_HOURS} hours, so don't wait too long!</em></p>
                <p>Once verified, you'll be able to:</p>
                <ul style="color: #333;">
                    <li>Access your personalized dashboard</li>
                    <li>Connect with your team members</li>
                    <li>Start using all our powerful features</li>
                </ul>
                <p>We can't wait to see what you'll accomplish with {settings.COMPANY_NAME}!</p>
                <p>Best regards,<br>{settings.COMPANY_NAME} Team</p>
            </div>
        """,
        "plain_text_content": f"""
            🎉 Welcome to {settings.COMPANY_NAME}! 🎉

            Hello there!

            We're thrilled to have you join {settings.COMPANY_NAME}! You're just one step away from unlocking all the amazing features we have to offer.

            To get started and access your account, please verify your email address by clicking the link below:
            {verification_url}

            This verification link will expire in {settings.VERIFICATION_TOKEN_EXPIRE_HOURS} hours, so don't wait too long!

            Once verified, you'll be able to:
            * Access your personalized dashboard
            * Connect with your team members
            * Start using all our powerful features

            We can't wait to see what you'll accomplish with {settings.COMPANY_NAME}!

            Best regards,
            {settings.COMPANY_NAME} Team
        """,
    }
