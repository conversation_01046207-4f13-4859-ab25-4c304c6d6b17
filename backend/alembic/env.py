from logging.config import fileConfig
from sqlalchemy import engine_from_config, text
from sqlalchemy import pool
from alembic import context
from sqlmodel import SQLModel

# Import all models so Alembic can see them
from app.auth.models import User, OrganizationUser, CurrentOrganizationUser
from app.organizations.models import Organization, Invitation
from app.cash_flow.models.account import AccountTable
from app.cash_flow.models.fixed_expense import FixedExpenseTable
from app.cash_flow.models.misc_expense import MiscExpenseTable
from app.cash_flow.models.payroll_expense import PayrollExpenseTable
from app.cash_flow.models.project import ProjectTable
from app.cash_flow.models.milestone import MilestoneTable
from app.cash_flow.models.project_expense import ProjectExpenseTable
from app.cash_flow.models.purchase_order import PurchaseOrderTable
from app.cash_flow.models.invoice import InvoiceTable
from app.cash_flow.models.snapshot import SnapshotTable
from app.cash_flow.models.tag import TagTable, TagMappingTable

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = SQLModel.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        connection.execute(text("SET search_path TO public"))
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()
        connection.commit()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
