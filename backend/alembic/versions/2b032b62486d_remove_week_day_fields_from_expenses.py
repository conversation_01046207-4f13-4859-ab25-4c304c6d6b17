"""remove_week_day_fields_from_expenses

Revision ID: 2b032b62486d
Revises: 4711329a885d
Create Date: 2025-07-16 21:13:18.610309

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = "2b032b62486d"
down_revision: Union[str, None] = "4711329a885d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Remove week_of_month and day_of_week columns from payroll_expenses table
    op.drop_column("payroll_expenses", "week_of_month")
    op.drop_column("payroll_expenses", "day_of_week")

    # Remove week_of_month and day_of_week columns from fixed_expenses table
    op.drop_column("fixed_expenses", "week_of_month")
    op.drop_column("fixed_expenses", "day_of_week")


def downgrade() -> None:
    # Add back week_of_month and day_of_week columns to payroll_expenses table
    op.add_column(
        "payroll_expenses", sa.Column("week_of_month", sa.Integer(), nullable=True)
    )
    op.add_column(
        "payroll_expenses", sa.Column("day_of_week", sa.Integer(), nullable=True)
    )

    # Add back week_of_month and day_of_week columns to fixed_expenses table
    op.add_column(
        "fixed_expenses", sa.Column("week_of_month", sa.Integer(), nullable=True)
    )
    op.add_column(
        "fixed_expenses", sa.Column("day_of_week", sa.Integer(), nullable=True)
    )
