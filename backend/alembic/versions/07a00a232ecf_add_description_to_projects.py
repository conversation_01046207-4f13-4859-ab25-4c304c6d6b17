"""add_description_to_projects

Revision ID: 07a00a232ecf
Revises: 9c73ac668442
Create Date: 2025-06-24 16:39:02.014450

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "07a00a232ecf"
down_revision: Union[str, None] = "9c73ac668442"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add description column to projects table
    op.add_column("projects", sa.Column("description", sa.String(), nullable=True))


def downgrade() -> None:
    # Remove description column from projects table
    op.drop_column("projects", "description")
