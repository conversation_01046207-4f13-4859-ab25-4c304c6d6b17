"""remove_week_day_fields_from_expenses

Revision ID: 4711329a885d
Revises: 0b1b828e2139
Create Date: 2025-07-16 21:12:00.982779

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '4711329a885d'
down_revision: Union[str, None] = '0b1b828e2139'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass 