"""rename_category_to_description_in_fixed_expenses

Revision ID: 67de68ffeabf
Revises: 07a00a232ecf
Create Date: 2025-06-24 16:45:05.099602

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "67de68ffeabf"
down_revision: Union[str, None] = "07a00a232ecf"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Rename category column to description in fixed_expenses table
    op.alter_column("fixed_expenses", "category", new_column_name="description")


def downgrade() -> None:
    # Rename description column back to category in fixed_expenses table
    op.alter_column("fixed_expenses", "description", new_column_name="category")
