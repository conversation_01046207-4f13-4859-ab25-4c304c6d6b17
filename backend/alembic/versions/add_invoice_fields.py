"""add invoice fields

Revision ID: add_invoice_fields
Revises: eae02940a84a
Create Date: 2024-12-19 10:00:00.000000

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "add_invoice_fields"
down_revision = "eae02940a84a"
branch_labels = None
depends_on = None


def upgrade():
    # Add invoice_number and description columns to invoices table
    op.add_column("invoices", sa.Column("invoice_number", sa.String(), nullable=True))
    op.add_column("invoices", sa.Column("description", sa.String(), nullable=True))


def downgrade():
    # Remove the columns in reverse order
    op.drop_column("invoices", "description")
    op.drop_column("invoices", "invoice_number")
