"""add_verification_token_fields

Revision ID: b91daf04b5cf
Revises: e1d2904a8e49
Create Date: 2024-03-19 10:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "b91daf04b5cf"
down_revision: Union[str, None] = "e1d2904a8e49"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add verification token fields to users table
    op.add_column(
        "users",
        sa.Column("is_verified", sa.<PERSON>an(), nullable=False, server_default="false"),
    )
    op.add_column("users", sa.Column("verification_token", sa.String(), nullable=True))
    op.add_column(
        "users",
        sa.Column(
            "verification_token_expires", sa.DateTime(timezone=True), nullable=True
        ),
    )


def downgrade() -> None:
    # Remove verification token fields from users table
    op.drop_column("users", "verification_token_expires")
    op.drop_column("users", "verification_token")
    op.drop_column("users", "is_verified")
