"""add tag system

Revision ID: add_tag_system
Revises:
Create Date: 2024-03-19 10:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "add_tag_system"
down_revision = "b91daf04b5cf"
branch_labels = None
depends_on = None


def upgrade():
    # Create tags table
    op.create_table(
        "tags",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("organization_id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "organization_id", name="uix_tag_name_org"),
    )

    # Create tag_mappings table
    op.create_table(
        "tag_mappings",
        sa.Column("tag_id", sa.BigInteger(), nullable=False),
        sa.Column(
            "entity_type",
            postgresql.ENUM(
                "fixed_expense",
                "invoice",
                "misc_expense",
                "payroll_expense",
                "project",
                "project_expense",
                "purchase_order",
                "milestone",
                name="entitytype",
            ),
            nullable=False,
        ),
        sa.Column("entity_id", sa.BigInteger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["tag_id"],
            ["tags.id"],
        ),
        sa.PrimaryKeyConstraint("tag_id", "entity_type", "entity_id"),
        sa.UniqueConstraint(
            "tag_id", "entity_type", "entity_id", name="uix_tag_mapping"
        ),
    )

    op.alter_column(
        "invoices", "purchase_order_id", existing_type=sa.BIGINT(), nullable=True
    )
    op.alter_column("users", "first_name", existing_type=sa.VARCHAR(), nullable=False)
    op.alter_column("users", "last_name", existing_type=sa.VARCHAR(), nullable=False)
    op.alter_column(
        "users",
        "verification_token_expires",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        type_=sa.DateTime(),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "users",
        "verification_token_expires",
        existing_type=sa.DateTime(),
        type_=postgresql.TIMESTAMP(timezone=True),
        existing_nullable=True,
    )
    op.alter_column("users", "last_name", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column("users", "first_name", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column(
        "invoices", "purchase_order_id", existing_type=sa.BIGINT(), nullable=False
    )

    # Drop tag_mappings table
    op.drop_table("tag_mappings")

    # Drop tags table
    op.drop_table("tags")

    # Drop entity_type enum
    op.execute("DROP TYPE entitytype")
