"""add_tag_role_to_tag_mappings

Revision ID: eae02940a84a
Revises: e8942fa30004
Create Date: 2025-06-24 12:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "eae02940a84a"
down_revision: Union[str, None] = "add_tag_system"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create the tagrole enum type first
    op.execute("CREATE TYPE tagrole AS ENUM ('regular', 'category')")

    # Add tag_role column to tag_mappings table
    op.add_column(
        "tag_mappings",
        sa.Column(
            "tag_role",
            sa.Enum("regular", "category", name="tagrole"),
            nullable=False,
            server_default="regular",
        ),
    )


def downgrade() -> None:
    # Remove tag_role column from tag_mappings table
    op.drop_column("tag_mappings", "tag_role")
    # Drop the enum type
    op.execute("DROP TYPE tagrole")
