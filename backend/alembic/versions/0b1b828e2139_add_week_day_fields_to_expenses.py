"""add_week_day_fields_to_expenses

Revision ID: 0b1b828e2139
Revises: 302bd981ea00
Create Date: 2025-07-16 17:21:29.697251

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "0b1b828e2139"
down_revision: Union[str, None] = "302bd981ea00"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add week_of_month and day_of_week columns to payroll_expenses table
    op.add_column(
        "payroll_expenses", sa.Column("week_of_month", sa.Integer(), nullable=True)
    )
    op.add_column(
        "payroll_expenses", sa.Column("day_of_week", sa.Integer(), nullable=True)
    )

    # Add week_of_month and day_of_week columns to fixed_expenses table
    op.add_column(
        "fixed_expenses", sa.Column("week_of_month", sa.Integer(), nullable=True)
    )
    op.add_column(
        "fixed_expenses", sa.Column("day_of_week", sa.Integer(), nullable=True)
    )

    # Set default values for existing monthly and biweekly records (week 1, Friday=4)
    # Update payroll_expenses
    op.execute(
        """
        UPDATE payroll_expenses
        SET week_of_month = 1, day_of_week = 4
        WHERE recurrence IN ('Monthly', 'monthly', 'Bi-weekly', 'bi-weekly', 'biweekly', 'Biweekly')
    """
    )

    # Update fixed_expenses
    op.execute(
        """
        UPDATE fixed_expenses
        SET week_of_month = 1, day_of_week = 4
        WHERE recurrence IN ('Monthly', 'monthly', 'Bi-weekly', 'bi-weekly', 'biweekly', 'Biweekly')
    """
    )


def downgrade() -> None:
    # Remove the new columns
    op.drop_column("fixed_expenses", "day_of_week")
    op.drop_column("fixed_expenses", "week_of_month")
    op.drop_column("payroll_expenses", "day_of_week")
    op.drop_column("payroll_expenses", "week_of_month")
