"""add description to purchase orders

Revision ID: 9c73ac668442
Revises: add_invoice_fields
Create Date: 2024-12-19 10:00:00.000000

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "9c73ac668442"
down_revision = "add_invoice_fields"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add description column to purchase_orders table
    op.add_column(
        "purchase_orders", sa.Column("description", sa.String(), nullable=True)
    )


def downgrade() -> None:
    # Remove description column from purchase_orders table
    op.drop_column("purchase_orders", "description")
