"""add_due_date_to_purchase_orders

Revision ID: 75c7333d90e6
Revises: 67de68ffeabf
Create Date: 2025-06-25 12:18:18.793450

"""

from typing import Sequence, Union
from datetime import datetime, timedelta
import re

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "75c7333d90e6"
down_revision: Union[str, None] = "67de68ffeabf"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def calculate_due_date(issue_date: datetime, lead_time: int, terms: str) -> datetime:
    """
    Calculate the due date based on issue date, lead time, and payment terms.

    Args:
        issue_date: The date the purchase order was issued
        lead_time: Lead time in weeks
        terms: Payment terms (e.g., "100% due upon receipt", "Net 30")

    Returns:
        The calculated due date
    """
    # Start with issue date + lead time weeks
    due_date = issue_date + timedelta(weeks=lead_time)

    # Add additional days based on payment terms
    if terms == "100% due upon receipt":
        # No additional days
        pass
    elif terms == "Net 30":
        due_date += timedelta(days=30)
    elif terms == "Net 60":
        due_date += timedelta(days=60)
    elif terms == "Net 90":
        due_date += timedelta(days=90)
    else:
        # Try to extract number from terms like "Net 45" or "45 days"
        numbers = re.findall(r"\d+", terms)
        if numbers:
            due_date += timedelta(days=int(numbers[0]))

    return due_date


def upgrade() -> None:
    # Add due_date column to purchase_orders table
    op.add_column(
        "purchase_orders", sa.Column("due_date", sa.DateTime(), nullable=True)
    )

    # Calculate due dates for existing purchase orders
    connection = op.get_bind()

    # Get all existing purchase orders
    result = connection.execute(
        sa.text(
            "SELECT id, issue_date, lead_time, terms FROM purchase_orders WHERE due_date IS NULL"
        )
    )

    for row in result:
        po_id, issue_date, lead_time, terms = row

        if issue_date and lead_time and terms:
            try:
                # Calculate due date
                due_date = calculate_due_date(issue_date, lead_time, terms)

                # Update the record
                connection.execute(
                    sa.text(
                        "UPDATE purchase_orders SET due_date = :due_date WHERE id = :id"
                    ),
                    {"due_date": due_date, "id": po_id},
                )
            except Exception as e:
                # Log error but continue with other records
                print(f"Error calculating due date for PO {po_id}: {e}")


def downgrade() -> None:
    # Remove due_date column from purchase_orders table
    op.drop_column("purchase_orders", "due_date")
