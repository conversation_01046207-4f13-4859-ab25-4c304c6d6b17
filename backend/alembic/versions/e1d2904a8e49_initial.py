"""initial

Revision ID: e1d2904a8e49
Revises:
Create Date: 2025-05-05 13:53:04.969388

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "e1d2904a8e49"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with open("alembic/initial.sql") as file:
        op.execute(file.read())
    # ### end Alembic commands ###


def downgrade() -> None:
    # Drop tables in reverse order of creation to handle foreign key dependencies
    op.execute("DROP TABLE IF EXISTS snapshots CASCADE")
    op.execute("DROP TABLE IF EXISTS purchase_orders CASCADE")
    op.execute("DROP TABLE IF EXISTS projects CASCADE")
    op.execute("DROP TABLE IF EXISTS payroll_expenses CASCADE")
    op.execute("DROP TABLE IF EXISTS organizations CASCADE")
    op.execute("DROP TABLE IF EXISTS organization_users CASCADE")
    op.execute("DROP TABLE IF EXISTS misc_expenses CASCADE")
    op.execute("DROP TABLE IF EXISTS milestones CASCADE")
    op.execute("DROP TABLE IF EXISTS invoices CASCADE")
    op.execute("DROP TABLE IF EXISTS invitations CASCADE")
    op.execute("DROP TABLE IF EXISTS project_expenses CASCADE")
    op.execute("DROP TABLE IF EXISTS fixed_expenses CASCADE")
    op.execute("DROP TABLE IF EXISTS current_organization_users CASCADE")
    op.execute("DROP TABLE IF EXISTS accounts CASCADE")
    op.execute("DROP TABLE IF EXISTS users CASCADE")
    # ### end Alembic commands ###
