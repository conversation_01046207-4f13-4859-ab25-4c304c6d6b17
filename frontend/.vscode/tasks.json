{"version": "2.0.0", "tasks": [{"label": "frontend: start", "type": "npm", "script": "start", "isBackground": true, "presentation": {"reveal": "silent", "close": true}, "problemMatcher": [{"owner": "npm", "pattern": {"regexp": ""}, "background": {"activeOnStart": true, "beginsPattern": {"regexp": "^.*Starting.*"}, "endsPattern": {"regexp": "^.*Compiled.*"}}}]}, {"label": "frontend: terminate", "command": "echo ${input:terminate}", "type": "shell", "problemMatcher": []}], "inputs": [{"id": "terminate", "type": "command", "command": "workbench.action.tasks.terminate", "args": "terminateAll"}]}