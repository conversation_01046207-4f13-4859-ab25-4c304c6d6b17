# Cashflow Frontend

A React-based frontend for the Cashflow application, built with TypeScript.

## Features

- Dashboard with financial overview
- Account management
- Fixed and miscellaneous expense tracking
- Project and payroll management
- Financial forecasting

## Technology Stack

- React 18
- TypeScript
- Ant Design UI library
- Chart.js for data visualization
- Axios for API communication

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm (v8+)

### Installation

1. Clone the repository
2. Navigate to the frontend directory:
   ```
   cd frontend
   ```
3. Install dependencies:
   ```
   npm install
   ```
4. Start the development server:
   ```
   npm start
   ```

The application will be available at http://localhost:3000.

## TypeScript Implementation

This project has been fully converted to TypeScript for improved type safety and developer experience. All components, services, and utilities are now type-safe.

### Type Definitions

Common types are defined in `src/types/index.ts`. These include:

- `Account` - For bank and financial accounts
- `FixedExpense` - For recurring expenses
- `MiscExpense` - For one-time expenses
- `ForecastSnapshot` - For forecast data
- `DashboardData` - For dashboard statistics

### Adding New Types

To add new types:

1. Define the interface in `src/types/index.ts`
2. Import and use the type in your components

Example:

```typescript
// In types/index.ts
export interface NewType {
  id: string | number;
  name: string;
  value: number;
}

// In your component
import { NewType } from '../types';

const MyComponent: React.FC = () => {
  const [items, setItems] = useState<NewType[]>([]);
  // ...
};
```

### API Service

The API service in `src/services/api.ts` uses TypeScript to provide type safety for API calls. When adding new API endpoints, make sure to:

1. Define the appropriate return type
2. Use proper parameter types

## Development Guidelines

- Use TypeScript for all new components and files
- Add appropriate type definitions for props, state, and function parameters
- Use React.FC type for functional components
- Use interfaces for complex object types

## Building for Production

To build the application for production:

```
npm run build
```

This will create an optimized production build in the `build` directory.

## License

This project is licensed under the MIT License.
