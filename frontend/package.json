{"name": "cashflow-frontend", "version": "0.1.0", "private": true, "description": "React frontend for Cashflow application", "main": "index.js", "scripts": {"start": "BROWSER=none craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ant-design/icons": "^5.2.6", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.31", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "antd": "^5.13.0", "axios": "^1.4.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.26.0", "@craco/craco": "^7.1.0", "@types/lodash": "^4.17.16", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}