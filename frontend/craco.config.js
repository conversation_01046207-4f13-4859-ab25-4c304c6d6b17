const fs = require('fs');
const path = require('path');

module.exports = {
  devServer: {
    host: '0.0.0.0',
    port: 3000,
    allowedHosts: 'all',
    webSocketServer: {
      options: {
        path: '/ws',
        port: 3000,
        host: 'cashflow.app'
      }
    },
    client: {
      webSocketURL: {
        hostname: 'cashflow.app',
        pathname: '/ws',
        port: 443,
        protocol: 'wss'
      }
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, X-Organization-Subdomain, X-Organization-Id'
    }
  }
}; 