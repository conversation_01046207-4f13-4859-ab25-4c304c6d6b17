// Frontend configuration
interface Config {
  protocol: string;
  baseDomain: string;
  environment: string;
  port: string;
  apiPort: string;
  isProduction: boolean;
  isDevelopment: boolean;
}

export const config: Config = {
  protocol: process.env.REACT_APP_PROTOCOL || 'http',
  baseDomain: process.env.REACT_APP_BASE_DOMAIN || 'localhost',
  environment: process.env.NODE_ENV || 'development',
  port: process.env.REACT_APP_PORT || '',
  apiPort: process.env.REACT_APP_API_PORT || ':8000',
  isProduction: process.env.NODE_ENV === 'production',
  isDevelopment: process.env.NODE_ENV === 'development',
};

// Log config in development
// if (config.isDevelopment) {
//   console.log('Frontend Config:', {
//     protocol: config.protocol,
//     baseDomain: config.baseDomain,
//     environment: config.environment,
//     port: config.port,
//     apiPort: config.apiPort,
//     isProduction: config.isProduction,
//     isDevelopment: config.isDevelopment,
//   });
// }

// Validate required environment variables in production
if (config.isProduction) {
  const requiredVars = ['REACT_APP_PROTOCOL', 'REACT_APP_BASE_DOMAIN'];
  const missing = requiredVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables in production: ${missing.join(', ')}`);
  }
}
