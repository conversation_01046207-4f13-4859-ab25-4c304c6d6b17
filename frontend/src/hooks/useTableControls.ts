import { TableProps } from 'antd';
import { useState } from 'react';

type FilterType = Record<string, any>;
type SorterType = Record<string, any>;

interface TableControls {
  filteredInfo: FilterType;
  sortedInfo: SorterType;
  handleTableChange: TableProps<any>['onChange'];
  clearFiltersAndSorting: () => void;
}

interface UseTableControlsOptions {
  initialSort?: {
    columnKey: string;
    order: 'ascend' | 'descend';
  };
}

export const useTableControls = (options?: UseTableControlsOptions): TableControls => {
  const [filteredInfo, setFilteredInfo] = useState<FilterType>({});
  const [sortedInfo, setSortedInfo] = useState<SorterType>(
    options?.initialSort
      ? {
          columnKey: options.initialSort.columnKey,
          order: options.initialSort.order,
        }
      : {}
  );

  const handleTableChange: TableProps<any>['onChange'] = (_pagination, filters, sorter) => {
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  };

  const clearFiltersAndSorting = () => {
    setFilteredInfo({});
    setSortedInfo(
      options?.initialSort
        ? {
            columnKey: options.initialSort.columnKey,
            order: options.initialSort.order,
          }
        : {}
    );
  };

  return {
    filteredInfo,
    sortedInfo,
    handleTableChange,
    clearFiltersAndSorting,
  };
};
