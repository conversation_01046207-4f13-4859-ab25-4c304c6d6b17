.logo {
  height: 32px;
  margin: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.site-layout-background {
  background: #fff;
}

.ant-layout-sider {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.ant-layout-header {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.ant-table-row-selected > td {
  background-color: #e6f7ff !important;
}

/* Make cards in the home page have equal height */
.ant-card {
  height: 100%;
  transition: all 0.3s;
}

.ant-card-hoverable:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Style for form elements */
.ant-form-item-label > label {
  font-weight: 500;
}

/* Style for buttons */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* Style for tables */
.ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

/* Style for statistics */
.ant-statistic-title {
  font-size: 16px;
  font-weight: 500;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ant-layout-content {
    margin: 0 8px;
  }

  .site-layout-background {
    padding: 16px;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
