import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useOrganization } from '../contexts/OrganizationContext';
import { Layout, Typography, Card, Button, Row, Col, Space } from 'antd';
import { DomainUtils } from '../utils/domain';
const { Content } = Layout;
const { Title, Text } = Typography;

export const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { organization, organizations } = useOrganization();

  return (
    <Content style={{ padding: '24px', maxWidth: 1200, margin: '0 auto' }}>
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card>
            <Space style={{ width: '100%', justifyContent: 'space-between' }}>
              <Space direction='vertical'>
                <Title level={2} style={{ margin: 0 }}>
                  Welcome, {user?.first_name} {user?.last_name}
                </Title>
                <Text type='secondary'>Current Organization: {organization?.name}</Text>
              </Space>
              <Button onClick={logout}>Logout</Button>
            </Space>
          </Card>
        </Col>

        <Col span={24}>
          <Card title='Your Organizations'>
            <Row gutter={[16, 16]}>
              {organizations.map(org => (
                <Col xs={24} sm={12} md={8} key={org.id}>
                  <Card
                    style={{
                      backgroundColor: org.id === organization?.id ? '#e6f7ff' : 'white',
                    }}
                  >
                    <Title level={5} style={{ margin: 0 }}>
                      {org.name}
                    </Title>
                    <Text type='secondary'>
                      {org.subdomain}.{DomainUtils.getConfiguredBaseDomain()}
                    </Text>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>
    </Content>
  );
};
