import { Card, Typography } from 'antd';
import React from 'react';
import { formatCurrency } from '../utils/formatting';

interface CategorySummaryProps {
  categoryName: string;
  projectExpenseTotal: number;
  purchaseOrderTotal: number;
  invoiceTotal: number;
}

const CategorySummary: React.FC<CategorySummaryProps> = ({
  categoryName,
  projectExpenseTotal,
  purchaseOrderTotal,
  invoiceTotal,
}) => {
  // Only show if there are any values (excluding invoices since they're not displayed)
  if (projectExpenseTotal === 0 && purchaseOrderTotal === 0) {
    return null;
  }

  return (
    <Card
      size='small'
      style={{
        marginBottom: '2px',
        backgroundColor: 'rgba(24, 144, 255, 0.05)',
        cursor: 'default',
        height: 'auto',
        minHeight: '32px',
      }}
      styles={{
        body: {
          padding: '4px 8px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        },
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <span style={{ fontSize: '12px' }}>🏷️</span>
        <Typography.Text
          style={{
            fontSize: '12px',
            fontWeight: 500,
            color: '#1890ff',
          }}
        >
          {categoryName}
        </Typography.Text>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {/* Project Expense */}
        {projectExpenseTotal !== 0 && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
            <span style={{ fontSize: '12px', color: '#1890ff' }}>PE:</span>
            <Typography.Text
              style={{
                fontSize: '12px',
                color: projectExpenseTotal < 0 ? '#ff4d4f' : '#1890ff',
              }}
            >
              {formatCurrency(projectExpenseTotal)}
            </Typography.Text>
          </div>
        )}

        {/* Purchase Order */}
        {purchaseOrderTotal !== 0 && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
            <span style={{ fontSize: '12px', color: '#fa8c16' }}>PO:</span>
            <Typography.Text
              style={{
                fontSize: '12px',
                color: purchaseOrderTotal < 0 ? '#ff4d4f' : '#fa8c16',
              }}
            >
              {formatCurrency(purchaseOrderTotal)}
            </Typography.Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default CategorySummary;
