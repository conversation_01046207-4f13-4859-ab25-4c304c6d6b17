import React from "react";
import { Card, Statistic, Typography, Tooltip } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { formatCurrency } from "../utils/helpers";

interface SummaryCardProps {
  title: string;
  value: number | string;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  tooltip?: string;
  color?: string;
  loading?: boolean;
  formatter?: (value: number | string) => React.ReactNode;
}

const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  prefix,
  suffix,
  tooltip,
  color = "#1890ff",
  loading = false,
  formatter = (val) => formatCurrency(val),
}) => {
  return (
    <Card
      hoverable
      style={{
        borderTop: `2px solid ${color}`,
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.09)",
      }}
      loading={loading}
    >
      <Statistic
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
            <Typography.Text strong>{title}</Typography.Text>
            {tooltip && (
              <Tooltip title={tooltip}>
                <InfoCircleOutlined style={{ marginLeft: 8, color: "#999" }} />
              </Tooltip>
            )}
          </div>
        }
        value={value}
        valueStyle={{ color }}
        prefix={prefix}
        suffix={suffix}
        formatter={formatter}
      />
    </Card>
  );
};

export default SummaryCard;
