import React, { useState } from 'react';
import { Button, Card, message, Space } from 'antd';
import { invitationService } from '../services/invitationService';

interface InvitationResponseProps {
  token: string;
  onSuccess?: () => void;
}

export const InvitationResponse: React.FC<InvitationResponseProps> = ({ token, onSuccess }) => {
  const [loading, setLoading] = useState(false);

  const handleAccept = async () => {
    setLoading(true);
    try {
      await invitationService.acceptInvitation(token);
      message.success('Invitation accepted successfully');
      onSuccess?.();
    } catch (error) {
      message.error('Failed to accept invitation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDecline = async () => {
    setLoading(true);
    try {
      await invitationService.declineInvitation(token);
      message.success('Invitation declined');
      onSuccess?.();
    } catch (error) {
      message.error('Failed to decline invitation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title='Organization Invitation'>
      <Space direction='vertical' style={{ width: '100%' }}>
        <p>You have been invited to join an organization.</p>
        <Space>
          <Button type='primary' onClick={handleAccept} loading={loading}>
            Accept Invitation
          </Button>
          <Button onClick={handleDecline} loading={loading}>
            Decline
          </Button>
        </Space>
      </Space>
    </Card>
  );
};
