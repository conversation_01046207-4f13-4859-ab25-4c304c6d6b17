import { InfoCircleOutlined } from '@ant-design/icons';
import { DatePicker, Form, Input, InputNumber, Select, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { DATE_FORMAT } from '../../constants/dateFormats';
import { Invoice, Project, PurchaseOrder } from '../../types';

const { Option } = Select;

interface InvoiceFormFieldsProps {
  projects: Project[];
  purchaseOrders?: PurchaseOrder[];
  tagOptions: { label: string; value: string }[];
  categoryTagOptions: { label: string; value: string }[];
  handleProjectChange: (value: number) => void;
  isEditing?: boolean;
  hideProjectSelect?: boolean;
  selectedProject?: number | null;
  initialValues?: Invoice | null;
  form?: any;
  defaultDate?: string; // Add default date prop
}

const InvoiceFormFields: React.FC<InvoiceFormFieldsProps> = ({
  projects,
  purchaseOrders = [],
  tagOptions,
  categoryTagOptions,
  handleProjectChange,
  isEditing = false,
  hideProjectSelect = false,
  selectedProject,
  initialValues,
  form,
  defaultDate,
}) => {
  const [categoryValue, setCategoryValue] = useState<string | undefined>();
  const [purchaseOrderValue, setPurchaseOrderValue] = useState<number | undefined>();

  // Set initial values when component mounts or initialValues change
  useEffect(() => {
    if (form && initialValues && isEditing) {
      form.setFieldsValue({
        project_id: initialValues.project_id,
        invoice_number: initialValues.invoice_number,
        description: initialValues.description,
        amount: initialValues.amount,
        due_date: initialValues.due_date ? dayjs(initialValues.due_date) : null,
        category_tag_name: initialValues.category_tag ? initialValues.category_tag.name : '',
        purchase_order_id: initialValues.purchase_order_id || undefined,
        tag_names: initialValues.tags ? initialValues.tags.map(tag => tag.name) : [],
      });
      setCategoryValue(initialValues.category_tag ? initialValues.category_tag.name : undefined);
      setPurchaseOrderValue(initialValues.purchase_order_id || undefined);
    } else if (!isEditing && defaultDate) {
      // Set default due_date when creating new invoice
      form.setFieldsValue({
        due_date: dayjs(defaultDate),
      });
      // Reset local state when not editing (new modal)
      setCategoryValue(undefined);
      setPurchaseOrderValue(undefined);
    } else if (!isEditing) {
      // Reset local state when not editing (new modal)
      setCategoryValue(undefined);
      setPurchaseOrderValue(undefined);
    }
  }, [form, initialValues, isEditing, defaultDate]);

  let categoryPlaceholder = 'Select a category';
  if (selectedProject && categoryTagOptions.length === 0) {
    categoryPlaceholder = 'No categories available';
  } else if (!selectedProject) {
    categoryPlaceholder = 'Select a project first';
  }

  const isCategorySelectDisabled = !selectedProject || categoryTagOptions.length === 0;
  const isPurchaseOrderSelectDisabled = !selectedProject || purchaseOrders.length === 0;

  // Only show error if both are set
  const hasBothFields = categoryValue && purchaseOrderValue;

  return (
    <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
      {!hideProjectSelect && (
        <Form.Item
          name='project_id'
          label='Project'
          rules={[{ required: true, message: 'Please select a project' }]}
          style={{ flex: 1, minWidth: '200px' }}
        >
          <Select onChange={handleProjectChange}>
            <Option value=''>Select a Project</Option>
            {projects.map(project => (
              <Option key={project.id} value={project.id}>
                {project.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
      )}

      <Form.Item
        name='invoice_number'
        label='Invoice Number'
        style={{ flex: 1, minWidth: '150px' }}
      >
        <Input placeholder='Enter invoice number' />
      </Form.Item>

      <Form.Item
        name='amount'
        label='Amount'
        rules={[{ required: true, message: 'Please enter an amount' }]}
        style={{ flex: 1, minWidth: '150px' }}
      >
        <InputNumber
          formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value!.replace(/\$\s?|(,*)/g, '')}
          style={{ width: '100%' }}
        />
      </Form.Item>

      <Form.Item
        name='due_date'
        label='Due Date'
        rules={[{ required: true, message: 'Please select a due date' }]}
        style={{ flex: 1, minWidth: '200px' }}
      >
        <DatePicker format={DATE_FORMAT} style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name='category_tag_name'
        label={
          <span>
            Expense Category{' '}
            <Tooltip title='You can select either an expense category OR a purchase order, but not both. Please choose only one.'>
              <InfoCircleOutlined style={{ color: '#1890ff', marginLeft: 4 }} />
            </Tooltip>
          </span>
        }
        style={{ flex: 1, minWidth: '200px' }}
        extra={
          hasBothFields ? (
            <span style={{ color: '#ff4d4f' }}>
              ⚠️ Cannot have both expense category and purchase order. Please choose only one.
            </span>
          ) : null
        }
        validateStatus={hasBothFields ? 'error' : undefined}
      >
        <Select
          showSearch
          allowClear
          disabled={isCategorySelectDisabled}
          placeholder={categoryPlaceholder}
          options={categoryTagOptions}
          filterOption={(inputValue, option) =>
            option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
          onChange={value => {
            setCategoryValue(value);
          }}
        />
      </Form.Item>

      <Form.Item
        name='purchase_order_id'
        label={
          <span>
            Purchase Order{' '}
            <Tooltip title='You can select either a purchase order OR an expense category, but not both. Please choose only one.'>
              <InfoCircleOutlined style={{ color: '#1890ff', marginLeft: 4 }} />
            </Tooltip>
          </span>
        }
        style={{ flex: 1, minWidth: '200px' }}
        extra={
          hasBothFields ? (
            <span style={{ color: '#ff4d4f' }}>
              ⚠️ Cannot have both expense category and purchase order. Please choose only one.
            </span>
          ) : null
        }
        validateStatus={hasBothFields ? 'error' : undefined}
      >
        <Select
          showSearch
          allowClear
          disabled={isPurchaseOrderSelectDisabled}
          placeholder={
            purchaseOrders.length === 0 ? 'No purchase orders available' : 'Select purchase order'
          }
          options={purchaseOrders.map(po => ({
            label: `${po.po_number} - ${po.description || 'No description'}`,
            value: po.id,
          }))}
          filterOption={(inputValue, option) =>
            option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
          onChange={value => {
            setPurchaseOrderValue(value);
          }}
        />
      </Form.Item>

      <Form.Item name='description' label='Description' style={{ flex: 2, minWidth: '200px' }}>
        <Input.TextArea rows={1} placeholder='Enter description' />
      </Form.Item>

      <Form.Item name='tag_names' label='Tags' style={{ flex: 2, minWidth: '200px' }}>
        <Select
          mode='tags'
          style={{ width: '100%' }}
          placeholder='Select or create tags'
          tokenSeparators={[',']}
          options={tagOptions}
          filterOption={(inputValue, option) =>
            option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
        />
      </Form.Item>
    </div>
  );
};

export default InvoiceFormFields;
