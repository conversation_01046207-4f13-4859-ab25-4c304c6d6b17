import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Form, Input, Layout, message, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { authService } from '../../services/auth';
import { DomainUtils } from '../../utils/domain';

const { Content } = Layout;
const { Title, Text } = Typography;

export const Login: React.FC = () => {
  const [form] = Form.useForm();
  const { login } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [unverifiedEmail, setUnverifiedEmail] = useState<string | null>(null);
  const [resendLoading, setResendLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [searchParams] = useSearchParams();
  const inviteToken = searchParams.get('invite');

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  const handleSubmit = async (values: any) => {
    try {
      setIsLoading(true);
      const redirectPath = await login(values.email, values.password);
      // If there's an invitation token, redirect to the invitation response page
      if (inviteToken) {
        navigate(`/invite/${inviteToken}`, { replace: true });
      } else if (redirectPath.startsWith('http')) {
        // For full URLs, let the browser handle the redirect
        window.location.href = redirectPath;
      } else {
        console.log('Using React Router navigate for relative path');
        navigate(redirectPath, { replace: true });
      }
    } catch (err: any) {
      if (
        err.response?.status === 403 &&
        err.response?.data?.detail?.includes('verify your email')
      ) {
        setUnverifiedEmail(values.email);
      } else {
        message.error(err.response?.data?.detail || 'Failed to login');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    if (!unverifiedEmail) return;

    try {
      setResendLoading(true);
      await authService.resendVerification(unverifiedEmail);
      message.success('Verification email sent successfully');
      setCountdown(30);
    } catch (err: any) {
      message.error(err.response?.data?.detail || 'Failed to resend verification email');
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <Content style={{ padding: '24px', maxWidth: 400, margin: '0 auto' }}>
      <Card>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 24 }}>
          Login
        </Title>

        {unverifiedEmail && (
          <Alert
            message='Email Not Verified'
            description={
              <>
                <p>Please verify your email address before logging in.</p>
                <p>Check your inbox for the verification link or click below to resend it.</p>
                <Button
                  type='primary'
                  onClick={handleResendVerification}
                  loading={resendLoading}
                  disabled={countdown > 0}
                  style={{ marginTop: 16 }}
                >
                  {countdown > 0
                    ? `Resend Verification Email (${countdown}s)`
                    : 'Resend Verification Email'}
                </Button>
                {countdown > 0 && (
                  <p style={{ marginTop: 8, color: '#666' }}>
                    Please wait {countdown} seconds before requesting another verification email.
                  </p>
                )}
              </>
            }
            type='warning'
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <Form.Item
            name='email'
            label='Email'
            rules={[
              { required: true, message: 'Please input your email!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name='password'
            label='Password'
            rules={[{ required: true, message: 'Please input your password!' }]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item>
            <Button type='primary' htmlType='submit' block loading={isLoading}>
              Login
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Text type='secondary'>
            Don't have an account?{' '}
            <Link
              to={
                DomainUtils.getSubdomain()
                  ? `${DomainUtils.getBaseUrl()}${
                      inviteToken ? `/register?invite=${inviteToken}` : '/register'
                    }`
                  : inviteToken
                  ? `/register?invite=${inviteToken}`
                  : '/register'
              }
            >
              Register here
            </Link>
          </Text>
        </div>
      </Card>
    </Content>
  );
};
