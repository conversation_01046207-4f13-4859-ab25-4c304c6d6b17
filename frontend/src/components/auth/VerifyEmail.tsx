import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Layout, Spin, Typography } from 'antd';
import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { DomainUtils } from '../../utils/domain';

const { Content } = Layout;
const { Title } = Typography;
let running = false;
export const VerifyEmail: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        if (running) {
          return;
        }
        running = true;
        await axios.get(`${DomainUtils.getAPIUrl()}/api/auth/verify-email/${token}`);
        setStatus('success');
        setMessage('Your email has been verified successfully! You can now log in.');
      } catch (error: any) {
        setStatus('error');
        setMessage(
          error.response?.data?.detail ||
            'Failed to verify email. The link may be invalid or expired.'
        );
      }
    };

    verifyEmail();
  }, [token]);

  const handleLogin = () => {
    navigate('/login');
  };

  return (
    <Content style={{ padding: '24px', maxWidth: 400, margin: '0 auto' }}>
      <Card>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 24 }}>
          Email Verification
        </Title>

        {status === 'loading' && (
          <div style={{ textAlign: 'center', padding: '24px' }}>
            <Spin size='large' />
            <p style={{ marginTop: 16 }}>Verifying your email...</p>
          </div>
        )}

        {status === 'success' && (
          <>
            <Alert
              message='Success'
              description={message}
              type='success'
              showIcon
              style={{ marginBottom: 24 }}
            />
            <Button type='primary' block onClick={handleLogin}>
              Go to Login
            </Button>
          </>
        )}

        {status === 'error' && (
          <Alert
            message='Error'
            description={message}
            type='error'
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}
      </Card>
    </Content>
  );
};
