import { Button, Card, Form, Input, Layout, Spin, Typography } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { useOrganization } from '../../contexts/OrganizationContext';
import { DomainUtils } from '../../utils/domain';

const { Content } = Layout;
const { Title } = Typography;

export const CreateOrganization: React.FC = () => {
  const [form] = Form.useForm();
  const { createOrganization, checkSubdomainAvailability } = useOrganization();
  const [isChecking, setIsChecking] = useState(false);
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [checkTimeout, setCheckTimeout] = useState<NodeJS.Timeout | null>(null);
  const [subdomain, setSubdomain] = useState('');

  const checkSubdomain = useCallback(
    async (subdomain: string) => {
      if (subdomain.length >= 3) {
        setIsChecking(true);
        try {
          console.log('Checking subdomain availability for:', subdomain);
          const available = await checkSubdomainAvailability(subdomain);
          console.log('Subdomain availability result:', available);
          setIsAvailable(available);
        } catch (err) {
          console.error('Error checking subdomain:', err);
          setIsAvailable(false);
        } finally {
          setIsChecking(false);
        }
      } else {
        setIsAvailable(null);
      }
    },
    [checkSubdomainAvailability]
  );

  const handleSubdomainChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSubdomain = e.target.value.toLowerCase();
    setSubdomain(newSubdomain);
    form.setFieldsValue({ subdomain: newSubdomain });

    // Clear any existing timeout
    if (checkTimeout) {
      clearTimeout(checkTimeout);
    }

    // Set new timeout
    const timeoutId = setTimeout(() => {
      checkSubdomain(newSubdomain);
    }, 500);

    setCheckTimeout(timeoutId);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
    };
  }, [checkTimeout]);

  const handleSubmit = async (values: any) => {
    if (!isAvailable) {
      form.setFields([
        {
          name: 'subdomain',
          errors: ['This subdomain is not available'],
        },
      ]);
      return;
    }

    try {
      await createOrganization(values.name, values.subdomain);
      window.location.href = DomainUtils.getOrganizationUrl(values.subdomain) + '/dashboard';
    } catch (err: any) {
      form.setFields([
        {
          name: 'name',
          errors: [err.response?.data?.detail || 'Failed to create organization'],
        },
      ]);
    }
  };

  const getSubdomainStatus = () => {
    // Show the availability status
    if (isChecking) return 'Checking availability...';
    if (isAvailable) return 'Subdomain is available';
    if (isAvailable === false) return 'This subdomain is already taken';
    if (!subdomain || subdomain.length < 3) return 'Enter at least 3 characters';
    return '';
  };

  return (
    <Content style={{ padding: '24px', maxWidth: 400, margin: '0 auto' }}>
      <Card>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 24 }}>
          Create Organization
        </Title>
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <Form.Item
            name='name'
            label='Organization Name'
            rules={[{ required: true, message: 'Please input organization name!' }]}
          >
            <Input autoFocus />
          </Form.Item>

          <Form.Item
            name='subdomain'
            label='Subdomain'
            validateStatus={isAvailable === false ? 'error' : undefined}
            help={getSubdomainStatus()}
            rules={[
              { required: true, message: 'Please input subdomain!' },
              { min: 3, message: 'Subdomain must be at least 3 characters!' },
            ]}
          >
            <Input
              suffix={
                <div style={{ width: 16, height: 16 }}>{isChecking && <Spin size='small' />}</div>
              }
              onChange={handleSubdomainChange}
            />
          </Form.Item>

          <Form.Item>
            <Button type='primary' htmlType='submit' block disabled={!isAvailable || isChecking}>
              Create Organization
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </Content>
  );
};
