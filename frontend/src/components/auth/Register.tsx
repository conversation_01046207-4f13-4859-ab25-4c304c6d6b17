import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Layout, Form, Input, Button, Typography, Card, Alert, Result } from 'antd';
import { MailOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title } = Typography;

export const Register: React.FC = () => {
  const [form] = Form.useForm();
  const { register } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const inviteToken = searchParams.get('invite');
  const [registrationSuccess, setRegistrationSuccess] = React.useState(false);

  const handleSubmit = async (values: any) => {
    try {
      await register({
        email: values.email,
        password: values.password,
        first_name: values.firstName,
        last_name: values.lastName,
        invite_token: inviteToken,
      });
      setRegistrationSuccess(true);
    } catch (err: any) {
      console.log(err);
      form.setFields([
        {
          name: 'email',
          errors: [err.response?.data?.detail || 'Failed to register'],
        },
      ]);
    }
  };

  if (registrationSuccess) {
    return (
      <Content style={{ padding: '24px', maxWidth: 400, margin: '0 auto' }}>
        <Card>
          <Result
            icon={<MailOutlined style={{ color: '#1890ff' }} />}
            status='success'
            title='Registration Successful!'
            subTitle="Please check your email for a verification link. You'll need to verify your email before you can log in."
            extra={[
              <Button type='primary' key='login' onClick={() => navigate('/login')}>
                Go to Login
              </Button>,
            ]}
          />
        </Card>
      </Content>
    );
  }

  return (
    <Content style={{ padding: '24px', maxWidth: 400, margin: '0 auto' }}>
      <Card>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 24 }}>
          Create Account
        </Title>
        {inviteToken && (
          <Alert
            message="You've been invited to join an organization"
            description="After creating your account, you'll be automatically added to the organization."
            type='info'
            style={{ marginBottom: 24 }}
          />
        )}
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <Form.Item
            name='firstName'
            label='First Name'
            rules={[{ required: true, message: 'Please input your first name!' }]}
          >
            <Input autoFocus />
          </Form.Item>

          <Form.Item
            name='lastName'
            label='Last Name'
            rules={[{ required: true, message: 'Please input your last name!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name='email'
            label='Email Address'
            rules={[
              { required: true, message: 'Please input your email!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name='password'
            label='Password'
            rules={[{ required: true, message: 'Please input your password!' }]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item
            name='confirmPassword'
            label='Confirm Password'
            dependencies={['password']}
            rules={[
              { required: true, message: 'Please confirm your password!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('The two passwords do not match!'));
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item>
            <Button type='primary' htmlType='submit' block>
              Create Account
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </Content>
  );
};
