import React, { forwardRef } from 'react';
import { Table, Spin, TableProps } from 'antd';

// Define the props interface with a generic type
interface LoadingTableProps<T> extends TableProps<T> {
  loading: boolean;
}

// Create a generic component type that TypeScript can use in JSX
const LoadingTableDef = <T extends object>(
  { loading, ...tableProps }: LoadingTableProps<T>,
  ref: React.Ref<any>
) => {
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin size='large' />
      </div>
    );
  }

  return <Table<T> ref={ref} {...tableProps} scroll={{ x: 'max-content' }} />;
};

// Use forwardRef with proper typing
export const LoadingTable = forwardRef(LoadingTableDef) as <T extends object>(
  props: LoadingTableProps<T> & { ref?: React.Ref<any> }
) => JSX.Element;

// Export with a type assertion to allow generic usage in JSX
export default LoadingTable;
