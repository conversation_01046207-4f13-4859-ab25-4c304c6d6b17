import React from 'react';
import { InputNumber, InputNumberProps } from 'antd';

type ValueType = string | number;

interface CurrencyInputProps extends Omit<InputNumberProps<ValueType>, 'formatter' | 'parser'> {
  value?: ValueType | null;
  onChange?: (value: ValueType | null) => void;
}

export const CurrencyInput: React.FC<CurrencyInputProps> = ({
  value,
  onChange,
  style,
  ...props
}) => (
  <InputNumber<ValueType>
    value={value}
    onChange={onChange}
    formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
    parser={value => value!.replace(/\$\s?|(,*)/g, '')}
    style={{ width: '100%', ...style }}
    {...props}
  />
);
