import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import { Divider, Table, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useTableControls } from '../hooks/useTableControls';
import { Project, ProjectsByWeek } from '../types';
import { formatDateRange } from '../utils/dateFormatting';
import { formatCurrency } from '../utils/formatting';
import { getParentChildSearchProps } from '../utils/tableFilters';
import CategorySummary from './CategorySummary';
import ClearFiltersButton from './common/clearFiltersButton';
import DraggableCard from './DraggableCard';
import DroppableCell from './DroppableCell';

interface ProjectTableProps {
  title: string;
  projectsByWeek: ProjectsByWeek;
  columnLabels: string[];
  weeklyTotals: Record<string, number>;
  runningTotals: Record<string, number>;
  loading: boolean;
  onDrop: (item: any, newWeekDate: string) => void;
  onEdit: (item: any) => void;
  onAddItem: (item: any) => void;
  onDelete: (item: any, type: string) => void;
}

const ProjectTable: React.FC<ProjectTableProps> = ({
  title,
  projectsByWeek,
  columnLabels,
  weeklyTotals,
  runningTotals,
  loading,
  onDrop,
  onEdit,
  onAddItem,
  onDelete,
}) => {
  const { filteredInfo, sortedInfo, handleTableChange, clearFiltersAndSorting } =
    useTableControls();

  // Helper function to calculate breakdown totals for a project
  const calculateProjectBreakdown = (projectData: any, weekDate: string) => {
    const weekData = projectData.weeklyData[weekDate];
    if (!weekData) return { revenue: 0, projected: 0, expected: 0, owed: 0 };

    const breakdown = { revenue: 0, projected: 0, expected: 0, owed: 0 };

    weekData.items.forEach((item: any) => {
      switch (item.type) {
        case 'milestone':
          breakdown.revenue += Number(item.amount);
          break;
        case 'projectExpense':
          breakdown.projected += Number(item.amount);
          break;
        case 'purchaseOrder':
          breakdown.expected += Number(item.amount);
          break;
        case 'invoice':
          breakdown.owed += Number(item.amount);
          break;
      }
    });

    return breakdown;
  };

  // Helper function to calculate running totals for a project
  const calculateProjectRunningTotals = (projectData: any, weekDate: string) => {
    const weekDates = Object.keys(projectData.weeklyData).sort();
    const currentWeekIndex = weekDates.indexOf(weekDate);

    if (currentWeekIndex === -1) return { revenue: 0, projected: 0, expected: 0, owed: 0 };

    const running = { revenue: 0, projected: 0, expected: 0, owed: 0 };

    for (let i = 0; i <= currentWeekIndex; i++) {
      const breakdown = calculateProjectBreakdown(projectData, weekDates[i]);
      running.revenue += breakdown.revenue;
      running.projected += breakdown.projected;
      running.expected += breakdown.expected;
      running.owed += breakdown.owed;
    }

    return running;
  };

  const getCategory = (item: any, project: any) => {
    if (item.category_tag && item.category_tag.name) {
      return item.category_tag.name;
    }

    if (item.type === 'invoice' && item.purchase_order_id) {
      const relatedPO = project.purchase_orders.find((po: any) => po.id === item.purchase_order_id);
      if (relatedPO?.category_tag?.name) {
        return relatedPO.category_tag.name;
      }
    }

    return null;
  };

  // Helper function to calculate unified category summaries for a project
  const calculateUnifiedCategorySummaries = (projectData: any, weekDate: string) => {
    const weekDates = Object.keys(projectData.weeklyData).sort();
    const currentWeekIndex = weekDates.indexOf(weekDate);

    if (currentWeekIndex === -1) return [];

    const categoryTotals: Record<
      string,
      {
        projectExpenseTotal: number;
        purchaseOrderTotal: number;
        invoiceTotal: number;
      }
    > = {};

    const project = projectData.project;
    const handledPOs: Map<number, any> = new Map();
    const poInvoiceAmount: Map<number, number> = new Map();

    // Calculate totals for each category up to and including the current week
    for (let i = 0; i <= currentWeekIndex; i++) {
      const weekData = projectData.weeklyData[weekDates[i]];
      if (!weekData) continue;

      weekData.items.forEach((item: any) => {
        const categoryName = getCategory(item, project);
        if (!categoryName) return;

        if (!categoryTotals[categoryName]) {
          categoryTotals[categoryName] = {
            projectExpenseTotal: 0,
            purchaseOrderTotal: 0,
            invoiceTotal: 0,
          };
        }

        if (item.type === 'projectExpense') {
          categoryTotals[categoryName].projectExpenseTotal += Number(item.amount);
        }

        if (item.type === 'purchaseOrder') {
          categoryTotals[categoryName].purchaseOrderTotal += Number(item.amount);
          handledPOs.set(item.id, item);
          if (poInvoiceAmount.has(item.id)) {
            categoryTotals[categoryName].purchaseOrderTotal -= poInvoiceAmount.get(item.id) || 0;
            poInvoiceAmount.delete(item.id);
          }
          // Subtract the amount from the project expense because PO represents a commitment to spend
          categoryTotals[categoryName].projectExpenseTotal -= Number(item.amount);
        }

        if (item.type === 'invoice') {
          categoryTotals[categoryName].invoiceTotal += Number(item.amount);
          if (item.purchase_order_id) {
            if (handledPOs.has(item.purchase_order_id)) {
              // Subtract the amount from the purchase order total because the invoice represents a payment owed from the purchase order
              categoryTotals[categoryName].purchaseOrderTotal -= Number(item.amount);
            } else {
              poInvoiceAmount.set(item.purchase_order_id, item.amount);
            }
          } else {
            // Subtract the amount from the project expense because invoice represents a payment owed from the project expense
            categoryTotals[categoryName].projectExpenseTotal -= Number(item.amount);
          }
        }
      });
    }

    // Convert to array format and filter out categories with no activity
    return Object.entries(categoryTotals)
      .map(([categoryName, data]) => ({
        categoryName,
        projectExpenseTotal: data.projectExpenseTotal,
        purchaseOrderTotal: data.purchaseOrderTotal,
        invoiceTotal: data.invoiceTotal,
      }))
      .filter(
        item =>
          item.projectExpenseTotal !== 0 || item.purchaseOrderTotal !== 0 || item.invoiceTotal !== 0
      );
  };

  // Helper function to format value with arrow
  const formatValueWithArrow = (value: number, isRevenue: boolean = false) => {
    // For display, show all values as positive, but use arrows to indicate cash flow direction
    const displayValue = Math.abs(value);

    if (displayValue === 0) {
      // For $0 values, show a blank space to maintain alignment but indicate no cash flow
      return (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <span style={{ width: '14px', marginRight: 4 }}></span>
          {formatCurrency(displayValue)}
        </span>
      );
    }

    const arrow = isRevenue ? (
      <ArrowUpOutlined style={{ color: '#52c41a', marginRight: 4, fontSize: '10px' }} />
    ) : (
      <ArrowDownOutlined style={{ color: '#ff4d4f', marginRight: 4, fontSize: '10px' }} />
    );

    return (
      <span style={{ display: 'inline-flex', alignItems: 'center' }}>
        {arrow}
        {formatCurrency(displayValue)}
      </span>
    );
  };

  const dataSource = Object.values(projectsByWeek).map(pd => ({
    key: pd.project.id,
    project: pd.project,
    weeklyData: pd.weeklyData,
    children: [
      {
        key: `${pd.project.id}-items`,
        project: pd.project,
        weeklyData: pd.weeklyData,
        isChild: true,
      },
    ],
  }));

  const tableStyles = `
  .forecast-page .ant-table-body > table {
    height: fit-content !important;
  }
  .forecast-page .ant-table-tbody > tr > td {
    height: 100% !important;
    vertical-align: top !important;
  }
  .forecast-page .child-row {
    background-color: #fafafa;
  }
  .forecast-page .child-row:hover > td {
    background-color: #f0f0f0 !important;
  }
  .breakdown-table {
    font-size: 11px;
    width: 100%;
  }
  .breakdown-table td {
    padding: 2px 4px;
    border: none;
  }
  .breakdown-table .header {
    font-weight: bold;
    text-align: left;
  }
  .breakdown-table .label {
    font-weight: 500;
    text-align: left;
  }
  .breakdown-table .value {
    text-align: left;
    white-space: nowrap;
  }
  .breakdown-table .value span {
    display: inline-flex;
    align-items: center;
  }
  .breakdown-table .revenue {
    color: #52c41a;
  }
  .breakdown-table .projected {
    color: #1890ff;
  }
  .breakdown-table .expected {
    color: #fa8c16;
  }
  .breakdown-table .owed {
    color: #f5222d;
  }`;

  const columns = useMemo(
    () => [
      {
        title: 'Project',
        dataIndex: 'project',
        key: 'project',
        fixed: 'left' as const,
        width: 200,
        render: (project: Project, record: any) =>
          !record.isChild && <Typography.Text strong>{project.name}</Typography.Text>,
        sorter: (a: any, b: any) => a.project.name.localeCompare(b.project.name),
        sortOrder: sortedInfo.columnKey === 'project' && sortedInfo.order,
        filteredValue: filteredInfo.project || null,
        ...getParentChildSearchProps('project'),
      },
      ...columnLabels.map((weekDate: string) => ({
        title: formatDateRange(dayjs(weekDate), dayjs(weekDate).day(6)),
        key: weekDate,
        dataIndex: weekDate,
        width: 250,
        sortOrder: sortedInfo.columnKey === weekDate && sortedInfo.order,
        filteredValue: filteredInfo[weekDate] || null,
        render: (_: unknown, record: any) => {
          if (!record.isChild) {
            const breakdown = calculateProjectBreakdown(record, weekDate);
            const running = calculateProjectRunningTotals(record, weekDate);

            // Only show if there are any items
            const hasItems =
              breakdown.revenue !== 0 ||
              breakdown.projected !== 0 ||
              breakdown.expected !== 0 ||
              breakdown.owed !== 0;

            if (!hasItems) {
              return null;
            }

            return (
              <table className='breakdown-table'>
                <tbody>
                  <tr>
                    <td></td>
                    <td className='header'>Week</td>
                    <td></td>
                    <td className='header'>Running</td>
                  </tr>
                  <tr>
                    <td className='label'>Revenue</td>
                    <td className='value revenue'>
                      {formatValueWithArrow(breakdown.revenue, true)}
                    </td>
                    <td></td>
                    <td className='value revenue'>{formatValueWithArrow(running.revenue, true)}</td>
                  </tr>
                  <tr>
                    <td className='label'>Projected</td>
                    <td className='value projected'>
                      {formatValueWithArrow(breakdown.projected, false)}
                    </td>
                    <td></td>
                    <td className='value projected'>
                      {formatValueWithArrow(running.projected, false)}
                    </td>
                  </tr>
                  <tr>
                    <td className='label'>Expected</td>
                    <td className='value expected'>
                      {formatValueWithArrow(breakdown.expected, false)}
                    </td>
                    <td></td>
                    <td className='value expected'>
                      {formatValueWithArrow(running.expected, false)}
                    </td>
                  </tr>
                  <tr>
                    <td className='label'>Owed</td>
                    <td className='value owed'>{formatValueWithArrow(breakdown.owed, false)}</td>
                    <td></td>
                    <td className='value owed'>{formatValueWithArrow(running.owed, false)}</td>
                  </tr>
                </tbody>
              </table>
            );
          }

          // For child rows, show unified category summaries above the droppable area
          const categorySummaries = calculateUnifiedCategorySummaries(record, weekDate);

          return (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '4px',
                alignItems: 'flex-start', // Align content to the top
                height: '100%',
                justifyContent: 'flex-start', // Ensure content starts at the top
              }}
            >
              {/* Summary Cards */}
              {categorySummaries.length > 0 && (
                <div style={{ marginBottom: '4px', width: '100%' }}>
                  {/* Unified Category Summary Cards */}
                  {categorySummaries.map(summary => (
                    <CategorySummary
                      key={`${record.project.id}-${weekDate}-${summary.categoryName}`}
                      categoryName={summary.categoryName}
                      projectExpenseTotal={summary.projectExpenseTotal}
                      purchaseOrderTotal={summary.purchaseOrderTotal}
                      invoiceTotal={summary.invoiceTotal}
                    />
                  ))}
                </div>
              )}

              {/* Droppable Area */}
              <DroppableCell
                weekDate={weekDate}
                projectId={record.project.id}
                projectName={record.project.name}
                onDrop={onDrop}
                onAddItem={onAddItem}
              >
                {record.weeklyData[weekDate]?.items?.length ? (
                  record.weeklyData[weekDate].items.map((item: any) => (
                    <DraggableCard
                      key={item.id}
                      item={item}
                      type={item.type}
                      weekDate={weekDate}
                      projectId={record.project.id}
                      onEdit={onEdit}
                      onAddItem={onAddItem}
                      onDelete={onDelete}
                      projectName={record.project.name}
                      project={record.project}
                    />
                  ))
                ) : (
                  <Typography.Text type='secondary'>No items</Typography.Text>
                )}
              </DroppableCell>
            </div>
          );
        },
      })),
    ],
    [
      columnLabels,
      sortedInfo,
      filteredInfo,
      projectsByWeek,
      onDrop,
      onEdit,
      onAddItem,
      onDelete,
      calculateProjectRunningTotals,
      calculateUnifiedCategorySummaries,
    ]
  );

  return (
    <div className='project-table-container'>
      <style>{tableStyles}</style>
      <Divider orientation='left'>
        <Typography.Title level={3}>{title}</Typography.Title>
      </Divider>
      {columnLabels?.length ? (
        <DndProvider backend={HTML5Backend}>
          <ClearFiltersButton onClear={clearFiltersAndSorting} />
          {Object.keys(projectsByWeek).length > 0 ? (
            <Table
              dataSource={dataSource}
              columns={columns}
              pagination={false}
              scroll={{ x: 1800, y: 600 }}
              bordered
              loading={loading}
              className='forecast-table'
              sticky={{ offsetHeader: 0 }}
              expandable={{
                defaultExpandedRowKeys: [],
                expandRowByClick: true,
              }}
              onRow={(record: any) => ({
                className: record.isChild ? 'child-row' : '',
              })}
              onChange={handleTableChange}
              summary={() => (
                <Table.Summary fixed='top'>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0}>
                      <Typography.Text strong>Weekly Totals</Typography.Text>
                    </Table.Summary.Cell>
                    {columnLabels.map(weekDate => (
                      <Table.Summary.Cell key={weekDate} index={1}>
                        <Typography.Text
                          strong
                          type={weeklyTotals[weekDate] >= 0 ? 'success' : 'danger'}
                        >
                          Week: ${weeklyTotals[weekDate]?.toLocaleString() || 0}
                        </Typography.Text>
                        <br />
                        <Typography.Text
                          strong
                          type={runningTotals[weekDate] >= 0 ? 'success' : 'danger'}
                        >
                          Running: ${runningTotals[weekDate]?.toLocaleString() || 0}
                        </Typography.Text>
                      </Table.Summary.Cell>
                    ))}
                  </Table.Summary.Row>
                </Table.Summary>
              )}
            />
          ) : (
            <Typography.Text
              type='secondary'
              style={{ display: 'block', textAlign: 'center', padding: 20 }}
            >
              No project data available
            </Typography.Text>
          )}
        </DndProvider>
      ) : (
        <Typography.Text
          type='secondary'
          style={{ display: 'block', textAlign: 'center', padding: 20 }}
        >
          Generate a forecast to view project data
        </Typography.Text>
      )}
    </div>
  );
};

export default ProjectTable;
