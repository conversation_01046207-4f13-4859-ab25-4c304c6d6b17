import React from 'react';
import { Collapse, Table, Checkbox, Button, Input, Popconfirm } from 'antd';
import { SaveOutlined, DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { DATE_FORMAT, DATETIME_FORMAT } from '../constants/dateFormats';
import { ForecastSnapshot, ChartData } from '../types';

interface SnapshotsPanelProps {
  snapshots: ForecastSnapshot[];
  visibleLines: Record<string | number, boolean>;
  onVisibilityChange: (id: string | number, checked: boolean) => void;
  onDelete: (id: string | number) => void;
  onSave: () => void;
  savingSnapshot: boolean;
  chartData: ChartData<'line'> | null;
  loading: boolean;
}

const SnapshotsPanel: React.FC<SnapshotsPanelProps> = ({
  snapshots,
  visibleLines,
  onVisibilityChange,
  onDelete,
  onSave,
  savingSnapshot,
  chartData,
  loading,
}) => {
  const columns = [
    { title: 'Name', dataIndex: 'name', key: 'name' },
    {
      title: 'Snapped At',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text: string) => dayjs(text).format(DATETIME_FORMAT),
    },
    {
      title: 'Start Week',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (text: string) => dayjs(text).format(DATE_FORMAT),
    },
    {
      title: 'End Week',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (text: string) => dayjs(text).format(DATE_FORMAT),
    },
    {
      title: 'Show',
      key: 'show',
      render: (_: any, record: ForecastSnapshot) => (
        <Checkbox
          checked={visibleLines[record.id]}
          onChange={e => onVisibilityChange(record.id, e.target.checked)}
        />
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: ForecastSnapshot) => (
        <Popconfirm title='Are you sure?' onConfirm={() => onDelete(record.id)}>
          <Button type='primary' danger size='small' icon={<DeleteOutlined />} />
        </Popconfirm>
      ),
    },
  ];

  const items = [
    {
      key: 'snapshots',
      label: 'Snapshots',
      extra: (
        <div onClick={e => e.stopPropagation()}>
          <Input
            id='snapshot-name'
            placeholder='Snapshot Name'
            style={{ width: '200px', marginRight: 8 }}
          />
          <Button
            type='primary'
            onClick={onSave}
            loading={savingSnapshot}
            icon={<SaveOutlined />}
            disabled={!chartData}
          >
            Save
          </Button>
        </div>
      ),
      children: (
        <Table
          dataSource={snapshots}
          columns={columns}
          rowKey='id'
          size='small'
          pagination={false}
          loading={loading}
        />
      ),
    },
  ];

  return <Collapse defaultActiveKey={[]} items={items} />;
};

export default SnapshotsPanel;
