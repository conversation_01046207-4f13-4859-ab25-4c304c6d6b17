import { <PERSON>, Modal, Spin } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import { DATE_FORMAT } from '../constants/dateFormats';
import { useOrganization } from '../contexts/OrganizationContext';
import {
  getProjectCategoryTags,
  getProjectExpenses,
  getProjects,
  getPurchaseOrders,
  getTags,
} from '../services/api';
import { Project, ProjectExpense, PurchaseOrder, Tag } from '../types';
import InvoiceFormFields from './forms/InvoiceFormFields';
import MilestoneFormFields from './forms/MilestoneFormFields';
import ProjectExpenseFormFields from './forms/ProjectExpenseFormFields';
import PurchaseOrderFormFields from './forms/PurchaseOrderFormFields';

type ItemType = 'milestone' | 'projectExpense' | 'purchaseOrder' | 'invoice';

interface CreateModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (values: any) => Promise<void>;
  projectName: string;
  type: ItemType;
  date: string;
  projectId: number;
}

const typeToTitle: Record<ItemType, string> = {
  milestone: 'Milestone',
  projectExpense: 'Project Expense',
  purchaseOrder: 'Purchase Order',
  invoice: 'Invoice',
};

const CreateModal: React.FC<CreateModalProps> = ({
  visible,
  onCancel,
  onSave,
  projectName,
  type,
  date,
  projectId,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const [categoryTagOptions, setCategoryTagOptions] = useState<{ label: string; value: string }[]>(
    []
  );
  const [projectExpenses, setProjectExpenses] = useState<ProjectExpense[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const { organization } = useOrganization();

  useEffect(() => {
    if (visible) {
      // Reset form to clear any previous values
      form.resetFields();
      // Set the required initial values with appropriate date field for each type
      const initialValues: any = { project_id: projectId };

      // Set the appropriate date field based on the item type
      switch (type) {
        case 'milestone':
        case 'projectExpense':
          initialValues.date = dayjs(date);
          break;
        case 'purchaseOrder':
          initialValues.issue_date = dayjs(date);
          break;
        case 'invoice':
          initialValues.due_date = dayjs(date);
          break;
      }

      form.setFieldsValue(initialValues);
      setSelectedProject(projectId);
      fetchInitialData();
    } else {
      // Clean up form when modal is closed
      form.resetFields();
      setSelectedProject(null);
    }
  }, [visible, date, projectId, type, form]);

  useEffect(() => {
    if (selectedProject) {
      fetchProjectExpenses(selectedProject);
      if (type === 'purchaseOrder' || type === 'invoice') {
        fetchProjectCategoryTags(selectedProject);
        fetchPurchaseOrders(selectedProject);
      }
    }
  }, [selectedProject, type]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      const [projectsRes, tagsRes] = await Promise.all([getProjects(), getTags()]);
      setProjects(projectsRes.data);
      const sortedTags = tagsRes.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setTags(sortedTags);
      setTagOptions(
        sortedTags.map((tag: Tag) => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProjectExpenses = async (projectId: number) => {
    try {
      // Correctly fetch project expenses
      const res = await getProjectExpenses(projectId);
      setProjectExpenses(res.data);
    } catch (error) {
      console.error('Error fetching project expenses:', error);
      setProjectExpenses([]);
    }
  };

  const fetchPurchaseOrders = async (projectId: number) => {
    try {
      const response = await getPurchaseOrders(projectId);
      const data = response.data;
      if (Array.isArray(data)) {
        setPurchaseOrders(data);
      } else {
        setPurchaseOrders([]);
      }
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      setPurchaseOrders([]);
    }
  };

  const fetchProjectCategoryTags = async (projectId: number) => {
    try {
      const response = await getProjectCategoryTags(projectId);
      const categoryTags = response.data;
      setCategoryTagOptions(
        categoryTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error) {
      console.error('Error fetching project category tags:', error);
      setCategoryTagOptions([]);
    }
  };

  const handleProjectChange = (value: number) => {
    setSelectedProject(value);
    form.setFieldsValue({ project_id: value });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      await onSave({
        ...values,
        date: (values.date as Dayjs)?.format(DATE_FORMAT),
        project_id: projectId,
        type,
      });
      onCancel();
    } catch (error) {
      console.error('Create failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderFormFields = () => {
    switch (type) {
      case 'purchaseOrder':
        return (
          <PurchaseOrderFormFields
            projects={projects}
            projectExpenses={projectExpenses}
            tagOptions={tagOptions}
            categoryTagOptions={categoryTagOptions}
            handleProjectChange={handleProjectChange}
            selectedProject={selectedProject}
            hideProjectSelect
            form={form}
            defaultDate={date}
          />
        );
      case 'invoice':
        return (
          <InvoiceFormFields
            projects={projects}
            purchaseOrders={purchaseOrders}
            tagOptions={tagOptions}
            categoryTagOptions={categoryTagOptions}
            handleProjectChange={handleProjectChange}
            hideProjectSelect
            selectedProject={selectedProject}
            form={form}
            defaultDate={date}
          />
        );
      case 'milestone':
        return <MilestoneFormFields tagOptions={tagOptions} form={form} defaultDate={date} />;
      case 'projectExpense':
        return <ProjectExpenseFormFields tagOptions={tagOptions} form={form} defaultDate={date} />;
      default:
        return null;
    }
  };

  return (
    <Modal
      title={`Create ${typeToTitle[type]} for ${projectName}`}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnClose
      width={800}
    >
      <Spin spinning={loading}>
        <Form form={form} layout='vertical' key={`create-${type}-${projectId}`}>
          {renderFormFields()}
        </Form>
      </Spin>
    </Modal>
  );
};

export default CreateModal;
