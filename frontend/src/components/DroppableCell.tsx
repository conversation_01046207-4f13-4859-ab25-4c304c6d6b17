import { PlusOutlined } from '@ant-design/icons';
import { Dropdown, Typography } from 'antd';
import React, { useMemo, useState } from 'react';
import { useDrop } from 'react-dnd';
import CreateModal from './CreateModal'; // Assume this exists from original

interface DroppableCellProps {
  weekDate: string;
  projectId: number;
  projectName: string;
  onDrop: (item: any, newWeekDate: string) => void;
  onAddItem: (item: any) => void;
  children: React.ReactNode;
}

const ItemTypes = {
  MILESTONE: 'milestone',
  PROJECT_EXPENSE: 'projectExpense',
  PURCHASE_ORDER: 'purchaseOrder',
  INVOICE: 'invoice',
};

const DroppableCell: React.FC<DroppableCellProps> = ({
  weekDate,
  projectId,
  projectName,
  onDrop,
  onAddItem,
  children,
}) => {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createModalType, setCreateModalType] = useState<
    'milestone' | 'projectExpense' | 'purchaseOrder' | 'invoice'
  >('milestone');

  const [{ isOver }, drop] = useDrop(() => ({
    accept: [
      ItemTypes.MILESTONE,
      ItemTypes.PROJECT_EXPENSE,
      ItemTypes.PURCHASE_ORDER,
      ItemTypes.INVOICE,
    ],
    drop: (droppedItem: any) => {
      onDrop(droppedItem, weekDate);
    },
    collect: (monitor: any) => ({
      isOver: !!monitor.isOver(),
    }),
  }));

  const menuItems = useMemo(
    () => [
      {
        key: 'add_milestone',
        icon: <PlusOutlined />,
        label: 'Add Milestone',
        onClick: () => {
          setCreateModalType('milestone');
          setCreateModalVisible(true);
        },
      },
      {
        key: 'addproject_expense',
        icon: <PlusOutlined />,
        label: 'Add Project Expense',
        onClick: () => {
          setCreateModalType('projectExpense');
          setCreateModalVisible(true);
        },
      },
      {
        key: 'add_purchase_order',
        label: 'Add Purchase Order',
        icon: <PlusOutlined />,
        onClick: () => {
          setCreateModalType('purchaseOrder');
          setCreateModalVisible(true);
        },
      },
      {
        key: 'add_invoice',
        label: 'Add Invoice',
        icon: <PlusOutlined />,
        onClick: () => {
          setCreateModalType('invoice');
          setCreateModalVisible(true);
        },
      },
    ],
    []
  );

  const handleSave = async (values: any) => {
    try {
      await onAddItem(values);
      setCreateModalVisible(false);
    } catch (error) {
      console.error('Error creating item:', error);
    }
  };

  return (
    <>
      <Dropdown menu={{ items: menuItems }} trigger={['contextMenu']}>
        <div
          ref={drop}
          style={{
            backgroundColor: isOver ? 'rgba(0, 0, 0, 0.05)' : 'transparent',
            minHeight: '100px',
            height: '100%',
            width: '100%',
            padding: '8px',
            border: '1px dashed #d9d9d9',
            borderRadius: '4px',
            display: 'flex',
            flexDirection: 'column' as const,
            gap: '8px',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
          }}
        >
          {children || <Typography.Text type='secondary'>No items</Typography.Text>}
        </div>
      </Dropdown>
      <CreateModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSave={handleSave}
        projectName={projectName}
        type={createModalType}
        date={weekDate}
        projectId={projectId}
      />
    </>
  );
};

export default DroppableCell;

// export default React.memo(DroppableCell, (prevProps, nextProps) => {
//   return (
//     prevProps.weekDate === nextProps.weekDate &&
//     prevProps.projectId === nextProps.projectId &&
//     prevProps.projectName === nextProps.projectName &&
//     prevProps.children === nextProps.children
//   );
// });
