import { But<PERSON>, Col, DatePicker, Form, Row, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React from 'react';
import { WEEK_FORMAT } from '../constants/dateFormats';
import { ForecastFormValues } from '../types';

const { WeekPicker } = DatePicker;
const { Text } = Typography;

interface ForecastFormProps {
  onSubmit: (values: ForecastFormValues) => void;
  submitting: boolean;
  initialValues?: ForecastFormValues | null;
}

const ForecastForm: React.FC<ForecastFormProps> = ({ onSubmit, submitting, initialValues }) => {
  const [form] = Form.useForm<ForecastFormValues>();

  // Custom handler to ensure we always use the first day of the week
  const handleSubmit = (values: ForecastFormValues) => {
    // Ensure both startWeek and endWeek are set to the first day of their respective weeks
    console.log(values.startWeek.startOf('week').toJSON());
    console.log(values.endWeek.startOf('week').toJSON());
    const adjustedValues = {
      ...values,
      startWeek: values.startWeek.startOf('week'),
      endWeek: values.endWeek.startOf('week'),
    };
    onSubmit(adjustedValues);
  };

  // Custom onChange handler for WeekPicker to ensure displayed value is always the first day of the week
  const handleWeekChange = (
    date: Dayjs | null,
    dateString: string | string[],
    field: 'startWeek' | 'endWeek'
  ) => {
    if (date) {
      // Set the form value to the first day of the week
      form.setFieldValue(field, date.startOf('week'));
    }
  };

  return (
    <Form
      form={form}
      layout='vertical'
      onFinish={handleSubmit}
      initialValues={
        initialValues || {
          startWeek: dayjs().startOf('week'),
          endWeek: dayjs().add(12, 'week').startOf('week'),
        }
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Typography.Title level={4}>Generate Cashflow Forecast</Typography.Title>
      </div>

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
          <div style={{ display: 'flex', alignItems: 'flex-start' }}>
            <Form.Item
              name='startWeek'
              label='Week of'
              rules={[{ required: true }]}
              style={{ marginBottom: 0 }}
            >
              <WeekPicker
                format={WEEK_FORMAT}
                onChange={(date, dateString) => handleWeekChange(date, dateString, 'startWeek')}
                style={{ width: 180 }}
              />
            </Form.Item>

            <div
              style={{
                margin: '0 16px',
                display: 'flex',
                alignItems: 'center',
                height: '32px',
                marginTop: '29px',
              }}
            >
              <Text strong>through</Text>
            </div>

            <Form.Item
              name='endWeek'
              label='Week of'
              rules={[{ required: true }]}
              style={{ marginBottom: 0 }}
            >
              <WeekPicker
                format={WEEK_FORMAT}
                onChange={(date, dateString) => handleWeekChange(date, dateString, 'endWeek')}
                style={{ width: 180 }}
              />
            </Form.Item>
          </div>
        </Col>

        <Col xs={24} sm={24} md={24} lg={24} xl={24} style={{ marginTop: 16 }}>
          <Form.Item>
            <Button type='primary' htmlType='submit' loading={submitting} size='large'>
              Generate Forecast
            </Button>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default ForecastForm;
