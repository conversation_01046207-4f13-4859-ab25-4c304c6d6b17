import React from 'react';
import { Card, Col, Row, Typography } from 'antd';
import { Account } from '../types';

const { Text, Title } = Typography;

interface AccountBalancesProps {
  accounts: Account[];
}

const AccountBalances: React.FC<AccountBalancesProps> = ({ accounts }) => {
  if (!accounts || accounts.length === 0) {
    return null;
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Define the desired order for account types
  const accountTypeOrder = ['Checking', 'Savings', 'Credit Card', 'Line of Credit'];

  // Sort accounts by the specified account type order
  const sortedAccounts = [...accounts].sort((a, b) => {
    const aIndex = accountTypeOrder.indexOf(a.account_type);
    const bIndex = accountTypeOrder.indexOf(b.account_type);

    // If account type is not in the order array, put it at the end
    const aOrder = aIndex === -1 ? accountTypeOrder.length : aIndex;
    const bOrder = bIndex === -1 ? accountTypeOrder.length : bIndex;

    return aOrder - bOrder;
  });

  return (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          Account Balances
        </Title>
      }
      style={{ marginBottom: 16 }}
    >
      <Row gutter={[16, 8]}>
        {sortedAccounts.map(account => (
          <Col key={account.id} xs={24} sm={12} md={6}>
            <div style={{ textAlign: 'center', padding: '8px' }}>
              <Text strong style={{ display: 'block', marginBottom: 4 }}>
                {account.name}
              </Text>
              <Text
                style={{
                  fontSize: '16px',
                  color: account.balance >= 0 ? '#52c41a' : '#ff4d4f',
                }}
              >
                {formatCurrency(account.balance)}
              </Text>
            </div>
          </Col>
        ))}
      </Row>
    </Card>
  );
};

export default AccountBalances;
