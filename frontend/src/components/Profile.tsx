import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useOrganization } from '../contexts/OrganizationContext';
import {
  Layout,
  Typography,
  Card,
  Tabs,
  Input,
  Button,
  Row,
  Col,
  Alert,
  List,
  Modal,
  Form,
  Space,
} from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { DomainUtils } from '../utils/domain';
const { Content } = Layout;
const { Title, Text } = Typography;

export const Profile: React.FC = () => {
  const { user, updateUser } = useAuth();
  const { organization, organizations, updateOrganization, inviteUser } = useOrganization();
  const [activeTab, setActiveTab] = useState('1');
  const [userForm] = Form.useForm();
  const [orgForm] = Form.useForm();
  const [inviteForm] = Form.useForm();
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [inviteModalVisible, setInviteModalVisible] = useState(false);

  const handleUserSubmit = async (values: any) => {
    setError('');
    setSuccess('');

    try {
      await updateUser(values);
      setSuccess('User information updated successfully');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to update user information');
    }
  };

  const handleOrgSubmit = async (values: any) => {
    setError('');
    setSuccess('');

    try {
      await updateOrganization(organization?.id!, values);
      setSuccess('Organization information updated successfully');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to update organization information');
    }
  };

  const handleInviteSubmit = async (values: any) => {
    setError('');
    setSuccess('');

    try {
      await inviteUser(values.email);
      setSuccess('Invitation sent successfully');
      setInviteModalVisible(false);
      inviteForm.resetFields();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to send invitation');
    }
  };

  const items = [
    {
      key: '1',
      label: 'User Information',
      children: (
        <Form
          form={userForm}
          layout='vertical'
          onFinish={handleUserSubmit}
          initialValues={{
            first_name: user?.first_name || '',
            last_name: user?.last_name || '',
            email: user?.email || '',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='first_name'
                label='First Name'
                rules={[{ required: true, message: 'Please input your first name!' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='last_name'
                label='Last Name'
                rules={[{ required: true, message: 'Please input your last name!' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name='email'
            label='Email'
            rules={[
              { required: true, message: 'Please input your email!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item>
            <Button type='primary' htmlType='submit'>
              Update User Information
            </Button>
          </Form.Item>
        </Form>
      ),
    },
    {
      key: '2',
      label: 'Organization Information',
      children: (
        <Form
          form={orgForm}
          layout='vertical'
          onFinish={handleOrgSubmit}
          initialValues={{
            name: organization?.name || '',
          }}
        >
          <Form.Item
            name='name'
            label='Organization Name'
            rules={[{ required: true, message: 'Please input organization name!' }]}
          >
            <Input />
          </Form.Item>
          <Text type='secondary'>
            Subdomain: {organization?.subdomain}.{DomainUtils.getConfiguredBaseDomain()}
          </Text>
          <Form.Item>
            <Button type='primary' htmlType='submit'>
              Update Organization Information
            </Button>
          </Form.Item>
        </Form>
      ),
    },
    {
      key: '3',
      label: 'Team Members',
      children: (
        <>
          <Space style={{ marginBottom: 16 }}>
            <Button type='primary' onClick={() => setInviteModalVisible(true)}>
              Invite Team Member
            </Button>
          </Space>
          <List
            itemLayout='horizontal'
            dataSource={[]}
            renderItem={item => (
              <List.Item
                actions={[
                  <Button type='text' icon={<EditOutlined />} />,
                  <Button type='text' danger icon={<DeleteOutlined />} />,
                ]}
              >
                <List.Item.Meta title='John Doe' description='<EMAIL>' />
              </List.Item>
            )}
          />
        </>
      ),
    },
  ];

  return (
    <Content style={{ padding: '24px', maxWidth: 800, margin: '0 auto' }}>
      <Card>
        {error && <Alert type='error' message={error} style={{ marginBottom: 16 }} />}
        {success && <Alert type='success' message={success} style={{ marginBottom: 16 }} />}

        <Tabs activeKey={activeTab} onChange={setActiveTab} items={items} />
      </Card>

      <Modal
        title='Invite Team Member'
        open={inviteModalVisible}
        onCancel={() => setInviteModalVisible(false)}
        footer={null}
      >
        <Form form={inviteForm} layout='vertical' onFinish={handleInviteSubmit}>
          <Form.Item
            name='email'
            label='Email Address'
            rules={[
              { required: true, message: 'Please input email address!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button onClick={() => setInviteModalVisible(false)}>Cancel</Button>
              <Button type='primary' htmlType='submit'>
                Send Invitation
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Content>
  );
};
