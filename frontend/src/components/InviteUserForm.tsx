import React, { useState } from 'react';
import { Button, Input, message } from 'antd';
import { invitationService } from '../services/invitationService';

interface InviteUserFormProps {
  organizationId: number;
  onSuccess?: () => void;
}

export const InviteUserForm: React.FC<InviteUserFormProps> = ({ organizationId, onSuccess }) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await invitationService.sendInvitation(organizationId, email);
      message.success('Invitation sent successfully');
      setEmail('');
      onSuccess?.();
    } catch (error) {
      message.error('Failed to send invitation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} style={{ display: 'flex', gap: '8px' }}>
      <Input
        type='email'
        placeholder='Enter email address'
        value={email}
        onChange={e => setEmail(e.target.value)}
        required
        style={{ flex: 1 }}
      />
      <Button type='primary' htmlType='submit' loading={loading}>
        Send Invitation
      </Button>
    </form>
  );
};
