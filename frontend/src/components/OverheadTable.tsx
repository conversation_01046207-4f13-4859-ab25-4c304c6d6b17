import React from 'react';
import { Table, Typography } from 'antd';
import { formatCurrency } from '../utils/formatting';
import { formatDateRange } from '../utils/dateFormatting';
import dayjs from 'dayjs';
import { OverheadData } from '../types';

interface OverheadTableProps {
  overheadData: OverheadData | null;
  loading: boolean;
}

interface TableRow {
  key: string;
  category: string;
  [weekDate: string]: string | number;
}

const OverheadTable: React.FC<OverheadTableProps> = ({ overheadData, loading }) => {
  if (!overheadData) return null;

  const columns = [
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      fixed: 'left' as const,
    },
    ...overheadData.labels.map((weekDate: string) => {
      const headerText = formatDateRange(dayjs(weekDate), dayjs(weekDate).day(6));
      // Calculate width based on header text length (approximate 8px per character + padding)
      const headerWidth = Math.max(headerText.length * 8 + 32, 120); // minimum 120px

      return {
        title: headerText,
        dataIndex: weekDate,
        key: weekDate,
        width: headerWidth,
        render: (value: number, record: TableRow) => {
          // For payroll, fixed, and misc expenses, show running total above current week value
          if (
            record.category === 'Payroll Expenses' ||
            record.category === 'Fixed Expenses' ||
            record.category === 'Misc Expenses'
          ) {
            const weeklyValue = (record[`${weekDate}_weekly`] as number) || 0;
            const runningTotal = value;

            return (
              <div>
                <div style={{ fontSize: '11px', color: '#666', marginBottom: '2px' }}>
                  Running: {formatCurrency(runningTotal)}
                </div>
                {weeklyValue !== 0 && (
                  <div style={{ fontWeight: 'bold' }}>{formatCurrency(weeklyValue)}</div>
                )}
              </div>
            );
          }

          // For other categories, show original behavior
          if (value !== 0) {
            return formatCurrency(value);
          }

          return null;
        },
      };
    }),
  ];

  const data: TableRow[] = [
    {
      key: 'accounts',
      category: 'Accounts Total',
      ...overheadData.labels.reduce(
        (acc: Record<string, number>, weekDate: string, index: number) => {
          acc[weekDate] = overheadData.accounts_totals[index] || 0;
          return acc;
        },
        {}
      ),
    },
    {
      key: 'payroll',
      category: 'Payroll Expenses',
      ...overheadData.labels.reduce(
        (acc: Record<string, number>, weekDate: string, index: number) => {
          acc[weekDate] = overheadData.payroll_expenses_totals[index] || 0;
          acc[`${weekDate}_weekly`] = overheadData.payroll_expenses_weekly[index] || 0;
          return acc;
        },
        {}
      ),
    },
    {
      key: 'fixed',
      category: 'Fixed Expenses',
      ...overheadData.labels.reduce(
        (acc: Record<string, number>, weekDate: string, index: number) => {
          acc[weekDate] = overheadData.fixed_expenses_totals[index] || 0;
          acc[`${weekDate}_weekly`] = overheadData.fixed_expenses_weekly[index] || 0;
          return acc;
        },
        {}
      ),
    },
    {
      key: 'misc',
      category: 'Misc Expenses',
      ...overheadData.labels.reduce(
        (acc: Record<string, number>, weekDate: string, index: number) => {
          acc[weekDate] = overheadData.misc_expenses_totals[index] || 0;
          acc[`${weekDate}_weekly`] = overheadData.misc_expenses_weekly[index] || 0;
          return acc;
        },
        {}
      ),
    },
  ];

  return (
    <div className='overhead-table'>
      <Typography.Title level={4}>Overhead</Typography.Title>
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
        scroll={{ x: 'max-content', y: 600 }}
        bordered
        size='small'
      />
    </div>
  );
};

export default OverheadTable;
