import { Form, Modal, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useOrganization } from '../contexts/OrganizationContext';
import { getProjectCategoryTags, getProjects, getPurchaseOrders, getTags } from '../services/api';
import { Invoice, Milestone, Project, ProjectExpense, PurchaseOrder, Tag } from '../types';
import InvoiceFormFields from './forms/InvoiceFormFields';
import MilestoneFormFields from './forms/MilestoneFormFields';
import ProjectExpenseFormFields from './forms/ProjectExpenseFormFields';
import PurchaseOrderFormFields from './forms/PurchaseOrderFormFields';

type ItemType = 'milestone' | 'projectExpense' | 'purchaseOrder' | 'invoice';

type Item = Milestone | ProjectExpense | PurchaseOrder | Invoice;

interface EditModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (values: Item) => Promise<void>;
  item: Item | null;
  type: ItemType;
}

const typeToTitle: Record<ItemType, string> = {
  milestone: 'Milestone',
  projectExpense: 'Project Expense',
  purchaseOrder: 'Purchase Order',
  invoice: 'Invoice',
};

const EditModal: React.FC<EditModalProps> = ({ visible, onCancel, onSave, item, type }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const [categoryTagOptions, setCategoryTagOptions] = useState<{ label: string; value: string }[]>(
    []
  );
  const [projectExpenses, setProjectExpenses] = useState<ProjectExpense[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const { organization } = useOrganization();

  useEffect(() => {
    if (visible && item) {
      // Reset form to clear any previous values
      form.resetFields();
      // Let each form fields component handle its own field mapping
      setSelectedProject(item.project_id);
      fetchInitialData();
    } else {
      // Clean up form when modal is closed
      form.resetFields();
      setSelectedProject(null);
    }
  }, [visible, item, form]);

  useEffect(() => {
    if (selectedProject) {
      fetchProjectExpenses(selectedProject);
      if (type === 'purchaseOrder' || type === 'invoice') {
        fetchProjectCategoryTags(selectedProject);
        fetchPurchaseOrders(selectedProject);
      }
    }
  }, [selectedProject, type]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      const [projectsRes, tagsRes] = await Promise.all([getProjects(), getTags()]);
      setProjects(projectsRes.data);
      const sortedTags = tagsRes.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setTags(sortedTags);
      setTagOptions(
        sortedTags.map((tag: Tag) => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProjectExpenses = async (projectId: number) => {
    try {
      // Correctly fetch project expenses
    } catch (error) {
      console.error('Error fetching project expenses:', error);
      setProjectExpenses([]);
    }
  };

  const fetchPurchaseOrders = async (projectId: number) => {
    try {
      const response = await getPurchaseOrders(projectId);
      const data = response.data;
      if (Array.isArray(data)) {
        setPurchaseOrders(data);
      } else {
        setPurchaseOrders([]);
      }
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      setPurchaseOrders([]);
    }
  };

  const fetchProjectCategoryTags = async (projectId: number) => {
    try {
      const response = await getProjectCategoryTags(projectId);
      const categoryTags = response.data;
      setCategoryTagOptions(
        categoryTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error) {
      console.error('Error fetching project category tags:', error);
      setCategoryTagOptions([]);
    }
  };

  const handleProjectChange = (value: number) => {
    setSelectedProject(value);
    form.setFieldsValue({ project_id: value });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      await onSave({
        ...item!,
        ...values,
      });
      onCancel();
    } catch (error) {
      console.error('Edit failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderFormFields = () => {
    switch (type) {
      case 'purchaseOrder':
        return (
          <PurchaseOrderFormFields
            projects={projects}
            projectExpenses={projectExpenses}
            tagOptions={tagOptions}
            categoryTagOptions={categoryTagOptions}
            handleProjectChange={handleProjectChange}
            isEditing
            selectedProject={selectedProject}
            hideProjectSelect
            form={form}
            initialValues={item as PurchaseOrder}
          />
        );
      case 'invoice':
        return (
          <InvoiceFormFields
            projects={projects}
            purchaseOrders={purchaseOrders}
            tagOptions={tagOptions}
            categoryTagOptions={categoryTagOptions}
            handleProjectChange={handleProjectChange}
            isEditing
            hideProjectSelect
            selectedProject={selectedProject}
            initialValues={item as Invoice}
            form={form}
          />
        );
      case 'milestone':
        return (
          <MilestoneFormFields
            tagOptions={tagOptions}
            initialValues={item as Milestone}
            form={form}
            isEditing={true}
          />
        );
      case 'projectExpense':
        return (
          <ProjectExpenseFormFields
            tagOptions={tagOptions}
            initialValues={item as ProjectExpense}
            form={form}
            isEditing={true}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      title={`Edit ${typeToTitle[type]}`}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnClose
      width={800}
    >
      <Spin spinning={loading}>
        <Form form={form} layout='vertical' key={`edit-${type}-${item?.id || 'new'}`}>
          {renderFormFields()}
        </Form>
      </Spin>
    </Modal>
  );
};

export default EditModal;
