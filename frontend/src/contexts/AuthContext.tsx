import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, authService } from '../services/auth';
import axios from 'axios';
import { DomainUtils } from '../utils/domain';
interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<string>;
  register: (data: any) => Promise<string>;
  logout: () => void;
  isLoading: boolean;
  updateUser: (data: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await authService.initialize();
        const user = await authService.getCurrentUser();
        setUser(user);
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Only logout if the error is an authentication error (401)
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          if (!DomainUtils.isLoginPath()) {
            authService.logout();
          }
          setUser(null);
        }
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      let subdomain = DomainUtils.getSubdomain() || '';
      console.log('Logging in with subdomain:', subdomain);
      const { user } = await authService.login({ email, password }, subdomain);
      setUser(user);
      const currentOrg = user.current_organization;
      if (currentOrg) {
        subdomain = currentOrg.subdomain;
      }
      // If user is logging in from base domain (no subdomain)
      if (!subdomain) {
        return '/create-organization';
      }
      const url = DomainUtils.getOrganizationUrl(subdomain);
      return `${url}/dashboard`;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const register = async (data: any) => {
    return await authService.register(data);
    // After registration, log in the user using the existing login function
    // return login(data.email, data.password);
  };

  const logout = () => {
    authService.logout();
    setUser(null);
  };

  const updateUser = async (data: Partial<User>) => {
    const updatedUser = await authService.updateUser(data);
    setUser(updatedUser);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        register,
        logout,
        isLoading,
        updateUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
