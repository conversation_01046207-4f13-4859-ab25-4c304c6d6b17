import axios from 'axios';
import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import api, { setOrganizationIdGetter } from '../services/api';
import { Organization } from '../types';
import { useAuth } from './AuthContext';

interface OrganizationContextType {
  organization: Organization | null;
  organizations: Organization[];
  loading: boolean;
  error: string | null;
  switchOrganization: (organizationId: number) => Promise<void>;
  createOrganization: (name: string, subdomain: string) => Promise<void>;
  updateOrganization: (orgId: number, data: Partial<Organization>) => Promise<void>;
  inviteUser: (email: string) => Promise<void>;
  setCurrentOrganization: (org: Organization) => void;
  checkSubdomainAvailability: (subdomain: string) => Promise<boolean>;
}

const OrganizationContext = createContext<OrganizationContextType>({
  organization: null,
  organizations: [],
  loading: true,
  error: null,
  switchOrganization: async () => {},
  createOrganization: async () => {},
  updateOrganization: async () => {},
  inviteUser: async () => {},
  setCurrentOrganization: () => {},
  checkSubdomainAvailability: async () => false,
});

export const useOrganization = () => useContext(OrganizationContext);

export const OrganizationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const hasFetched = useRef(false);
  const { user } = useAuth();

  // Initialize organization data from user data
  useEffect(() => {
    if (user && !hasFetched.current) {
      try {
        setLoading(true);
        setError(null);

        // Set organizations from user data
        setOrganizations(user.organizations || []);

        // Set current organization from user data
        const currentOrg = user.current_organization;
        if (currentOrg) {
          setOrganization(currentOrg);
          // Set up the organization ID getter immediately when we have the current organization
          setOrganizationIdGetter(() => currentOrg.id);
        }
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.detail ||
          err.response?.data?.message ||
          err.message ||
          'Failed to load organization data';
        setError(typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage));
      } finally {
        setLoading(false);
        hasFetched.current = true;
      }
    } else if (!user) {
      setLoading(false);
      // Clear the organization ID getter when user is not available
      setOrganizationIdGetter(() => undefined);
    }
  }, [user]);

  // Update organization ID getter when organization changes
  useEffect(() => {
    if (organization) {
      setOrganizationIdGetter(() => organization.id);
    } else {
      setOrganizationIdGetter(() => undefined);
    }
  }, [organization]);

  const switchOrganization = async (organizationId: number) => {
    try {
      const response = await api.post(`/api/organizations/${organizationId}/switch`);
      setOrganization(response.data);
      // Redirect to the new organization's dashboard
      navigate(`/dashboard`);
    } catch (error) {
      console.error('Error switching organization:', error);
      throw error;
    }
  };

  const createOrganization = async (name: string, subdomain: string) => {
    try {
      const response = await api.post('/api/organizations', { name, subdomain });
      setOrganizations([...organizations, response.data]);
      setOrganization(response.data);
    } catch (error) {
      console.error('Error creating organization:', error);
      throw error;
    }
  };

  const updateOrganization = async (orgId: number, data: Partial<Organization>) => {
    try {
      const response = await api.patch(`/api/organizations/${orgId}`, data);
      setOrganizations(organizations.map(org => (org.id === orgId ? response.data : org)));
      if (organization?.id === orgId) {
        setOrganization(response.data);
      }
    } catch (error) {
      console.error('Error updating organization:', error);
      throw error;
    }
  };

  const inviteUser = async (email: string) => {
    try {
      await api.post(`/api/organizations/${organization?.subdomain}/invites`, { email });
    } catch (error) {
      console.error('Error inviting user:', error);
      throw error;
    }
  };

  const setCurrentOrganization = (org: Organization) => {
    setOrganization(org);
  };

  const checkSubdomainAvailability = async (subdomain: string) => {
    try {
      const response = await api.get(`/api/organizations/check-subdomain/${subdomain}`);
      return response.data.available;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        return true; // Subdomain is available if endpoint returns 404
      }
      console.error('Error checking subdomain:', error);
      throw error;
    }
  };

  if (error) {
    return (
      <div className='error-message'>
        <h3>Error Loading Organization</h3>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <OrganizationContext.Provider
      value={{
        organization,
        organizations,
        loading,
        error,
        switchOrganization,
        createOrganization,
        updateOrganization,
        inviteUser,
        setCurrentOrganization,
        checkSubdomainAvailability,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
};
