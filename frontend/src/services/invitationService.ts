import axios from 'axios';

export interface Invitation {
  id: number;
  email: string;
  status: 'pending' | 'accepted' | 'declined';
  expires_at: string;
  created_at: string;
  organization_id: number;
  invited_by_id: number;
}

export interface InvitationResponse {
  message: string;
  invitation: Invitation;
}

export const invitationService = {
  sendInvitation: async (organizationId: number, email: string): Promise<InvitationResponse> => {
    const response = await axios.post(`/api/organizations/${organizationId}/invites`, { email });
    return response.data;
  },

  acceptInvitation: async (token: string): Promise<InvitationResponse> => {
    const response = await axios.post(`/api/organizations/invites/${token}/accept`);
    return response.data;
  },

  declineInvitation: async (token: string): Promise<InvitationResponse> => {
    const response = await axios.post(`/api/organizations/invites/${token}/decline`);
    return response.data;
  },
};
