import axios, { AxiosInstance, AxiosResponse } from 'axios';
import dayjs from 'dayjs';
import {
  Account,
  DashboardData,
  FixedExpense,
  ForecastParams,
  ForecastResponse,
  ForecastSnapshot,
  Invoice,
  Milestone,
  MiscExpense,
  PayrollExpense,
  Project,
  ProjectExpense,
  ProjectPayment,
  PurchaseOrder,
  Tag,
  UpsertAccount,
  UpsertFixedExpense,
  UpsertForecastSnapshot,
  UpsertInvoice,
  UpsertMilestone,
  UpsertMiscExpense,
  UpsertPayrollExpense,
  UpsertProject,
  UpsertProjectExpense,
  UpsertProjectPayment,
  UpsertPurchaseOrder,
  UpsertTag,
} from '../types';
import { DomainUtils } from '../utils/domain';

// Create a function to get the current organization ID
let getCurrentOrganizationId: (() => number | undefined) | null = null;

// Function to set the getter
export const setOrganizationIdGetter = (getter: () => number | undefined) => {
  getCurrentOrganizationId = getter;
};

const isoDateFormat = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d*)?(?:Z|[-+]\d{2}:\d{2})?$/;

function isIsoDateString(value: any): boolean {
  return value && typeof value === 'string' && isoDateFormat.test(value);
}

function convertToDayjs(body: any): any {
  if (body === null || body === undefined || typeof body !== 'object') {
    return body;
  }
  for (const key of Object.keys(body)) {
    const value = body[key];
    if (isIsoDateString(value)) {
      body[key] = dayjs(value);
    } else if (typeof value === 'object') {
      body[key] = convertToDayjs(value);
    }
  }
  return body;
}

function convertToString(body: any): any {
  if (body === null || body === undefined || typeof body !== 'object') {
    return body;
  }
  for (const key of Object.keys(body)) {
    const value = body[key];
    if (dayjs.isDayjs(value)) {
      body[key] = value.toISOString();
    } else if (typeof value === 'object') {
      body[key] = convertToString(value);
    }
  }
  return body;
}

// Create an axios instance with default config
const api: AxiosInstance = axios.create({
  baseURL: DomainUtils.getAPIUrl(), // Use DomainUtils to get the API URL
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  withCredentials: true, // Include cookies in cross-site requests
  transformResponse: [
    (data: any) => {
      try {
        const jsonData = JSON.parse(data);
        return convertToDayjs(jsonData);
      } catch (error) {
        return data;
      }
    },
  ],
  transformRequest: [
    (data: any, headers: any) => {
      const transformedData = convertToString(data);
      return JSON.stringify(transformedData);
    },
    ...(axios.defaults.transformRequest as any[]),
  ],
});

// Add request interceptor to handle subdomain
api.interceptors.request.use(config => {
  // Get the subdomain using DomainUtils
  const subdomain = DomainUtils.getSubdomain();

  // Add subdomain to headers for backend validation
  config.headers['X-Organization-Subdomain'] = subdomain || '';
  // Get the current organization ID using the getter
  if (getCurrentOrganizationId) {
    const orgId = getCurrentOrganizationId();
    if (orgId) {
      config.headers['X-Organization-Id'] = orgId.toString();
    }
  }

  return config;
});

// Add response interceptor to handle errors
api.interceptors.response.use(
  response => response,
  error => {
    // Log error details for debugging
    console.log('API Error:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url,
      baseURL: error.config?.baseURL,
      headers: error.config?.headers,
    });

    if (error.response?.status === 401) {
      // Only redirect to login if we're not already on the login page
      if (!DomainUtils.isLoginPath()) {
        // Get the current subdomain
        // Use React Router's navigate to maintain domain context
        window.location.href = `/login`;
      }
    }

    // Handle CORS errors
    if (error.message === 'Network Error' || error.code === 'ERR_NETWORK') {
      console.error('CORS or network error detected. Please check your backend configuration.');
      // Try to get more details about the error
      if (error.request) {
        console.error('Request details:', {
          url: error.request.url,
          method: error.request.method,
          headers: error.request.headers,
        });
      }
    }

    // Handle 500 errors
    if (error.response?.status === 500) {
      console.error('Server error:', error.response.data);
    }

    return Promise.reject(error);
  }
);

// Dashboard
export const getDashboardData = (): Promise<AxiosResponse<DashboardData>> => {
  // Create a mock dashboard data response
  return Promise.resolve({
    data: {
      total_balance: 0,
      monthly_fixed_expenses: 0,
      active_projects_value: 0,
    },
  } as AxiosResponse<DashboardData>);
};

// Accounts
export const getAccounts = (): Promise<AxiosResponse<Account[]>> => api.get('/api/accounts');
export const addAccount = (accountData: UpsertAccount): Promise<AxiosResponse<Account>> =>
  api.post('/api/accounts', accountData);
export const updateAccount = (accountData: UpsertAccount): Promise<AxiosResponse<Account>> =>
  api.post('/api/accounts', accountData);

// Fixed Expenses
export const getFixedExpenses = (): Promise<AxiosResponse<FixedExpense[]>> =>
  api.get('/api/fixed-expenses');
export const addFixedExpense = (
  expenseData: UpsertFixedExpense
): Promise<AxiosResponse<FixedExpense>> => api.post('/api/fixed-expenses', expenseData);
export const updateFixedExpense = (
  expenseData: UpsertFixedExpense
): Promise<AxiosResponse<FixedExpense>> => api.put('/api/fixed-expenses', expenseData);
export const deleteFixedExpense = (id: string | number): Promise<AxiosResponse<null>> =>
  api.delete('/api/fixed-expenses', { data: { id } });

// Misc Expenses
export const getMiscExpenses = (): Promise<AxiosResponse<MiscExpense[]>> =>
  api.get('/api/misc-expenses');
export const addMiscExpense = (
  expenseData: UpsertMiscExpense
): Promise<AxiosResponse<MiscExpense>> => api.post('/api/misc-expenses', expenseData);
export const updateMiscExpense = (
  expenseData: UpsertMiscExpense
): Promise<AxiosResponse<MiscExpense>> => api.put('/api/misc-expenses', expenseData);
export const deleteMiscExpense = (id: string | number): Promise<AxiosResponse<null>> =>
  api.delete('/api/misc-expenses', { data: { id } });

// Payroll
export const getPayroll = (): Promise<AxiosResponse<PayrollExpense[]>> =>
  api.get('/api/payroll-expenses');
export const addPayrollExpense = (
  expenseData: UpsertPayrollExpense
): Promise<AxiosResponse<PayrollExpense>> => api.post('/api/payroll-expenses', expenseData);
export const updatePayrollExpense = (
  expenseData: UpsertPayrollExpense
): Promise<AxiosResponse<PayrollExpense>> => api.put('/api/payroll-expenses', expenseData);
export const deletePayrollExpense = (id: string | number): Promise<AxiosResponse<null>> =>
  api.delete('/api/payroll-expenses', { data: { id } });

// Projects
// Milestones
export const createMilestone = (milestone: UpsertMilestone): Promise<AxiosResponse<Milestone>> =>
  api.post('/api/milestones', milestone);

export const updateMilestone = (milestone: UpsertMilestone): Promise<AxiosResponse<Milestone>> =>
  api.put('/api/milestones', milestone);

export const deleteMilestone = (id: number | string): Promise<AxiosResponse<null>> =>
  api.delete(`/api/milestones`, { data: { id } });

// Expenses
export const getProjectExpenses = (projectId: number): Promise<AxiosResponse<ProjectExpense[]>> =>
  api.get(`/api/project-expenses`, { params: { project_id: projectId } });

export const createProjectExpense = (
  projectExpense: UpsertProjectExpense
): Promise<AxiosResponse<ProjectExpense>> => api.post('/api/project-expenses', projectExpense);

export const updateProjectExpense = (
  projectExpense: UpsertProjectExpense
): Promise<AxiosResponse<ProjectExpense>> => api.put('/api/project-expenses', projectExpense);

export const deleteProjectExpense = (id: number | string): Promise<AxiosResponse<null>> =>
  api.delete(`/api/project-expenses`, { data: { id } });

export const getProjects = (): Promise<AxiosResponse<Project[]>> => {
  return api.get('/api/current-projects');
};
export const addProject = (projectData: UpsertProject): Promise<AxiosResponse<Project>> =>
  api.post('/api/current-projects', projectData);
export const updateProject = (projectData: UpsertProject): Promise<AxiosResponse<Project>> => {
  return api.put('/api/current-projects', {
    ...projectData,
  });
};
export const deleteProject = (id: number): Promise<AxiosResponse<null>> =>
  api.delete('/api/current-projects', { data: { id } });

// Project Payments
export const getProjectPayments = (
  projectId: string | number
): Promise<AxiosResponse<ProjectPayment[]>> => api.get(`/api/project-payments/${projectId}`);
export const addProjectPayment = (
  paymentData: UpsertProjectPayment
): Promise<AxiosResponse<ProjectPayment>> => api.post('/api/project-payment', paymentData);
export const updateProjectPayment = (
  paymentData: UpsertProjectPayment
): Promise<AxiosResponse<ProjectPayment>> => api.put('/api/project-payment', paymentData);
export const deleteProjectPayment = (id: string | number): Promise<AxiosResponse<null>> =>
  api.delete('/api/project-payment', { data: { id } });

// Forecast
export const generateForecast = (
  params: ForecastParams
): Promise<AxiosResponse<ForecastResponse>> => api.post('/api/forecast', params);
export const getForecastSnapshots = (): Promise<AxiosResponse<ForecastSnapshot[]>> =>
  api.get('/api/snapshots');
export const saveForecastSnapshot = (
  snapshotData: UpsertForecastSnapshot
): Promise<AxiosResponse<ForecastSnapshot>> => api.post('/api/snapshots', snapshotData);
export const deleteForecastSnapshot = (
  id: string | number
): Promise<AxiosResponse<{ message: string }>> => api.delete('/api/snapshots', { data: { id } });

// Purchase Orders
export const getPurchaseOrders = (
  project_id?: number
): Promise<
  AxiosResponse<{
    pos: PurchaseOrder[];
    projects: string[];
    project_expenses: any;
  }>
> => api.get('/api/purchase-orders', { params: { project_id } });

export const addPurchaseOrder = (
  poData: UpsertPurchaseOrder
): Promise<AxiosResponse<PurchaseOrder>> => api.post('/api/purchase-orders', poData);

export const updatePurchaseOrder = (
  poData: UpsertPurchaseOrder
): Promise<AxiosResponse<PurchaseOrder>> => {
  return api.put('/api/purchase-orders', poData);
};

export const deletePurchaseOrder = (poNumber: string | number): Promise<AxiosResponse<null>> =>
  api.delete('/api/purchase-orders', { data: { po_number: poNumber } });

// Invoices
export const getInvoices = (): Promise<AxiosResponse<Invoice[]>> => api.get('/api/invoices');

export const addInvoice = (invoiceData: UpsertInvoice): Promise<AxiosResponse<Invoice[]>> =>
  api.post('/api/invoices', invoiceData);

export const updateInvoice = (invoiceData: UpsertInvoice): Promise<AxiosResponse<Invoice>> =>
  api.put('/api/invoices', invoiceData);

export const deleteInvoice = (id: string | number): Promise<AxiosResponse<null>> =>
  api.delete('/api/invoices', { data: { id } });

// Anticipated Projects
export const getAnticipatedProjects = (): Promise<
  AxiosResponse<{
    projects: Project[];
    archived: { name: string }[];
  }>
> => api.get('/api/anticipated-projects');

// Project Creation Types
export const addAnticipatedProject = (
  projectData: UpsertProject
): Promise<AxiosResponse<Project>> => api.post('/api/anticipated-projects', projectData);

export const updateAnticipatedProject = (
  projectData: UpsertProject
): Promise<AxiosResponse<Project>> => api.put('/api/anticipated-projects', projectData);

export const deleteAnticipatedProject = (id: string): Promise<AxiosResponse<Project>> =>
  api.delete('/api/anticipated-projects', { data: { id } });

export const archiveProject = (id: string): Promise<AxiosResponse<Project>> =>
  api.post('/api/archive-project', { id });

export const restoreProject = (id: string): Promise<AxiosResponse<Project>> =>
  api.post('/api/restore-project', { id });

// Tags
export const getTags = (): Promise<AxiosResponse<Tag[]>> => api.get('/api/tags');
export const createTag = (tag: UpsertTag): Promise<AxiosResponse<Tag>> =>
  api.post('/api/tags', tag);
export const updateTag = (tag: UpsertTag): Promise<AxiosResponse<Tag>> => api.put('/api/tags', tag);
export const deleteTag = (id: number | string): Promise<AxiosResponse<null>> =>
  api.delete(`/api/tags`, { data: { id } });

export const getProjectCategoryTags = (projectId: number): Promise<AxiosResponse<Tag[]>> =>
  api.get(`/api/tags/project-categories/${projectId}`);

export default api;
