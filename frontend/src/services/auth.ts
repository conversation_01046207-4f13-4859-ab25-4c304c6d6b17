import axios from 'axios';
import { DomainUtils } from '../utils/domain';

export interface Organization {
  id: number;
  name: string;
  subdomain: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  created_at: string;
  updated_at: string;
  organizations: Organization[];
  current_organization: Organization;
}

class AuthService {
  private isInitializing = false;
  private initializationPromise: Promise<void> | null = null;

  private getAuthHeader() {
    return {};
  }

  private async refreshToken() {
    try {
      const response = await axios.post(
        `${DomainUtils.getAPIUrl()}/api/auth/refresh`,
        {},
        {
          withCredentials: true,
        }
      );
      const { access_token } = response.data;
      if (access_token) {
        return access_token;
      }
    } catch (error) {
      console.error('Failed to refresh token:', error);
    }
    return null;
  }

  private async handleApiError(error: any) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 401) {
        const newToken = await this.refreshToken();
        if (newToken && error.config) {
          const response = await axios.request(error.config);
          return response.data;
        }
      }
    }
    throw error;
  }

  async initialize() {
    if (this.isInitializing) {
      return this.initializationPromise;
    }

    this.isInitializing = true;
    this.initializationPromise = (async () => {
      try {
        await this.getCurrentUser();
      } catch (error) {
        console.error('Auth initialization failed:', error);
      } finally {
        this.isInitializing = false;
        this.initializationPromise = null;
      }
    })();

    return this.initializationPromise;
  }

  async login(credentials: { email: string; password: string }, subdomain: string) {
    const formData = new URLSearchParams();
    formData.append('username', credentials.email);
    formData.append('password', credentials.password);
    formData.append('client_id', subdomain);
    console.log('TEST API URL:', DomainUtils.getAPIUrl());
    const apiUrl = DomainUtils.getAPIUrl();
    console.log('Login API URL:', apiUrl);

    const response = await axios.post(`${apiUrl}/api/auth/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Organization-Subdomain': subdomain,
        Accept: 'application/json',
      },
      withCredentials: true,
    });
    console.log('Login response:', {
      message: response.data.message,
      headers: response.headers,
      cookies: document.cookie,
    });

    // Get user data after successful login
    console.log('Making /me request to:', `${apiUrl}/api/auth/me`);
    const userResponse = await axios.get(`${apiUrl}/api/auth/me`, {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        'X-Organization-Subdomain': subdomain,
        Accept: 'application/json',
      },
    });
    console.log('User response:', userResponse.data);

    return { user: userResponse.data };
  }

  async register(data: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    invite_token?: string;
  }) {
    const response = await axios.post(`${DomainUtils.getAPIUrl()}/api/auth/register`, data);
    return response.data;
  }

  async getCurrentUser() {
    try {
      const apiUrl = DomainUtils.getAPIUrl();
      const response = await axios.get(`${apiUrl}/api/auth/me`, {
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        const newToken = await this.refreshToken();
        if (newToken) {
          const response = await axios.get(`${DomainUtils.getAPIUrl()}/api/auth/me`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          });
          return response.data;
        }
      }
      throw error;
    }
  }

  async updateUser(data: Partial<User>) {
    const response = await axios.patch(`${DomainUtils.getAPIUrl()}/api/auth/me`, data, {
      headers: {
        ...this.getAuthHeader(),
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      withCredentials: true,
    });
    return response.data;
  }

  async logout() {
    try {
      await axios.post(
        `${DomainUtils.getAPIUrl()}/api/auth/logout`,
        {},
        {
          headers: this.getAuthHeader(),
          withCredentials: true,
        }
      );
    } catch (error) {
      console.error('Error during logout:', error);
    }
  }

  async resendVerification(email: string) {
    try {
      const response = await axios.post(`${DomainUtils.getAPIUrl()}/api/auth/resend-verification`, {
        email,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to resend verification:', error);
      throw error;
    }
  }
}

export const authService = new AuthService();
