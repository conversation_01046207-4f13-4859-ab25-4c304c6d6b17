import { ColumnType } from 'antd/lib/table';
import { FilterDropdownProps as AntFilterDropdownProps } from 'antd/lib/table/interface';

export interface TableFormProps {
  loading: boolean;
  submitting: boolean;
  onSubmit: (values: any) => Promise<void>;
}

export type FilterValue = string | number | { toString(): string };

export type FilterDropdownProps = AntFilterDropdownProps;

export type TableColumnType<T> = Omit<ColumnType<T>, 'onFilter' | 'filters'> & {
  onFilter?: (value: any, record: T) => boolean;
  filters?: Array<{
    text: React.ReactNode;
    value: any;
    children?: Array<{
      text: React.ReactNode;
      value: any;
    }>;
  }>;
};
