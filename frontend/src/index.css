body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubunt<PERSON>',
    'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.site-layout {
  min-height: 100vh;
}

.site-layout-background {
  background: #fff;
}

.logo {
  height: 32px;
  margin: 16px;
  color: white;
  text-align: center;
}

.ant-layout-sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}

.ant-layout-header {
  background: #fff;
  padding: 0;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.site-layout-content {
  min-height: 280px;
  padding: 24px;
  background: #fff;
}

.ant-row-rtl .logo {
  float: right;
  margin: 16px 0 16px 24px;
}

.editable-cell {
  position: relative;
}

.editable-cell-value-wrap {
  padding: 5px 12px;
  cursor: pointer;
}

.editable-row:hover .editable-cell-value-wrap {
  padding: 4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

[data-theme='dark'] .editable-row:hover .editable-cell-value-wrap {
  border: 1px solid #434343;
}

.chart-container {
  height: 400px;
  width: 100%;
}
