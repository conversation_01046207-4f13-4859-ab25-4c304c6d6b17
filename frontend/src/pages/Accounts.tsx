import { CloseOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Space, Table, Typography } from 'antd';
import { ColumnType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import { getAccounts, updateAccount } from '../services/api';
import { Account } from '../types'; // Import from your types file
import { formatCurrency } from '../utils/helpers';

import ClearFiltersButton from '../components/common/clearFiltersButton';
import { CurrencyInput } from '../components/common/CurrencyInput';
import { FormCard } from '../components/common/FormCard';
import { LoadingTable } from '../components/common/LoadingTable';
import { useTableControls } from '../hooks/useTableControls';

import { getColumnSearchProps, getNumericColumnSearchProps } from '../utils/tableFilters';

const { Title } = Typography;

const Accounts: React.FC = () => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [editingKey, setEditingKey] = useState<string | number>('');
  const [editForm] = Form.useForm();
  const [total, setTotal] = useState<number>(0);
  const { filteredInfo, sortedInfo, handleTableChange, clearFiltersAndSorting } =
    useTableControls();

  useEffect(() => {
    fetchAccounts();
  }, []);

  useEffect(() => {
    const sum = accounts.reduce((acc, account) => acc + account.balance, 0);
    setTotal(sum);
  }, [accounts]);

  const fetchAccounts = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getAccounts(); // Should return ApiResponse<Account[]>
      const accountsData = response.data;
      setAccounts(accountsData);
    } catch (error: any) {
      console.error('Error fetching accounts:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to load accounts';
      message.error(errorMessage);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  const isEditing = (record: Account): boolean => record.id === editingKey;

  const edit = (record: Account): void => {
    editForm.setFieldsValue({
      ...record,
      balance: record.balance,
    });
    setEditingKey(record.id);
  };

  const cancel = (): void => {
    setEditingKey('');
  };

  const save = async (record: Account): Promise<void> => {
    try {
      const row = await editForm.validateFields();
      //merge row over record
      const updatedAccount = { ...record, ...row };
      await updateAccount(updatedAccount);
      message.success('Account updated successfully');
      setEditingKey('');
      fetchAccounts();
    } catch (error: any) {
      console.error('Error updating account:', error);
      message.error(error.response?.data?.detail || 'Failed to update account');
    }
  };

  const columns: ColumnType<Account>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '25%',
      sorter: (a: Account, b: Account) => a.name.localeCompare(b.name),
      sortOrder: sortedInfo.columnKey === 'name' && sortedInfo.order,
      filteredValue: filteredInfo.name || null,
      ...getColumnSearchProps('name', 'Name'),
      render: (_: any, record: Account) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='name' rules={[{ required: true, message: 'Please enter a name' }]}>
            <Input />
          </Form.Item>
        ) : (
          record.name
        );
      },
    },
    {
      title: 'Type',
      dataIndex: 'account_type',
      key: 'account_type',
      width: '20%',
      sorter: (a: Account, b: Account) => a.account_type.localeCompare(b.account_type),
      sortOrder: sortedInfo.columnKey === 'account_type' && sortedInfo.order,
      filteredValue: filteredInfo.account_type || null,
      filters: [
        { text: 'Checking', value: 'Checking' },
        { text: 'Savings', value: 'Savings' },
        { text: 'Credit Card', value: 'Credit Card' },
        { text: 'Line of Credit', value: 'Line of Credit' },
      ],
      onFilter: (value: any, record: Account) => record.account_type === value,
      render: (_: any, record: Account) => record.account_type,
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      width: '20%',
      sorter: (a: Account, b: Account) => a.balance - b.balance,
      sortOrder: sortedInfo.columnKey === 'balance' && sortedInfo.order,
      filteredValue: filteredInfo.balance || null,
      ...getNumericColumnSearchProps('balance', true),
      render: (_: any, record: Account) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='balance' rules={[{ required: true, message: 'Please enter a balance' }]}>
            <CurrencyInput />
          </Form.Item>
        ) : (
          formatCurrency(record.balance)
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '20%',
      render: (_: any, record: Account) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button type='primary' icon={<SaveOutlined />} onClick={() => save(record)}>
              Save
            </Button>
            <Button icon={<CloseOutlined />} onClick={cancel}>
              Cancel
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              icon={<EditOutlined />}
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
            >
              Edit
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>Accounts</Title>

      <FormCard title='Accounts'>
        <Form form={editForm} component={false}>
          <ClearFiltersButton onClear={clearFiltersAndSorting} />
          <LoadingTable<Account>
            loading={loading}
            columns={columns}
            dataSource={accounts}
            rowKey='id'
            pagination={{ defaultPageSize: 10, showSizeChanger: true }}
            onChange={handleTableChange}
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2} align='right'>
                    <strong>Total Balance:</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>
                    <strong>{formatCurrency(total)}</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2} />
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </Form>
      </FormCard>
    </div>
  );
};

export default Accounts;
