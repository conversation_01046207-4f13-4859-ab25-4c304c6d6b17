import { CloseOutlined, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import {
  Tag as AntdTag,
  Button,
  Card,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { DATE_FORMAT } from '../constants/dateFormats';
import {
  addProject,
  createMilestone,
  createProjectExpense,
  createTag,
  deleteMilestone,
  deleteProject,
  deleteProjectExpense,
  getProjects,
  getTags,
  updateMilestone,
  updateProjectExpense,
} from '../services/api';
import {
  Milestone,
  Project,
  ProjectExpense,
  Tag,
  UpsertMilestone,
  UpsertProject,
  UpsertProjectExpense,
} from '../types';
import { formatCurrency, parseCurrency } from '../utils/helpers';

import ClearFiltersButton from '../components/common/clearFiltersButton';
import { useOrganization } from '../contexts/OrganizationContext';
import { useTableControls } from '../hooks/useTableControls';
import {
  getColumnSearchProps,
  getDateRangeSearchProps,
  getNumericColumnSearchProps,
  getTagColumnSearchProps,
} from '../utils/tableFilters';

const { Title } = Typography;

const CurrentProjects: React.FC = () => {
  const [form] = Form.useForm<UpsertProject>();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [expandedProjects, setExpandedProjects] = useState<Record<string, boolean>>({});
  const [editingMilestoneKey, setEditingMilestoneKey] = useState<string>('');
  const [editingExpenseKey, setEditingExpenseKey] = useState<string>('');
  const [milestoneForm] = Form.useForm();
  const [expenseForm] = Form.useForm();
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const { organization } = useOrganization();

  const currentProjectTableControls = useTableControls({
    initialSort: { columnKey: 'start_date', order: 'descend' },
  });
  // Add CSS styles for clickable rows

  // Create a separate component for expanded row content
  const ExpandedRowContent: React.FC<{ record: Project }> = ({ record }) => {
    const milestoneTableControls = useTableControls({
      initialSort: { columnKey: `milestone_date_${record.id}`, order: 'descend' },
    });
    const projectExpenseTableControls = useTableControls({
      initialSort: { columnKey: `project_expense_date_${record.id}`, order: 'descend' },
    });

    const milestoneColumns = [
      {
        title: 'Date',
        dataIndex: 'date',
        key: `milestone_date_${record.id}`,
        width: '15%',
        sorter: (a: Milestone, b: Milestone) => dayjs(a.date).unix() - dayjs(b.date).unix(),
        sortOrder:
          milestoneTableControls.sortedInfo.columnKey === `milestone_date_${record.id}`
            ? milestoneTableControls.sortedInfo.order
            : null,
        filteredValue: milestoneTableControls.filteredInfo[`milestone_date_${record.id}`] || null,
        ...getDateRangeSearchProps('date'),
        render: (text: string, milestone: Milestone) => {
          const editingKey = `${record.id}-milestone-${milestone.id}`;
          const isEditing = editingMilestoneKey === editingKey;

          return isEditing ? (
            <Form.Item
              name={`${editingKey}-date`}
              rules={[{ required: true, message: 'Please select a date' }]}
              initialValue={dayjs(text)}
            >
              <DatePicker style={{ width: '100%' }} format={DATE_FORMAT} />
            </Form.Item>
          ) : (
            dayjs(text).format(DATE_FORMAT)
          );
        },
      },
      {
        title: 'Description',
        dataIndex: 'description',
        key: `milestone_description_${record.id}`,
        width: '35%',
        sorter: (a: Milestone, b: Milestone) => a.description.localeCompare(b.description),
        sortOrder:
          milestoneTableControls.sortedInfo.columnKey === `milestone_description_${record.id}`
            ? milestoneTableControls.sortedInfo.order
            : null,
        filteredValue:
          milestoneTableControls.filteredInfo[`milestone_description_${record.id}`] || null,
        ...getColumnSearchProps('description'),
        render: (text: string, milestone: Milestone) => {
          const editingKey = `${record.id}-milestone-${milestone.id}`;
          const isEditing = editingMilestoneKey === editingKey;

          return isEditing ? (
            <Form.Item
              name={`${editingKey}-description`}
              rules={[{ required: true, message: 'Please enter a description' }]}
              initialValue={text}
            >
              <Input.TextArea rows={1} placeholder='Enter description' />
            </Form.Item>
          ) : (
            text
          );
        },
      },
      {
        title: 'Tags',
        dataIndex: 'tags',
        key: `milestone_tags_${record.id}`,
        width: '20%',
        ...getTagColumnSearchProps(allTags),
        filteredValue: milestoneTableControls.filteredInfo.tags || null,
        render: (tags: Tag[] | undefined, milestone: Milestone) => {
          const editingKey = `${record.id}-milestone-${milestone.id}`;
          const isEditing = editingMilestoneKey === editingKey;

          return isEditing ? (
            <Form.Item name={`${editingKey}-tags`}>
              <Select
                mode='tags'
                style={{ width: '100%' }}
                placeholder='Select or create tags'
                tokenSeparators={[',']}
                options={tagOptions}
              />
            </Form.Item>
          ) : (
            <>
              {(tags || []).map(tag => (
                <AntdTag key={tag.id}>{tag.name}</AntdTag>
              ))}
            </>
          );
        },
      },
      {
        title: 'Amount',
        dataIndex: 'amount',
        key: `milestone_amount_${record.id}`,
        width: '15%',
        sorter: (a: Milestone, b: Milestone) => parseCurrency(a.amount) - parseCurrency(b.amount),
        sortOrder:
          milestoneTableControls.sortedInfo.columnKey === `milestone_amount_${record.id}`
            ? milestoneTableControls.sortedInfo.order
            : null,
        filteredValue: milestoneTableControls.filteredInfo[`milestone_amount_${record.id}`] || null,
        ...getNumericColumnSearchProps('amount', true),
        render: (text: string | number, milestone: Milestone) => {
          const editingKey = `${record.id}-milestone-${milestone.id}`;
          const isEditing = editingMilestoneKey === editingKey;

          return isEditing ? (
            <Form.Item
              name={`${editingKey}-amount`}
              rules={[{ required: true, message: 'Please enter an amount' }]}
              initialValue={text}
            >
              <InputNumber
                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\$\s?|(,*)/g, '')}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : (
            formatCurrency(text)
          );
        },
      },
      {
        title: 'Actions',
        key: 'actions',
        width: '15%',
        render: (_: any, milestone: Milestone): React.ReactNode => {
          const editingKey = `${record.id}-milestone-${milestone.id}`;
          const isEditing = editingMilestoneKey === editingKey;

          return isEditing ? (
            <Space>
              <Button
                icon={<SaveOutlined />}
                type='primary'
                onClick={() => handleSaveMilestone(record.id, milestone.id)}
                size='small'
              >
                Save
              </Button>
              <Button
                icon={<CloseOutlined />}
                onClick={() => handleCancelMilestone(record.id, milestone.id)}
                size='small'
              >
                Cancel
              </Button>
            </Space>
          ) : (
            <Space>
              <Button
                icon={<EditOutlined />}
                type='primary'
                size='small'
                disabled={editingMilestoneKey !== '' || editingExpenseKey !== ''}
                onClick={() => {
                  setEditingMilestoneKey(editingKey);

                  milestoneForm.setFieldsValue({
                    [`${editingKey}-date`]: dayjs(milestone.date),
                    [`${editingKey}-description`]: milestone.description,
                    [`${editingKey}-amount`]: String(milestone.amount),
                    [`${editingKey}-tags`]: milestone.tags
                      ? milestone.tags.map(tag => tag.name)
                      : [],
                  });
                }}
              >
                Edit
              </Button>
              <Button
                danger
                icon={<DeleteOutlined />}
                size='small'
                disabled={editingMilestoneKey !== '' || editingExpenseKey !== ''}
                onClick={() => handleDeleteMilestone(record.id, milestone.id)}
              >
                Delete
              </Button>
            </Space>
          );
        },
      },
    ];

    const projectExpenseColumns = [
      {
        title: 'Date',
        dataIndex: 'date',
        key: `project_expense_date_${record.id}`,
        width: '12%',
        sorter: (a: ProjectExpense, b: ProjectExpense) =>
          dayjs(a.date).unix() - dayjs(b.date).unix(),
        sortOrder:
          projectExpenseTableControls.sortedInfo.columnKey === `project_expense_date_${record.id}`
            ? projectExpenseTableControls.sortedInfo.order
            : null,
        filteredValue:
          projectExpenseTableControls.filteredInfo[`project_expense_date_${record.id}`] || null,
        ...getDateRangeSearchProps('date'),
        render: (text: string, expense: ProjectExpense) => {
          const editingKey = `${record.id}-expense-${expense.id}`;
          const isEditing = editingExpenseKey === editingKey;

          return isEditing ? (
            <Form.Item
              name={`${editingKey}-date`}
              rules={[{ required: true, message: 'Please select a date' }]}
              initialValue={dayjs(text)}
            >
              <DatePicker style={{ width: '100%' }} format={DATE_FORMAT} />
            </Form.Item>
          ) : (
            dayjs(text).format(DATE_FORMAT)
          );
        },
      },
      {
        title: 'Description',
        dataIndex: 'description',
        key: `project_expense_description_${record.id}`,
        width: '25%',
        sorter: (a: ProjectExpense, b: ProjectExpense) =>
          a.description.localeCompare(b.description),
        sortOrder:
          projectExpenseTableControls.sortedInfo.columnKey ===
          `project_expense_description_${record.id}`
            ? projectExpenseTableControls.sortedInfo.order
            : null,
        filteredValue:
          projectExpenseTableControls.filteredInfo[`project_expense_description_${record.id}`] ||
          null,
        ...getColumnSearchProps('description'),
        render: (text: string, expense: ProjectExpense) => {
          const editingKey = `${record.id}-expense-${expense.id}`;
          const isEditing = editingExpenseKey === editingKey;

          return isEditing ? (
            <Form.Item
              name={`${editingKey}-description`}
              rules={[{ required: true, message: 'Please enter a description' }]}
              initialValue={text}
            >
              <Input.TextArea rows={1} placeholder='Enter description' />
            </Form.Item>
          ) : (
            text
          );
        },
      },
      {
        title: 'Expense Category',
        dataIndex: 'category_tag',
        key: `project_category_${record.id}`,
        width: '18%',
        ...getTagColumnSearchProps(allTags),
        filteredValue: projectExpenseTableControls.filteredInfo.category_tag || null,
        render: (category_tag: Tag | undefined, expense: ProjectExpense) => {
          const editingKey = `${record.id}-expense-${expense.id}`;
          const isEditing = editingExpenseKey === editingKey;

          return isEditing ? (
            <Form.Item
              name={`${editingKey}-category_tag_name`}
              rules={[{ required: true, message: 'Please select an expense category' }]}
              initialValue={category_tag?.name || ''}
            >
              <Select
                mode='tags'
                maxCount={1}
                showSearch
                style={{ width: '100%' }}
                placeholder='Select or create expense category'
                tokenSeparators={[',']}
                options={tagOptions}
                filterOption={(inputValue, option) =>
                  option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                }
              />
            </Form.Item>
          ) : category_tag ? (
            <AntdTag color='blue'>{category_tag.name}</AntdTag>
          ) : (
            <span style={{ color: '#999' }}>No category</span>
          );
        },
      },
      {
        title: 'Tags',
        dataIndex: 'tags',
        key: `project_expense_tags_${record.id}`,
        width: '15%',
        ...getTagColumnSearchProps(allTags),
        filteredValue: projectExpenseTableControls.filteredInfo.tags || null,
        render: (tags: Tag[] | undefined, expense: ProjectExpense) => {
          const editingKey = `${record.id}-expense-${expense.id}`;
          const isEditing = editingExpenseKey === editingKey;

          return isEditing ? (
            <Form.Item name={`${editingKey}-tags`}>
              <Select
                mode='tags'
                style={{ width: '100%' }}
                placeholder='Select or create tags'
                tokenSeparators={[',']}
                options={tagOptions}
              />
            </Form.Item>
          ) : (
            <>
              {(tags || []).map(tag => (
                <AntdTag key={tag.id}>{tag.name}</AntdTag>
              ))}
            </>
          );
        },
      },
      {
        title: 'Amount',
        dataIndex: 'amount',
        key: `project_expense_amount_${record.id}`,
        width: '12%',
        sorter: (a: ProjectExpense, b: ProjectExpense) =>
          parseCurrency(a.amount) - parseCurrency(b.amount),
        sortOrder:
          projectExpenseTableControls.sortedInfo.columnKey === `project_expense_amount_${record.id}`
            ? projectExpenseTableControls.sortedInfo.order
            : null,
        filteredValue:
          projectExpenseTableControls.filteredInfo[`project_expense_amount_${record.id}`] || null,
        ...getNumericColumnSearchProps('amount', true),
        render: (text: string | number, expense: ProjectExpense) => {
          const editingKey = `${record.id}-expense-${expense.id}`;
          const isEditing = editingExpenseKey === editingKey;

          return isEditing ? (
            <Form.Item
              name={`${editingKey}-amount`}
              rules={[{ required: true, message: 'Please enter an amount' }]}
              initialValue={text}
            >
              <InputNumber
                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\$\s?|(,*)/g, '')}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : (
            formatCurrency(text)
          );
        },
      },
      {
        title: 'Actions',
        key: 'actions',
        width: '18%',
        render: (_: any, expense: ProjectExpense): React.ReactNode => {
          const editingKey = `${record.id}-expense-${expense.id}`;
          const isEditing = editingExpenseKey === editingKey;

          return isEditing ? (
            <Space>
              <Button
                icon={<SaveOutlined />}
                type='primary'
                onClick={() => handleSaveExpense(record.id, expense.id)}
                size='small'
              >
                Save
              </Button>
              <Button
                icon={<CloseOutlined />}
                onClick={() => handleCancelExpense(record.id, expense.id)}
                size='small'
              >
                Cancel
              </Button>
            </Space>
          ) : (
            <Space>
              <Button
                icon={<EditOutlined />}
                type='primary'
                size='small'
                disabled={editingMilestoneKey !== '' || editingExpenseKey !== ''}
                onClick={() => handleEditExpense(record.id, expense.id, expense)}
              >
                Edit
              </Button>
              <Button
                danger
                icon={<DeleteOutlined />}
                size='small'
                disabled={editingMilestoneKey !== '' || editingExpenseKey !== ''}
                onClick={() => handleDeleteProjectExpense(record.id, expense.id)}
              >
                Delete
              </Button>
            </Space>
          );
        },
      },
    ];

    return (
      <>
        <Title level={4}>Milestones</Title>
        <Form form={milestoneForm} component={false}>
          <ClearFiltersButton onClear={milestoneTableControls.clearFiltersAndSorting} />
          <Table
            columns={milestoneColumns}
            dataSource={record.milestones}
            pagination={false}
            onChange={milestoneTableControls.handleTableChange}
            rowKey={milestone =>
              milestone.key || `${record.id}-milestone-${milestone.id || Date.now()}`
            }
          />
        </Form>
        <Button
          type='primary'
          style={{ margin: '16px 0' }}
          onClick={() => handleAddMilestoneToProject(record.id)}
        >
          Add Milestone
        </Button>

        <Divider />

        <Title level={4}>Expenses</Title>
        <Form form={expenseForm} component={false}>
          <ClearFiltersButton onClear={projectExpenseTableControls.clearFiltersAndSorting} />
          <Table
            columns={projectExpenseColumns}
            dataSource={record.project_expenses}
            pagination={false}
            onChange={projectExpenseTableControls.handleTableChange}
            rowKey={expense => expense.key || `${record.id}-expense-${expense.id || Date.now()}`}
          />
        </Form>
        <Button
          type='primary'
          style={{ margin: '16px 0' }}
          onClick={() => handleAddExpenseToProject(record.id)}
        >
          Add Project Expense
        </Button>
      </>
    );
  };

  useEffect(() => {
    console.log('Fetching projects in useEffect');
    fetchProjects();
    fetchAllTags();
  }, []);

  // Add CSS for table cell alignment
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .current-projects-table .ant-table-tbody > tr > td {
        vertical-align: top !important;
        padding-top: 8px !important;
      }
      .current-projects-table .ant-form-item {
        margin-bottom: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const fetchAllTags = async () => {
    try {
      const response = await getTags();
      const sortedTags = response.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setAllTags(sortedTags);
      setTagOptions(
        sortedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      message.error('Failed to load tags');
    }
  };

  const fetchProjects = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getProjects();
      const projectsData = response.data.map((project: any) => ({
        ...project,
        id: Number(project.id),
        key: String(project.id),
        start_date: dayjs(project.start_date),
        milestones: project.milestones?.map((m: any) => ({ ...m, date: dayjs(m.date) })) || [],
        project_expenses:
          project.project_expenses?.map((e: any) => ({ ...e, date: dayjs(e.date) })) || [],
      }));
      setProjects(projectsData);
    } catch (error) {
      console.error('Error fetching projects:', error);
      message.error('Failed to load projects');
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: UpsertProject): Promise<void> => {
    try {
      setSubmitting(true);
      const tagIds = await processTags(values.tag_names);
      const payload: UpsertProject = {
        id: 0,
        name: values.name,
        description: values.description,
        start_date: values.start_date,
        duration: values.duration,
        savings_percentage: values.savings_percentage,
        tag_ids: tagIds,
        status: 'current',
        archived: false,
        active: true,
      };

      await addProject(payload);
      message.success('Project added successfully');
      form.resetFields();
      fetchProjects();
    } catch (error) {
      console.error('Error adding project:', error);
      message.error('Failed to add project');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: number): Promise<void> => {
    try {
      await deleteProject(id);
      message.success('Project deleted successfully');
      fetchProjects();
    } catch (error) {
      console.error('Error deleting project:', error);
      message.error('Failed to delete project');
    }
  };

  const handleDeleteMilestone = async (projectId: number, milestoneId: number): Promise<void> => {
    try {
      const project = projects.find(p => p.id === projectId);
      if (!project) return;

      const milestone = project.milestones.find(m => m.id === milestoneId);
      if (!milestone || !milestone.id) return;

      await deleteMilestone(String(milestone.id));
      message.success('Milestone deleted successfully');
      fetchProjects();
    } catch (error) {
      console.error('Error deleting milestone:', error);
      message.error('Failed to delete milestone');
    }
  };

  const handleDeleteProjectExpense = async (
    projectId: number,
    expenseId: number
  ): Promise<void> => {
    try {
      const project = projects.find(p => p.id === projectId);
      if (!project) return;

      const projectExpense = project.project_expenses.find(e => e.id === expenseId);
      if (!projectExpense || !projectExpense.id) return;

      await deleteProjectExpense(String(projectExpense.id));
      message.success('Project expense deleted successfully');
      fetchProjects();
    } catch (error) {
      console.error('Error deleting project expense:', error);
      message.error('Failed to delete project expense');
    }
  };

  const toggleProjectExpand = (id: string): void => {
    setExpandedProjects(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const calculateTotalAmount = (project: Project): number => {
    const milestonesTotal = project.milestones.reduce(
      (sum, milestone) => sum + parseCurrency(milestone.amount),
      0
    );
    const projectExpenseTotal = project.project_expenses.reduce(
      (sum, cost) => sum + parseCurrency(cost.amount),
      0
    );
    return milestonesTotal + projectExpenseTotal;
  };

  const handleAddMilestoneToProject = async (projectId: number) => {
    const newMilestone: Milestone = {
      project_id: projectId,
      date: dayjs(),
      description: '',
      amount: 0,
      id: 0,
      key: `${projectId}-milestone-${Date.now()}`, // Unique key for the row
      tags: [],
      type: 'milestone',
    };

    // Add to project's milestones
    const updatedProjects = projects.map(project => {
      if (project.id === projectId) {
        return {
          ...project,
          milestones: [...project.milestones, newMilestone],
        };
      }
      return project;
    });
    setProjects(updatedProjects);

    // Find the project
    const project = projects.find(p => p.id === projectId);
    if (!project) return;

    // Put the new milestone in edit mode (use temporary ID for new items)
    const tempId = Date.now();
    const editingKey = `${projectId}-milestone-${tempId}`;
    setEditingMilestoneKey(editingKey);

    // Update the milestone with the temp ID for consistency
    newMilestone.id = tempId;
    newMilestone.key = `${projectId}-milestone-${tempId}`;

    // Set form values for the new milestone
    milestoneForm.setFieldsValue({
      [`${editingKey}-date`]: dayjs(),
      [`${editingKey}-description`]: '',
      [`${editingKey}-amount`]: 0,
      [`${editingKey}-tags`]: [],
    });
  };

  const handleAddExpenseToProject = async (projectId: number) => {
    const newProjectExpense: ProjectExpense = {
      project_id: projectId,
      date: dayjs(),
      description: '',
      amount: 0,
      id: 0,
      key: `${projectId}-expense-${Date.now()}`, // Unique key for the row
      tags: [],
      category_tag_id: 0, // Will be set when saved
      type: 'projectExpense',
    };

    // Add to project's expenses
    const updatedProjects = projects.map(project => {
      if (project.id === projectId) {
        return {
          ...project,
          project_expenses: [...project.project_expenses, newProjectExpense],
        };
      }
      return project;
    });
    setProjects(updatedProjects);

    // Find the project
    const project = projects.find(p => p.id === projectId);
    if (!project) return;

    // Put the new expense in edit mode (use temporary ID for new items)
    const tempId = Date.now();
    const editingKey = `${projectId}-expense-${tempId}`;
    setEditingExpenseKey(editingKey);

    // Update the expense with the temp ID for consistency
    newProjectExpense.id = tempId;
    newProjectExpense.key = `${projectId}-expense-${tempId}`;

    // Set form values for the new expense
    expenseForm.setFieldsValue({
      [`${editingKey}-date`]: dayjs(),
      [`${editingKey}-description`]: '',
      [`${editingKey}-amount`]: 0,
      [`${editingKey}-tags`]: [],
      [`${editingKey}-category_tag_name`]: [],
    });
  };

  const handleCancelMilestone = (projectId: number, milestoneId: number) => {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;

    const milestone = project.milestones.find(m => m.id === milestoneId);
    if (!milestone) return;

    // Remove editing key
    setEditingMilestoneKey('');

    // If this was a new unsaved milestone (temporary ID), remove it from the state
    if (milestone.id > 1000000000000) {
      const updatedProjects = projects.map(p => {
        if (p.id === projectId) {
          return {
            ...p,
            milestones: p.milestones.filter(m => m.id !== milestone.id),
          };
        }
        return p;
      });
      setProjects(updatedProjects);
    }
  };

  const handleCancelExpense = (projectId: number, expenseId: number) => {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;

    const expense = project.project_expenses.find(e => e.id === expenseId);
    if (!expense) return;

    // Remove editing key
    setEditingExpenseKey('');

    // If this was a new unsaved expense (temporary ID), remove it from the state
    if (expense.id > 1000000000000) {
      const updatedProjects = projects.map(p => {
        if (p.id === projectId) {
          return {
            ...p,
            project_expenses: p.project_expenses.filter(e => e.id !== expense.id),
          };
        }
        return p;
      });
      setProjects(updatedProjects);
    }
  };

  const handleSaveMilestone = async (projectId: number, milestoneId: number): Promise<void> => {
    try {
      const project = projects.find(p => p.id === projectId);
      if (!project) return;

      const milestoneIndex = project.milestones.findIndex(m => m.id === milestoneId);
      if (milestoneIndex === -1) return;

      const milestone = project.milestones[milestoneIndex];
      const editingKey = `${projectId}-milestone-${milestoneId}`;

      const values = await milestoneForm.validateFields([
        `${editingKey}-date`,
        `${editingKey}-description`,
        `${editingKey}-amount`,
        `${editingKey}-tags`,
      ]);
      const tagIds = await processTags(values[`${editingKey}-tags`]);

      const updatedMilestone: UpsertMilestone = {
        project_id: projectId,
        date: values[`${editingKey}-date`],
        description: values[`${editingKey}-description`],
        amount: parseCurrency(values[`${editingKey}-amount`]),
        tag_ids: tagIds,
        type: 'milestone',
        id: milestone.id as number,
      };

      let savedMilestone: Milestone;
      // Check if this is a new milestone (temporary ID > 1000000000000 indicates timestamp)
      if (milestone.id > 1000000000000) {
        // This is a new milestone, create it using the form values
        const response = await createMilestone(updatedMilestone);
        savedMilestone = response.data;
        message.success('Milestone created successfully');
      } else {
        // This is an existing milestone, update it
        const response = await updateMilestone(updatedMilestone);
        savedMilestone = response.data;
        message.success('Milestone updated successfully');
      }

      // Update the projects state directly
      const updatedProjects = projects.map(p => {
        if (p.id === projectId) {
          const updatedMilestones = [...p.milestones];
          updatedMilestones[milestoneIndex] = {
            ...savedMilestone,
            key: `${projectId}-milestone-${milestoneIndex}`,
          };
          return {
            ...p,
            milestones: updatedMilestones,
          };
        }
        return p;
      });
      setProjects(updatedProjects);

      setEditingMilestoneKey('');
    } catch (error) {
      console.error('Error updating milestone:', error);
      message.error('Failed to update milestone');
    }
  };

  const handleSaveExpense = async (projectId: number, expenseId: number): Promise<void> => {
    try {
      const project = projects.find(p => p.id === projectId);
      if (!project) return;

      const expenseIndex = project.project_expenses.findIndex(e => e.id === expenseId);
      if (expenseIndex === -1) return;

      const editingKey = `${projectId}-expense-${expenseId}`;
      const values = await expenseForm.validateFields([
        `${editingKey}-date`,
        `${editingKey}-description`,
        `${editingKey}-amount`,
        `${editingKey}-tags`,
        `${editingKey}-category_tag_name`,
      ]);

      const expense = project.project_expenses[expenseIndex];
      const tagIds = await processTags(values[`${editingKey}-tags`]);
      const categoryTagId = await processCategoryTag(values[`${editingKey}-category_tag_name`][0]);

      const updatedExpenseData: UpsertProjectExpense = {
        project_id: projectId,
        date: values[`${editingKey}-date`],
        description: values[`${editingKey}-description`],
        amount: parseCurrency(values[`${editingKey}-amount`]),
        tag_ids: tagIds,
        category_tag_id: categoryTagId,
        type: 'projectExpense',
        id: expense.id as number,
      };

      let savedExpense: ProjectExpense;
      // Check if this is a new expense (temporary ID > 1000000000000 indicates timestamp)
      if (expense.id > 1000000000000) {
        // This is a new expense, create it using the form values
        const response = await createProjectExpense(updatedExpenseData);
        savedExpense = response.data;
        message.success('Project expense created successfully');
      } else {
        // This is an existing expense, update it
        const response = await updateProjectExpense(updatedExpenseData);
        savedExpense = response.data;
        message.success('Project expense updated successfully');
      }

      // Update the projects state directly
      const updatedProjects = projects.map(p => {
        if (p.id === projectId) {
          const updatedProjectExpenses = [...p.project_expenses];
          updatedProjectExpenses[expenseIndex] = {
            ...savedExpense,
            key: `${projectId}-expense-${expenseIndex}`,
          };
          return {
            ...p,
            project_expenses: updatedProjectExpenses,
          };
        }
        return p;
      });
      setProjects(updatedProjects);

      setEditingExpenseKey('');
    } catch (error) {
      console.error('Error updating expense:', error);
      message.error('Failed to update expense');
    }
  };

  const handleEditExpense = (projectId: number, expenseId: number, expense: ProjectExpense) => {
    const editingKey = `${projectId}-expense-${expenseId}`;
    setEditingExpenseKey(editingKey);

    // Set form values
    expenseForm.setFieldsValue({
      [`${editingKey}-date`]: dayjs(expense.date),
      [`${editingKey}-description`]: expense.description,
      [`${editingKey}-amount`]: expense.amount,
      [`${editingKey}-tags`]: expense.tags ? expense.tags.map(tag => tag.name) : [],
      [`${editingKey}-category_tag_name`]: expense.category_tag ? [expense.category_tag.name] : [],
    });
  };

  const processTags = async (tags: string[] | undefined): Promise<number[]> => {
    if (!organization) {
      message.error('Organization not found');
      return [];
    }
    try {
      if (!tags) {
        return [];
      }
      const tagIds: number[] = [];
      const newTags: Tag[] = [];
      for (const tagName of tags) {
        const existingTag = allTags.find(tag => tag.name === tagName);
        if (existingTag) {
          tagIds.push(existingTag.id);
        } else {
          const trimmedTagName = tagName.trim();
          const response = await createTag({
            id: 0,
            name: trimmedTagName,
            organization_id: organization.id,
          });
          const tagObject = response.data;
          if (tagObject) {
            tagIds.push(tagObject.id);
            newTags.push(tagObject);
          }
        }
      }

      const updatedTags = [...allTags, ...newTags].sort((a, b) => a.name.localeCompare(b.name));
      setAllTags(updatedTags);
      setTagOptions(
        updatedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );

      return tagIds;
    } catch (error) {
      console.error('Error processing tags:', error);
      message.error('Failed to process tags');
      return [];
    }
  };

  const processCategoryTag = async (tagName: string): Promise<number> => {
    if (!organization) {
      message.error('Organization not found');
      return 0;
    }
    const trimmedTagName = tagName?.trim();
    if (!trimmedTagName) {
      return 0;
    }
    try {
      const existingTag = allTags.find(tag => tag.name === trimmedTagName);
      if (existingTag) {
        return existingTag.id;
      } else {
        const response = await createTag({
          id: 0,
          name: trimmedTagName,
          organization_id: organization.id,
        });
        const tagObject = response.data;
        if (tagObject) {
          return tagObject.id;
        }
      }
      return 0;
    } catch (error) {
      console.error('Error processing expense category tag:', error);
      message.error('Failed to process expense category tag');
      return 0;
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      sorter: (a: Project, b: Project) => a.name.localeCompare(b.name),
      ...getColumnSearchProps('name'),
      sortOrder:
        currentProjectTableControls.sortedInfo.columnKey === 'name'
          ? currentProjectTableControls.sortedInfo.order
          : undefined,
      filteredValue: currentProjectTableControls.filteredInfo.name || null,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '25%',
      ...getColumnSearchProps('description'),
      sortOrder:
        currentProjectTableControls.sortedInfo.columnKey === 'description'
          ? currentProjectTableControls.sortedInfo.order
          : undefined,
      filteredValue: currentProjectTableControls.filteredInfo.description || null,
      render: (text: string) => text || '-',
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      width: '15%',
      ...getTagColumnSearchProps(allTags),
      filteredValue: currentProjectTableControls.filteredInfo.tags || null,
      render: (tags: Tag[] | undefined) => (
        <>
          {(tags || []).map(tag => (
            <AntdTag key={tag.id}>{tag.name}</AntdTag>
          ))}
        </>
      ),
    },
    {
      title: 'Start Date',
      dataIndex: 'start_date',
      key: 'start_date',
      width: '12%',
      sorter: (a: Project, b: Project) => dayjs(a.start_date).unix() - dayjs(b.start_date).unix(),
      ...getDateRangeSearchProps('start_date'),
      sortOrder:
        currentProjectTableControls.sortedInfo.columnKey === 'start_date'
          ? currentProjectTableControls.sortedInfo.order
          : undefined,
      filteredValue: currentProjectTableControls.filteredInfo.start_date || null,
      render: (text: string) => dayjs(text).format(DATE_FORMAT),
      defaultSortOrder: 'descend' as const,
    },
    {
      title: 'Duration (days)',
      dataIndex: 'duration',
      key: 'duration',
      width: '10%',
      sorter: (a: Project, b: Project) => a.duration - b.duration,
      ...getNumericColumnSearchProps('duration', false),
      sortOrder:
        currentProjectTableControls.sortedInfo.columnKey === 'duration'
          ? currentProjectTableControls.sortedInfo.order
          : undefined,
      filteredValue: currentProjectTableControls.filteredInfo.duration || null,
    },
    {
      title: 'Savings %',
      dataIndex: 'savings_percentage',
      key: 'savings_percentage',
      width: '10%',
      sorter: (a: Project, b: Project) => a.savings_percentage - b.savings_percentage,
      ...getNumericColumnSearchProps('savings_percentage', false),
      sortOrder:
        currentProjectTableControls.sortedInfo.columnKey === 'savings_percentage'
          ? currentProjectTableControls.sortedInfo.order
          : undefined,
      filteredValue: currentProjectTableControls.filteredInfo.savings_percentage || null,
      render: (text: number) => `${text}%`,
    },
    {
      title: 'Amount',
      key: 'amount',
      width: '10%',
      sorter: (a: Project, b: Project) => calculateTotalAmount(a) - calculateTotalAmount(b),
      ...getNumericColumnSearchProps('amount', true),
      sortOrder:
        currentProjectTableControls.sortedInfo.columnKey === 'amount'
          ? currentProjectTableControls.sortedInfo.order
          : undefined,
      filteredValue: currentProjectTableControls.filteredInfo.amount || null,
      render: (_: any, record: Project) => formatCurrency(calculateTotalAmount(record)),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '8%',
      render: (_: any, record: Project) => (
        <Space>
          <Popconfirm
            title='Are you sure you want to delete this project?'
            onConfirm={() => handleDelete(record.id)}
            okText='Yes'
            cancelText='No'
          >
            <Button danger icon={<DeleteOutlined />} size='small'>
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const expandedRowRender = (record: Project) => {
    return <ExpandedRowContent record={record} />;
  };

  return (
    <div>
      <Title level={2}>Current Projects</Title>

      <Card title='Add Project' style={{ marginBottom: 16 }}>
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
            <Form.Item
              name='name'
              label='Project Name'
              rules={[{ required: true, message: 'Please enter a project name' }]}
              style={{ flex: 1, minWidth: '200px' }}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name='description'
              label='Description'
              style={{ flex: 2, minWidth: '300px' }}
            >
              <Input.TextArea rows={1} placeholder='Enter description' />
            </Form.Item>

            <Form.Item
              name='start_date'
              label='Start Date'
              rules={[{ required: true, message: 'Please select a start date' }]}
              style={{ flex: 1, minWidth: '200px' }}
            >
              <DatePicker format={DATE_FORMAT} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name='duration'
              label='Duration (days)'
              rules={[{ required: true, message: 'Please enter duration' }]}
              style={{ flex: 1, minWidth: '150px' }}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name='savings_percentage'
              label='Savings %'
              initialValue={0}
              style={{ flex: 1, minWidth: '150px' }}
            >
              <InputNumber min={0} max={100} step={0.1} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item name='tag_names' label='Tags' style={{ flex: 1, minWidth: '200px' }}>
              <Select
                mode='tags'
                style={{ width: '100%' }}
                placeholder='Select or create tags'
                tokenSeparators={[',']}
                options={tagOptions}
              />
            </Form.Item>

            <Form.Item
              style={{
                alignSelf: 'flex-end',
                marginBottom: '0',
                flex: 1,
                minWidth: '150px',
              }}
            >
              <Button
                type='primary'
                htmlType='submit'
                loading={submitting}
                style={{ width: '100%', marginBottom: '24px' }}
              >
                Add Project
              </Button>
            </Form.Item>
          </div>
        </Form>
      </Card>

      <Card title='Current Projects'>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size='large' />
          </div>
        ) : (
          <>
            <ClearFiltersButton onClear={currentProjectTableControls.clearFiltersAndSorting} />
            <Table
              columns={columns}
              dataSource={projects}
              rowKey={record => record.id.toString()}
              expandable={{
                expandedRowRender,
                expandedRowKeys: Object.keys(expandedProjects).filter(key => expandedProjects[key]),
                onExpand: (_expanded, record) => toggleProjectExpand(record.id.toString()),
              }}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
              }}
              loading={loading}
              scroll={{ x: 'max-content' }}
              onChange={currentProjectTableControls.handleTableChange}
              className='current-projects-table'
              summary={pageData => {
                const total = pageData.reduce(
                  (sum, project) => sum + calculateTotalAmount(project),
                  0
                );
                return (
                  <Table.Summary fixed>
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0} colSpan={6} align='right'>
                        <strong>Total:</strong>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={1}>
                        <strong>{formatCurrency(total)}</strong>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  </Table.Summary>
                );
              }}
            />
          </>
        )}
      </Card>
    </div>
  );
};

export default CurrentProjects;
