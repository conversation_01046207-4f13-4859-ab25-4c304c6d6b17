import {
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { Button, Form, Input, message, Modal, Space, Typography } from 'antd';
import { ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { DATE_FORMAT } from '../constants/dateFormats';
import ClearFiltersButton from '../components/common/clearFiltersButton';
import { FormCard } from '../components/common/FormCard';
import { LoadingTable } from '../components/common/LoadingTable';
import { useOrganization } from '../contexts/OrganizationContext';
import { useTableControls } from '../hooks/useTableControls';
import { createTag, deleteTag, getTags, updateTag } from '../services/api';
import { Tag } from '../types';
import { getColumnSearchProps, getDateRangeSearchProps } from '../utils/tableFilters';

const { Title } = Typography;

const Tags: React.FC = () => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [editingKey, setEditingKey] = useState<string | number>('');
  const [editForm] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [addForm] = Form.useForm();
  const { filteredInfo, sortedInfo, handleTableChange, clearFiltersAndSorting } =
    useTableControls();
  const { organization } = useOrganization();

  useEffect(() => {
    fetchTags();
  }, []);

  // Add CSS for table cell alignment
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .tags-table .ant-table-tbody > tr > td {
        vertical-align: top !important;
        padding-top: 8px !important;
      }
      .tags-table .ant-form-item {
        margin-bottom: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const fetchTags = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getTags();
      setTags(response.data);
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to load tags';
      message.error(errorMessage);
      setTags([]);
    } finally {
      setLoading(false);
    }
  };

  const isEditing = (record: Tag): boolean => record.id === editingKey;

  const edit = (record: Tag): void => {
    editForm.setFieldsValue({ ...record });
    setEditingKey(record.id);
  };

  const cancel = (): void => {
    setEditingKey('');
  };

  const save = async (record: Tag): Promise<void> => {
    try {
      const row = await editForm.validateFields();
      const updatedTag = { ...record, ...row };
      await updateTag(updatedTag);
      message.success('Tag updated successfully');
      setEditingKey('');
      fetchTags();
    } catch (error: any) {
      console.error('Error updating tag:', error);
      message.error(error.response?.data?.detail || 'Failed to update tag');
    }
  };

  const handleDelete = async (id: number): Promise<void> => {
    try {
      await deleteTag(id);
      message.success('Tag deleted successfully');
      fetchTags();
    } catch (error: any) {
      console.error('Error deleting tag:', error);
      message.error(error.response?.data?.detail || 'Failed to delete tag');
    }
  };

  const showAddModal = () => {
    setIsModalVisible(true);
  };

  const handleAddOk = async () => {
    try {
      const values = await addForm.validateFields();
      if (!organization) {
        message.error('Organization not found.');
        return;
      }
      await createTag({ id: 0, name: values.name, organization_id: organization.id });
      message.success('Tag created successfully');
      setIsModalVisible(false);
      addForm.resetFields();
      fetchTags();
    } catch (error: any) {
      console.error('Error creating tag:', error);
      message.error(error.response?.data?.detail || 'Failed to create tag');
    }
  };

  const handleAddCancel = () => {
    setIsModalVisible(false);
    addForm.resetFields();
  };

  const columns: ColumnType<Tag>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '25%',
      sorter: (a: Tag, b: Tag) => a.name.localeCompare(b.name),
      sortOrder: sortedInfo.columnKey === 'name' && sortedInfo.order,
      filteredValue: filteredInfo.name || null,
      ...getColumnSearchProps('name', 'Name'),
      render: (_: any, record: Tag) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='name' rules={[{ required: true, message: 'Please enter a name' }]}>
            <Input />
          </Form.Item>
        ) : (
          record.name
        );
      },
    },
    {
      title: 'Created At',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: (a: Tag, b: Tag) => dayjs(a.created_at).unix() - dayjs(b.created_at).unix(),
      sortOrder: sortedInfo.columnKey === 'created_at' && sortedInfo.order,
      filteredValue: filteredInfo.created_at || null,
      ...getDateRangeSearchProps('created_at'),
      render: (date: dayjs.Dayjs) => date.format(DATE_FORMAT),
    },
    {
      title: 'Updated At',
      dataIndex: 'updated_at',
      key: 'updated_at',
      sorter: (a: Tag, b: Tag) => dayjs(a.updated_at).unix() - dayjs(b.updated_at).unix(),
      sortOrder: sortedInfo.columnKey === 'updated_at' && sortedInfo.order,
      filteredValue: filteredInfo.updated_at || null,
      ...getDateRangeSearchProps('updated_at'),
      render: (date: dayjs.Dayjs) => date.format(DATE_FORMAT),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '20%',
      render: (_: any, record: Tag) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              icon={<SaveOutlined />}
              type='primary'
              onClick={() => save(record)}
              size='small'
            >
              Save
            </Button>
            <Button icon={<CloseOutlined />} onClick={cancel} size='small'>
              Cancel
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              icon={<EditOutlined />}
              type='primary'
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
              size='small'
            >
              Edit
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
              size='small'
            >
              Delete
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>Tags</Title>
      <FormCard title='Manage Tags'>
        <Button
          type='primary'
          icon={<PlusOutlined />}
          onClick={showAddModal}
          style={{ marginBottom: 16 }}
        >
          Add Tag
        </Button>
        <Form form={editForm} component={false}>
          <ClearFiltersButton onClear={clearFiltersAndSorting} />
          <LoadingTable<Tag>
            loading={loading}
            columns={columns}
            dataSource={tags}
            rowKey='id'
            pagination={{ defaultPageSize: 10, showSizeChanger: true }}
            onChange={handleTableChange}
            className='tags-table'
          />
        </Form>
      </FormCard>
      <Modal
        title='Add New Tag'
        visible={isModalVisible}
        onOk={handleAddOk}
        onCancel={handleAddCancel}
      >
        <Form form={addForm} layout='vertical'>
          <Form.Item
            name='name'
            label='Tag Name'
            rules={[{ required: true, message: 'Please enter the tag name' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Tags;
