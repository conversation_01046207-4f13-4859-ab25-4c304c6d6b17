import { Button, Card, Typography } from 'antd';
import React from 'react';
import { Link } from 'react-router-dom';
import { InviteUserForm } from '../components/InviteUserForm';
import { useOrganization } from '../contexts/OrganizationContext';

const { Title, Paragraph } = Typography;

export const OrganizationSettings: React.FC = () => {
  const { organization } = useOrganization();

  if (!organization) {
    return <div>Loading...</div>;
  }

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '24px' }}>
      <Title level={2}>Organization Settings</Title>

      <Card title='Invite Users' style={{ marginBottom: '24px' }}>
        <p>
          Invite users to join your organization. They will receive an email with instructions to
          accept or decline the invitation.
        </p>
        <InviteUserForm
          organizationId={organization.id}
          onSuccess={() => {
            // Refresh organization data if needed
          }}
        />
      </Card>

      <Card title='Manage Tags'>
        <Paragraph>
          Manage the tags used across your organization for tracking and categorization.
        </Paragraph>
        <Link to='/tags'>
          <Button type='primary'>Manage Tags</Button>
        </Link>
      </Card>

      {/* Add more organization settings sections here */}
    </div>
  );
};
