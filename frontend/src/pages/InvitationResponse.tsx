import React, { useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { InvitationResponse as InvitationResponseComponent } from '../components/InvitationResponse';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

export const InvitationResponse: React.FC = () => {
  const { user } = useAuth();
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!user) {
      navigate('/login', { state: { from: location.pathname } });
    }
  }, [user, navigate, location.pathname]);

  if (!token) {
    return <div>Invalid invitation link</div>;
  }

  const handleSuccess = async () => {
    if (!user) {
      try {
        // Check if the email exists
        const response = await axios.get(`/api/organizations/invites/${token}/check`);
        const { exists } = response.data;

        if (exists) {
          // If user exists, redirect to login with the invitation token
          navigate(`/login?invite=${token}`);
        } else {
          // If user doesn't exist, redirect to registration with the invitation token
          navigate(`/register?invite=${token}`);
        }
      } catch (error) {
        // If there's an error checking the invitation, redirect to registration
        navigate(`/register?invite=${token}`);
      }
    } else {
      // If user is logged in, redirect to dashboard
      navigate('/dashboard');
    }
  };

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto', padding: '24px' }}>
      <InvitationResponseComponent token={token} onSuccess={handleSuccess} />
    </div>
  );
};
