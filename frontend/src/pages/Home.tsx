import {
  BankOutlined,
  <PERSON><PERSON>ardOutlined,
  <PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  ProjectOutlined,
  RocketOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { Card, Col, Row, Spin, Statistic, Typography, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { getDashboardData } from '../services/api';
import { DashboardData } from '../types';

const { Title } = Typography;

const Home: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    total_balance: 0,
    monthly_fixed_expenses: 0,
    active_projects_value: 0,
  });
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getDashboardData();

      // Ensure we have the expected data structure
      const data = response.data || {};

      // Set dashboard data
      setDashboardData({
        total_balance: data.total_balance || 0,
        monthly_fixed_expenses: data.monthly_fixed_expenses || 0,
        active_projects_value: data.active_projects_value || 0,
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      message.error('Failed to load dashboard data');
      // Set default values on error
      setDashboardData({
        total_balance: 0,
        monthly_fixed_expenses: 0,
        active_projects_value: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Title level={2}>Dashboard</Title>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size='large' />
        </div>
      ) : (
        <>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Card>
                <Statistic
                  title='Total Balance'
                  value={dashboardData.total_balance}
                  precision={2}
                  valueStyle={{ color: '#3f8600' }}
                  prefix='$'
                />
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card>
                <Statistic
                  title='Monthly Fixed Expenses'
                  value={dashboardData.monthly_fixed_expenses}
                  precision={2}
                  valueStyle={{ color: '#cf1322' }}
                  prefix='$'
                />
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card>
                <Statistic
                  title='Active Projects Value'
                  value={dashboardData.active_projects_value}
                  precision={2}
                  valueStyle={{ color: '#1890ff' }}
                  prefix='$'
                />
              </Card>
            </Col>
          </Row>

          <Title level={3} style={{ marginTop: '24px' }}>
            Quick Links
          </Title>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Link to='/accounts'>
                <Card hoverable style={{ height: '100%' }}>
                  <Card.Meta
                    title='Accounts'
                    description='Manage your bank accounts and balances'
                    avatar={<BankOutlined style={{ fontSize: 24, color: '#1890ff' }} />}
                  />
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Link to='/fixed-expenses'>
                <Card hoverable style={{ height: '100%' }}>
                  <Card.Meta
                    title='Fixed Expenses'
                    description='Track your recurring expenses'
                    avatar={<CreditCardOutlined style={{ fontSize: 24, color: '#ff4d4f' }} />}
                  />
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Link to='/misc-expenses'>
                <Card hoverable style={{ height: '100%' }}>
                  <Card.Meta
                    title='Misc Expenses'
                    description='Track one-time or variable expenses'
                    avatar={<DollarOutlined style={{ fontSize: 24, color: '#faad14' }} />}
                  />
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Link to='/current-projects'>
                <Card hoverable style={{ height: '100%' }}>
                  <Card.Meta
                    title='Current Projects'
                    description='Manage your active client projects'
                    avatar={<ProjectOutlined style={{ fontSize: 24, color: '#52c41a' }} />}
                  />
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Link to='/anticipated-projects'>
                <Card hoverable style={{ height: '100%' }}>
                  <Card.Meta
                    title='Anticipated Projects'
                    description='Track upcoming potential projects'
                    avatar={<RocketOutlined style={{ fontSize: 24, color: '#722ed1' }} />}
                  />
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Link to='/payroll'>
                <Card hoverable style={{ height: '100%' }}>
                  <Card.Meta
                    title='Payroll'
                    description='Manage employee and contractor payments'
                    avatar={<TeamOutlined style={{ fontSize: 24, color: '#722ed1' }} />}
                  />
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Link to='/forecast'>
                <Card hoverable style={{ height: '100%' }}>
                  <Card.Meta
                    title='Forecast'
                    description='View cash flow projections and scenarios'
                    avatar={<LineChartOutlined style={{ fontSize: 24, color: '#13c2c2' }} />}
                  />
                </Card>
              </Link>
            </Col>
          </Row>
        </>
      )}
    </div>
  );
};

export default Home;
