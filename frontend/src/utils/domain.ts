import { config } from '../config';

export class DomainUtils {
  static getHostname(): string {
    return window.location.hostname;
  }

  static getPathname(): string {
    return window.location.pathname;
  }

  static getConfiguredBaseDomain(): string {
    return config.baseDomain;
  }

  static getSubdomain(): string {
    let hostname = this.getHostname();

    // remove port if present
    hostname = hostname.split(':')[0];

    // If the hostname is the base domain, return an empty string
    if (hostname === config.baseDomain) {
      return '';
    }
    // Split by dots and get all parts
    const parts = hostname.split('.');

    // If we don't have exactly 3 parts, no subdomain
    if (parts.length !== 3) {
      console.log('No subdomain found parts !== 3:', parts);
      return '';
    }

    // check if the last two parts match the base domain
    const lastTwoParts = parts.slice(-2).join('.');
    if (lastTwoParts !== config.baseDomain) {
      throw new Error(
        `Base domain mismatch: ${hostname} does not contain configured base domain ${config.baseDomain}`
      );
    }

    const subdomain = parts[0];
    return subdomain;
  }

  static getBaseUrl(): string {
    return `${config.protocol}://${config.baseDomain}`;
  }

  static getAPIUrl(): string {
    let subdomain = this.getSubdomain();
    if (subdomain) {
      subdomain = `${subdomain}.`;
    }

    // Build the URL with proper protocol and port
    const url = `${config.protocol}://${subdomain || ''}${config.baseDomain}`;
    return url;
  }

  static getOrganizationUrl(subdomain?: string | null): string | null {
    subdomain = subdomain || this.getSubdomain();
    if (!subdomain) {
      return null;
    }
    return `${config.protocol}://${subdomain}.${config.baseDomain}`;
  }

  static getLoginUrl(): string {
    return `${this.getBaseUrl()}/login`;
  }

  static getDashboardUrl(subdomain?: string | null): string {
    const orgUrl = this.getOrganizationUrl(subdomain);
    return orgUrl ? `${orgUrl}/dashboard` : `${this.getBaseUrl()}/dashboard`;
  }

  static isSubdomainValid(subdomain: string): boolean {
    return subdomain !== 'www' && subdomain !== this.getHostname();
  }

  static getProtocol(): string {
    return config.protocol;
  }

  static isLoginPath(): boolean {
    return this.getPathname().includes('/login');
  }

  static navigate(url: string) {
    window.location.assign(url);
  }
}
