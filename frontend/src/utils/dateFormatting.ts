import dayjs from 'dayjs';

/**
 * Formats a date range smartly based on whether the dates are in the same year and/or month
 * @param startDate - The start date of the range
 * @param endDate - The end date of the range
 * @returns Formatted date range string
 *
 * Examples:
 * - Same year and month: "Jan 4–6, 2014"
 * - Same year, different months: "Dec 6–Jan 2, 2014"
 * - Different years: "Dec 6, 2013–Jan 2, 2014"
 */
export const formatDateRange = (startDate: dayjs.Dayjs, endDate: dayjs.Dayjs): string => {
  const sameYear = startDate.year() === endDate.year();
  const sameMonth = startDate.month() === endDate.month();

  if (sameYear && sameMonth) {
    return `${startDate.format('MMM D')}–${endDate.format('D, YYYY')}`;
  } else if (sameYear) {
    return `${startDate.format('MMM D')}–${endDate.format('MMM D, YYYY')}`;
  } else {
    return `${startDate.format('MMM D, YYYY')}–${endDate.format('MMM D, YYYY')}`;
  }
};
