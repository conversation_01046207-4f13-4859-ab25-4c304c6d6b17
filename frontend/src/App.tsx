import React, { ReactNode, Suspense, lazy, useState } from 'react';
import {
  Link,
  Navigate,
  Route,
  BrowserRouter as Router,
  Routes,
  useLocation,
  useNavigate,
} from 'react-router-dom';
import {
  App as AntApp,
  BankOutlined,
  ConfigProvider,
  CreditCardOutlined,
  DollarOutlined,
  Dropdown,
  FileTextOutlined,
  HomeOutlined,
  Layout,
  LineChartOutlined,
  LogoutOutlined,
  Menu,
  ProjectOutlined,
  RocketOutlined,
  SettingOutlined,
  ShoppingOutlined,
  Spin,
  TagsOutlined,
  TeamOutlined,
  UserOutlined,
} from './components/antd';
import { CreateOrganization } from './components/auth/CreateOrganization';
import { Login } from './components/auth/Login';
import { Register } from './components/auth/Register';
import { VerifyEmail } from './components/auth/VerifyEmail';
import { Profile } from './components/Profile';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { OrganizationProvider } from './contexts/OrganizationContext';

import './App.css';
import { DomainUtils } from './utils/domain';
// Lazy load components
const Home = lazy(() => import('./pages/Home'));
const Accounts = lazy(() => import('./pages/Accounts'));
const FixedExpenses = lazy(() => import('./pages/FixedExpenses'));
const MiscExpenses = lazy(() => import('./pages/MiscExpenses'));
const Payroll = lazy(() => import('./pages/Payroll'));
const Forecast = lazy(() => import('./pages/Forecast'));
const PurchaseOrders = lazy(() => import('./pages/PurchaseOrders'));
const Invoices = lazy(() => import('./pages/Invoices'));
const CurrentProjects = lazy(() => import('./pages/CurrentProjects'));
const AnticipatedProjects = lazy(() => import('./pages/AnticipatedProjects'));
const OrganizationSettings = lazy(() =>
  import('./pages/OrganizationSettings').then(module => ({ default: module.OrganizationSettings }))
);
const InvitationResponse = lazy(() =>
  import('./pages/InvitationResponse').then(module => ({ default: module.InvitationResponse }))
);
const Tags = lazy(() => import('./pages/Tags'));

const { Header, Content, Footer, Sider } = Layout;

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const { user, logout } = useAuth();

  const getSelectedKey = (): string => {
    const path = DomainUtils.getPathname();
    if (path === '/') return '1';
    if (path === '/accounts') return '2';
    if (path === '/fixed-expenses') return '3';
    if (path === '/misc-expenses') return '4';
    if (path === '/current-projects') return '5';
    if (path === '/anticipated-projects') return '6';
    if (path === '/purchase-orders') return '7';
    if (path === '/invoices') return '8';
    if (path === '/payroll') return '9';
    if (path === '/forecast') return '10';
    if (path === '/profile') return '11';
    if (path === '/organization-settings') return '12';
    if (path === '/tags') return '13';
    return '1';
  };

  // Define menu items
  const menuItems = [
    {
      key: '1',
      icon: <HomeOutlined />,
      label: <Link to='/'>Home</Link>,
    },
    {
      key: '2',
      icon: <BankOutlined />,
      label: <Link to='/accounts'>Accounts</Link>,
    },
    {
      key: '3',
      icon: <CreditCardOutlined />,
      label: <Link to='/fixed-expenses'>Fixed Expenses</Link>,
    },
    {
      key: '4',
      icon: <DollarOutlined />,
      label: <Link to='/misc-expenses'>Misc Expenses</Link>,
    },
    {
      key: '5',
      icon: <ProjectOutlined />,
      label: <Link to='/current-projects'>Current Projects</Link>,
    },
    {
      key: '6',
      icon: <RocketOutlined />,
      label: <Link to='/anticipated-projects'>Anticipated Projects</Link>,
    },
    {
      key: '7',
      icon: <ShoppingOutlined />,
      label: <Link to='/purchase-orders'>Purchase Orders</Link>,
    },
    {
      key: '8',
      icon: <FileTextOutlined />,
      label: <Link to='/invoices'>Invoices</Link>,
    },
    {
      key: '9',
      icon: <TeamOutlined />,
      label: <Link to='/payroll'>Payroll</Link>,
    },
    {
      key: '10',
      icon: <LineChartOutlined />,
      label: <Link to='/forecast'>Forecast</Link>,
    },
    {
      key: '11',
      icon: <SettingOutlined />,
      label: <Link to='/profile'>Profile</Link>,
    },
    {
      key: '12',
      icon: <TeamOutlined />,
      label: <Link to='/organization-settings'>Organization</Link>,
    },
    {
      key: '13',
      icon: <TagsOutlined />,
      label: <Link to='/tags'>Tags</Link>,
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: <Link to='/profile'>Profile</Link>,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: () => {
        logout();
        navigate('/login');
      },
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        collapsible
        collapsed={collapsed}
        onCollapse={value => setCollapsed(value)}
        breakpoint='lg'
        collapsedWidth={80}
        style={{
          position: 'fixed',
          height: '100vh',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 1000,
          overflow: 'auto',
        }}
      >
        <div
          className='logo'
          style={{ position: 'sticky', top: 0, zIndex: 1, background: '#001529' }}
        >
          <h2 style={{ color: 'white', textAlign: 'center', margin: '16px 0' }}>
            {collapsed ? '$' : 'Cashflow'}
          </h2>
        </div>
        <Menu
          theme='dark'
          selectedKeys={[getSelectedKey()]}
          mode='inline'
          items={menuItems}
          style={{ overflow: 'auto' }}
        />
      </Sider>
      <Layout className='site-layout' style={{ marginLeft: collapsed ? 80 : 200 }}>
        <Header
          className='site-layout-background'
          style={{ padding: 0, display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}
        >
          <div style={{ marginRight: 24 }}>
            <Dropdown menu={{ items: userMenuItems }} placement='bottomRight'>
              <span style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>
                <UserOutlined style={{ marginRight: 8 }} />
                {user?.first_name} {user?.last_name}
              </span>
            </Dropdown>
          </div>
        </Header>
        <Content style={{ margin: '0 16px' }}>
          <div className='site-layout-background' style={{ padding: 24, minHeight: 360 }}>
            <Suspense
              fallback={
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                  }}
                >
                  <Spin size='large' />
                </div>
              }
            >
              {children}
            </Suspense>
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
          Cashflow Manager ©{new Date().getFullYear()}
        </Footer>
      </Layout>
    </Layout>
  );
};

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <div>Loading...</div>; // Or your loading component
  }

  if (!user) {
    // Use Navigate to maintain the current domain context
    // The state will be preserved for the redirect back after login
    return <Navigate to='/login' state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

const AppRoutes: React.FC = () => {
  return (
    <Routes future={{ v7_relativeSplatPath: true, v7_startTransition: true }}>
      <Route path='/login' element={<Login />} />
      <Route path='/register' element={<Register />} />
      <Route path='/verify-email/:token' element={<VerifyEmail />} />
      <Route path='/create-organization' element={<CreateOrganization />} />
      <Route path='/invite/:token' element={<InvitationResponse />} />
      <Route
        path='/dashboard'
        element={
          <ProtectedRoute>
            <AppLayout>
              <Home />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/accounts'
        element={
          <ProtectedRoute>
            <AppLayout>
              <Accounts />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/fixed-expenses'
        element={
          <ProtectedRoute>
            <AppLayout>
              <FixedExpenses />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/misc-expenses'
        element={
          <ProtectedRoute>
            <AppLayout>
              <MiscExpenses />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/current-projects'
        element={
          <ProtectedRoute>
            <AppLayout>
              <CurrentProjects />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/anticipated-projects'
        element={
          <ProtectedRoute>
            <AppLayout>
              <AnticipatedProjects />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/purchase-orders'
        element={
          <ProtectedRoute>
            <AppLayout>
              <PurchaseOrders />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/invoices'
        element={
          <ProtectedRoute>
            <AppLayout>
              <Invoices />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/payroll'
        element={
          <ProtectedRoute>
            <AppLayout>
              <Payroll />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/forecast'
        element={
          <ProtectedRoute>
            <AppLayout>
              <Forecast />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/profile'
        element={
          <ProtectedRoute>
            <AppLayout>
              <Profile />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/organization-settings'
        element={
          <ProtectedRoute>
            <AppLayout>
              <OrganizationSettings />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path='/tags'
        element={
          <ProtectedRoute>
            <AppLayout>
              <Tags />
            </AppLayout>
          </ProtectedRoute>
        }
      />
      <Route path='*' element={<Navigate to='/dashboard' replace />} />
    </Routes>
  );
};

const App: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1890ff',
          colorSuccess: '#52c41a',
          colorWarning: '#faad14',
          colorError: '#ff4d4f',
        },
      }}
    >
      <Router>
        <AuthProvider>
          <OrganizationProvider>
            <AntApp>
              <AppRoutes />
            </AntApp>
          </OrganizationProvider>
        </AuthProvider>
      </Router>
    </ConfigProvider>
  );
};

// Add type definition for Routes with future flag
declare module 'react-router-dom' {
  interface RoutesProps {
    future?: {
      v7_relativeSplatPath?: boolean;
      v7_startTransition?: boolean;
    };
  }
}

export default App;
