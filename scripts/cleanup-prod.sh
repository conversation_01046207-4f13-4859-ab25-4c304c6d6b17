#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the absolute path of the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Get the project root directory (one level up from scripts)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print status messages
print_status() { echo -e "\n${GREEN}[✓] $1${NC}"; }
print_error() { echo -e "\n${RED}[✗] $1${NC}"; }
print_warning() { echo -e "\n${YELLOW}[!] $1${NC}"; }
print_info() { echo -e "\n${BLUE}[i] $1${NC}"; }

# Function to prompt user for yes/no
prompt_yes_no() {
    while true; do
        echo
        read -p "$1 (y/n): " yn
        case $yn in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo -e "\nPlease answer yes or no.";;
        esac
    done
}

# Function to remove PostgreSQL
remove_postgresql() {
    print_info "Removing PostgreSQL..."
    
    # Stop the service
    sudo systemctl stop postgresql
    sudo systemctl disable postgresql

    # Uninstall packages with purge
    sudo apt purge -y postgresql postgresql-*
    sudo apt autoremove --purge -y

    # Remove configuration and data files
    sudo rm -rf /etc/postgresql
    sudo rm -rf /var/lib/postgresql
    sudo rm -rf /var/log/postgresql
    sudo rm -rf /run/postgresql
    sudo rm -f /etc/apt/sources.list.d/postgresql.list*

    print_status "PostgreSQL removed"
}

# Function to remove Caddy
remove_caddy() {
    print_info "Removing Caddy..."
    
    # Stop and disable Caddy service
    sudo systemctl stop caddy
    sudo systemctl disable caddy
    
    # Remove Caddy packages with purge
    sudo apt purge -y caddy
    sudo apt autoremove --purge -y
    
    # Remove Caddy configuration and data
    sudo rm -rf /etc/caddy
    sudo rm -rf /var/lib/caddy
    sudo rm -rf /usr/local/bin/caddy
    sudo rm -f /etc/apt/sources.list.d/caddy-stable.list*
    sudo rm -f /usr/share/keyrings/caddy-stable-archive-keyring.gpg
    
    print_status "Caddy removed"
}

# Function to remove systemd services
remove_services() {
    print_info "Removing systemd services..."
    
    # Stop and disable services
    sudo systemctl stop cashflow-backend
    sudo systemctl stop cashflow-frontend
    sudo systemctl disable cashflow-backend
    sudo systemctl disable cashflow-frontend
    
    # Remove service files
    sudo rm -f /etc/systemd/system/cashflow-backend.service
    sudo rm -f /etc/systemd/system/cashflow-frontend.service
    
    # Reload systemd
    sudo systemctl daemon-reload
    sudo systemctl reset-failed
    
    print_status "Systemd services removed"
}

# Function to remove Python environment
remove_python_env() {
    print_info "Removing Python virtual environment..."
    
    if [ -d "$PROJECT_ROOT/backend/venv" ]; then
        rm -rf "$PROJECT_ROOT/backend/venv"
        print_status "Python virtual environment removed"
    else
        print_info "Python virtual environment not found"
    fi
}

# Function to remove frontend build
remove_frontend() {
    print_info "Removing frontend build..."
    
    if [ -d "/var/www/cashflow" ]; then
        sudo rm -rf /var/www/cashflow
        print_status "Frontend build removed"
    else
        print_info "Frontend build not found"
    fi
}

# Function to remove environment files
remove_env_files() {
    print_info "Removing environment files..."
    
    # Remove backend .env
    if [ -f "$PROJECT_ROOT/backend/.env" ]; then
        rm -f "$PROJECT_ROOT/backend/.env"
    fi
    
    # Remove frontend .env.production
    if [ -f "$PROJECT_ROOT/frontend/.env.production" ]; then
        rm -f "$PROJECT_ROOT/frontend/.env.production"
    fi
    
    print_status "Environment files removed"
}

# Function to remove Node.js
remove_nodejs() {
    print_info "Removing Node.js..."
    
    # Remove Node.js and npm with purge
    sudo apt purge -y nodejs npm
    sudo apt autoremove --purge -y
    
    # Remove NodeSource repository and key
    sudo rm -f /etc/apt/sources.list.d/nodesource.list*
    sudo rm -f /usr/share/keyrings/nodesource.gpg
    
    # Remove global npm packages and cache
    sudo rm -rf /usr/local/lib/node_modules
    sudo rm -rf /usr/local/bin/npm
    sudo rm -rf /usr/local/bin/node
    sudo rm -rf ~/.npm
    sudo rm -rf ~/.node-gyp
    
    print_status "Node.js removed"
}

# Function to remove Python packages
remove_python_packages() {
    print_info "Removing Python packages..."
    
    # Remove Python packages with purge
    sudo apt purge -y python3.11 python3.11-venv python3.11-dev python3.11-minimal
    sudo apt autoremove --purge -y
    
    # Remove Python configuration files
    sudo rm -rf /usr/local/lib/python3.11
    sudo rm -rf /usr/lib/python3.11
    sudo rm -f /etc/python3.11
    
    print_status "Python packages removed"
}

# Main cleanup process
echo -e "\nStarting production environment cleanup..."

# Confirm cleanup
if ! prompt_yes_no "Are you sure you want to remove production environment components? This cannot be undone."; then
    print_info "Cleanup cancelled"
    exit 0
fi

# Remove components with individual confirmations
if prompt_yes_no "Do you want to remove PostgreSQL?"; then
    remove_postgresql
fi

if prompt_yes_no "Do you want to remove Caddy?"; then
    remove_caddy
fi

if prompt_yes_no "Do you want to remove systemd services?"; then
    remove_services
fi

if prompt_yes_no "Do you want to remove Python virtual environment?"; then
    remove_python_env
fi

if prompt_yes_no "Do you want to remove frontend build?"; then
    remove_frontend
fi

if prompt_yes_no "Do you want to remove environment files?"; then
    remove_env_files
fi

if prompt_yes_no "Do you want to remove Node.js?"; then
    remove_nodejs
fi

if prompt_yes_no "Do you want to remove Python packages?"; then
    remove_python_packages
fi

echo -e "\n${GREEN}Production environment cleanup complete!${NC}"
echo -e "\nAll selected components have been removed. You can run setup-prod.sh again to recreate the environment." 