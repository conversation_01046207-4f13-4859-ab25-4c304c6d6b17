#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the absolute path of the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Get the project root directory (one level up from scripts)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print status messages
print_status() { echo -e "\n${GREEN}[✓] $1${NC}"; }
print_error() { echo -e "\n${RED}[✗] $1${NC}"; }
print_warning() { echo -e "\n${YELLOW}[!] $1${NC}"; }
print_info() { echo -e "\n${BLUE}[i] $1${NC}"; }

# Function to check if a service is running
check_service() {
    if systemctl is-active --quiet "$1"; then
        print_status "$1 is running"
        return 0
    else
        print_error "$1 is not running"
        return 1
    fi
}

# Function to start a service
start_service() {
    print_info "Starting $1..."
    systemctl start "$1"
    sleep 2
    if check_service "$1"; then
        print_status "$1 started successfully"
    else
        print_error "Failed to start $1"
        return 1
    fi
}

# Function to restart a service
restart_service() {
    print_info "Restarting $1..."
    systemctl restart "$1"
    sleep 2
    if check_service "$1"; then
        print_status "$1 restarted successfully"
    else
        print_error "Failed to restart $1"
        return 1
    fi
}

# Function to rebuild and deploy frontend
rebuild_frontend() {
    print_info "Rebuilding frontend..."
    
    # Install frontend dependencies
    cd "$PROJECT_ROOT/frontend"
    npm install
    
    # Build frontend
    npm run build
    
    # Copy built files to web root
    cp -r build/* /var/www/cashflow/
    chown -R $SUDO_USER:$SUDO_USER /var/www/cashflow
    
    print_status "Frontend rebuilt and deployed"
}

# Main startup process
echo -e "\nStarting production services..."

# Start PostgreSQL if not running
if ! check_service "postgresql"; then
    start_service "postgresql"
fi

# Start Caddy if not running
if ! check_service "caddy"; then
    start_service "caddy"
fi

# Start backend service
if ! check_service "cashflow-backend"; then
    start_service "cashflow-backend"
fi

# Check if frontend needs to be rebuilt
if [ "$1" == "--rebuild" ]; then
    rebuild_frontend
fi

# Verify all services are running
echo -e "\nVerifying services..."
check_service "postgresql"
check_service "caddy"
check_service "cashflow-backend"

echo -e "\n${GREEN}All services started!${NC}"
echo -e "\nYou can check service status with:"
echo "sudo systemctl status cashflow-backend"
echo "sudo systemctl status caddy"
echo "sudo systemctl status postgresql"

# Show logs if requested
if [ "$1" == "--logs" ]; then
    echo -e "\nShowing service logs (Ctrl+C to exit)..."
    journalctl -f -u cashflow-backend -u caddy
fi 