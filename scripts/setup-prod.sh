#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the absolute path of the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Get the project root directory (one level up from scripts)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Configuration
PG_USER="supergres"              # PostgreSQL superuser
CASHFLOW_USER="cashgres"         # Cashflow application user
CASHFLOW_DB="cashflow"           # Database name

# Global variables for storing values
PG_ADMIN_PASSWORD=""
DB_PASSWORD=""
SECRET_KEY=""
DOMAIN_NAME=""
EMAIL=""
SENDGRID_API_KEY=""
COMPANY_NAME=""

# Function to print status messages
print_status() { echo -e "\n${GREEN}[✓] $1${NC}"; }
print_error() { echo -e "\n${RED}[✗] $1${NC}"; }
print_warning() { echo -e "\n${YELLOW}[!] $1${NC}"; }
print_info() { echo -e "\n${BLUE}[i] $1${NC}"; }

# Utility functions
command_exists() { command -v "$1" >/dev/null 2>&1; }
service_running() { systemctl is-active --quiet "$1"; }
port_in_use() { lsof -i ":$1" >/dev/null 2>&1; }
file_exists() { [ -f "$1" ]; }
dir_exists() { [ -d "$1" ]; }

# Function to prompt user for yes/no
prompt_yes_no() {
    while true; do
        echo
        read -p "$1 (y/n): " yn
        case $yn in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo -e "\nPlease answer yes or no.";;
        esac
    done
}

# Function to generate a random password
generate_password() {
    tr -dc 'A-Za-z0-9!_-' < /dev/urandom | head -c 24
}

# Function to prompt for password with confirmation
prompt_password() {
    local prompt="$1"
    local password1
    local password2
    
    while true; do
        read -s -p "$prompt: " password1
        echo >&2
        
        read -s -p "Confirm $prompt: " password2
        echo >&2
        
        if [ "$password1" = "$password2" ]; then
            echo "$password1"
            return 0
        else
            print_error "Passwords do not match. Please try again."
        fi
    done
}

# Function to get existing value from .env file
get_existing_value() {
    local file="$1"
    local key="$2"
    if [ -f "$file" ]; then
        if [ "$key" = "DB_PASSWORD" ]; then
            local db_url=$(grep "^DATABASE_URL=" "$file" | cut -d'=' -f2-)
            if [ -n "$db_url" ]; then
                echo "$db_url" | sed -E 's|^postgresql://[^:]+:([^@]+)@.*|\1|'
            fi
        else
            grep "^$key=" "$file" | cut -d'=' -f2-
        fi
    fi
}

# Function to prompt for value with existing value option
prompt_with_existing() {
    local prompt="$1"
    local key="$2"
    local file="$3"
    local existing_value
    
    existing_value=$(get_existing_value "$file" "$key")
    
    if [ -n "$existing_value" ]; then
        echo -e "\n${YELLOW}Existing value found for $key${NC}"
        if prompt_yes_no "Would you like to use the existing value?"; then
            eval "$key=\"$existing_value\""
            return 0
        fi
    fi
    
    if prompt_yes_no "Would you like to auto-generate a new value?"; then
        local new_value=$(generate_password)
        echo -e "\n${YELLOW}Generated new value for $key${NC}"
        eval "$key=\"$new_value\""
        return 0
    fi
    
    local new_value=$(prompt_password "$prompt")
    eval "$key=\"$new_value\""
    return 0
}

# Function to update or add environment variable
update_env_var() {
    local file="$1"
    local key="$2"
    local value="$3"
    
    local clean_value=$(echo "$value" | sed -r "s/\x1B\[([0-9]{1,3}(;[0-9]{1,2})?)?[mGK]//g" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    
    # Wrap value in double quotes if it contains spaces or special characters
    if [[ "$clean_value" =~ [[:space:]] ]]; then
        clean_value="\"$clean_value\""
    fi
    
    local temp_file=$(mktemp)
    
    if grep -q "^$key=" "$file"; then
        awk -v key="$key" -v value="$clean_value" '
            BEGIN { FS=OFS="=" }
            $1 == key { $2 = value }
            { print }
        ' "$file" > "$temp_file"
    else
        cp "$file" "$temp_file"
        echo "$key=$clean_value" >> "$temp_file"
    fi
    
    mv "$temp_file" "$file"
}

# Function to prompt for setup confirmation
prompt_setup() {
    local component="$1"
    if prompt_yes_no "Would you like to setup $component?"; then
        return 0
    fi
    print_info "Skipping $component setup"
    return 1
}

# Function to setup firewall
setup_firewall() {
    print_info "Setting up firewall..."
    
    if ! command_exists ufw; then
        print_info "Installing UFW..."
        apt-get update
        apt-get install -y ufw
    fi
    
    # Reset UFW to default
    ufw --force reset
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH
    ufw allow ssh
    
    # Allow HTTP/HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Enable firewall
    ufw --force enable
    
    print_status "Firewall configured"
}

# Function to setup system updates
setup_system_updates() {
    print_info "Setting up automatic system updates..."
    
    if ! command_exists unattended-upgrades; then
        apt-get update
        apt-get install -y unattended-upgrades apt-listchanges
    fi
    
    # Configure automatic updates
    cat > /etc/apt/apt.conf.d/20auto-upgrades << EOF
APT::Periodic::Update-Package-Lists "1";
APT::Periodic::Unattended-Upgrade "1";
APT::Periodic::AutocleanInterval "7";
APT::Periodic::Download-Upgradeable-Packages "1";
APT::Periodic::Verbose "1";
EOF
    
    # Enable security updates
    cat > /etc/apt/apt.conf.d/50unattended-upgrades << EOF
Unattended-Upgrade::Allowed-Origins {
    "\${distro_id}:\${distro_codename}-security";
    "\${distro_id}ESMApps:\${distro_codename}-apps-security";
    "\${distro_id}ESM:\${distro_codename}-infra-security";
};
Unattended-Upgrade::Package-Blacklist {
};
Unattended-Upgrade::DevRelease "auto";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "true";
Unattended-Upgrade::Automatic-Reboot-Time "02:00";
EOF
    
    print_status "Automatic system updates configured"
}

# Function to setup PostgreSQL
setup_postgresql() {
    print_info "Setting up PostgreSQL..."
    
    if ! command_exists psql; then
        print_info "Installing PostgreSQL..."
        apt-get update
        apt-get install -y postgresql postgresql-contrib
    fi
    
    # Configure PostgreSQL for production
    sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" /etc/postgresql/*/main/postgresql.conf
    sed -i "s/#max_connections = 100/max_connections = 100/" /etc/postgresql/*/main/postgresql.conf
    sed -i "s/#shared_buffers = 128MB/shared_buffers = 256MB/" /etc/postgresql/*/main/postgresql.conf
    
    # Restart PostgreSQL
    systemctl restart postgresql
    
    print_status "PostgreSQL configured"
}

# Function to setup Caddy
setup_caddy() {
    print_info "Setting up Caddy..."
    
    if ! command_exists caddy; then
        print_info "Installing Caddy with GoDaddy DNS provider..."
        apt-get install -y debian-keyring debian-archive-keyring apt-transport-https curl
        
        # Remove existing Caddy repository files if they exist
        rm -f /usr/share/keyrings/caddy-stable-archive-keyring.gpg
        rm -f /etc/apt/sources.list.d/caddy-stable.list
        
        # Add Caddy repository and key
        curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
        curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | tee /etc/apt/sources.list.d/caddy-stable.list > /dev/null
        
        apt-get update
        apt-get install -y caddy

        # Install Go for building Caddy
        print_info "Installing Go..."
        if ! command_exists go; then
            # Remove any existing Go installation
            apt-get purge -y golang-go golang-1.*
            apt-get autoremove -y
            
            # Install Go 1.21
            wget https://go.dev/dl/go1.21.6.linux-amd64.tar.gz
            rm -rf /usr/local/go
            tar -C /usr/local -xzf go1.21.6.linux-amd64.tar.gz
            rm go1.21.6.linux-amd64.tar.gz
            
            # Add Go to PATH
            echo 'export PATH=$PATH:/usr/local/go/bin' > /etc/profile.d/go.sh
            chmod +x /etc/profile.d/go.sh
            source /etc/profile.d/go.sh
        fi
        
        # Add Go bin to PATH for current session
        export PATH=$PATH:/usr/local/go/bin
        
        # Install xcaddy for custom builds
        print_info "Installing xcaddy..."        
        go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest

        # Add Go bin to PATH for xcaddy
        export PATH=$PATH:$HOME/go/bin
        # Create a temporary directory for building
        TEMP_DIR=$(mktemp -d)
        cd "$TEMP_DIR"        
        # Build Caddy with GoDaddy DNS provider
        print_info "Building Caddy with GoDaddy DNS provider..."
        $HOME/go/bin/xcaddy build --with github.com/dmarquesdev/caddy-dns-godaddy
        
        # Move the custom build to replace the default Caddy
        mv caddy /usr/bin/caddy
        chown root:root /usr/bin/caddy
        chmod 755 /usr/bin/caddy

        # Clean up
        cd - > /dev/null
        rm -rf "$TEMP_DIR"
    fi
    
    # Prompt for GoDaddy API credentials
    read -p "Enter your GoDaddy API Key: " GODADDY_API_KEY
    read -p "Enter your GoDaddy API Secret: " GODADDY_API_SECRET

    # Ensure DOMAIN_NAME is set
    if [ -z "$DOMAIN_NAME" ]; then
        read -p "Enter your domain name (e.g., example.com): " DOMAIN_NAME
    fi
    
    # Create Caddy environment file
    print_info "Creating Caddy environment file..."
    mkdir -p /etc/caddy
    echo "GODADDY_TOKEN=$GODADDY_API_KEY:$GODADDY_API_SECRET" > /etc/caddy/caddy.env
    chmod 600 /etc/caddy/caddy.env
    
    # Create Caddy service override
    print_info "Creating Caddy service override..."
    mkdir -p /etc/systemd/system/caddy.service.d
    cat > /etc/systemd/system/caddy.service.d/override.conf << EOF
[Service]
EnvironmentFile=/etc/caddy/caddy.env
# Automatically restart caddy if it crashes except if the exit code was 1
Restart=always
RestartSec=5s
StartLimitIntervalSec=0
Type=simple
EOF
    
    # Reload systemd to apply changes
    systemctl daemon-reload
    
    # Create Caddy production configuration
    cat > "$PROJECT_ROOT/caddy/Caddyfile.prod" << EOF
{
    # Global options
    admin off
    servers {
        protocols h1 h2 h2c h3
        # Increase max header size for large cookies/tokens
        max_header_size 16384
    }
    # Configure logging
    log {
        output file /var/log/caddy/access.log {
            roll_size 10MB
            roll_keep 5
            roll_keep_for 720h
        }
        # Add error log
        output file /var/log/caddy/error.log {
            roll_size 10MB
            roll_keep 5
            roll_keep_for 720h
        }
        format json
    }
    # Configure automatic HTTPS
    auto_https disable_redirects
    # Configure storage
    storage file_system {
        root /var/lib/caddy
    }
}

# Handle main www domain redirect
www.$DOMAIN_NAME {
    redir https://$DOMAIN_NAME{uri} permanent
}

*.$DOMAIN_NAME, $DOMAIN_NAME {
    # TLS configuration
    tls {
        protocols tls1.2 tls1.3
        alpn h2 http/1.1
        dns godaddy {$GODADDY_TOKEN}
    }

    # API endpoints
    handle /api/* {
        # Reverse proxy to backend
        reverse_proxy localhost:8000 {
            # Headers
            header_up Host {upstream_hostport}
            header_up X-Real-IP {remote_host}
            
            # Health checks
            health_uri /api/health
            health_interval 30s
            health_timeout 5s
            health_status 200
            health_headers {
                User-Agent "Caddy Health Check"
            }
            
            # Load balancing (if you add more backend instances later)
            lb_policy round_robin
            
            # Timeouts
            transport http {
                dial_timeout 10s
                response_header_timeout 10s
                read_timeout 60s
                write_timeout 60s
            }
        }

        # API-specific headers
        header {
            # Cache control for API
            Cache-Control "no-store, no-cache, must-revalidate"
            Pragma "no-cache"
            Expires "0"
        }
    }

    # Frontend - Production
    handle {
        root * /var/www/cashflow
        try_files {path} /index.html
        file_server

        # Enable compression for static files
        encode gzip
        encode zstd

        # Cache static assets
        header {
            # Cache control for static assets
            Cache-Control "public, max-age=31536000, immutable"
            # Cache control for HTML
            @html {
                file
                path *.html
            }
            header @html Cache-Control "no-cache"
        }
    }

    # Production-specific security headers
    header {
        # Enable HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        # Prevent clickjacking
        X-Frame-Options "SAMEORIGIN"
        # XSS protection
        X-XSS-Protection "1; mode=block"
        # Prevent MIME type sniffing
        X-Content-Type-Options "nosniff"
        # Referrer policy
        Referrer-Policy "strict-origin-when-cross-origin"
        # Content Security Policy
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"
        # Remove server header
        -Server
    }
}
EOF
    
    # Create web root directory
    mkdir -p /var/www/cashflow
    chown -R $SUDO_USER:$SUDO_USER /var/www/cashflow
    
    # Create Caddy data directory with proper permissions
    mkdir -p /var/lib/caddy
    chown -R caddy:caddy /var/lib/caddy
    
    # Create Caddy config directory with proper permissions
    mkdir -p /etc/caddy
    chown -R caddy:caddy /etc/caddy
    
    # Copy Caddyfile to system location
    cp "$PROJECT_ROOT/caddy/Caddyfile.prod" /etc/caddy/Caddyfile
    chown caddy:caddy /etc/caddy/Caddyfile
    
    # Enable and start Caddy
    systemctl enable caddy
    systemctl restart caddy
    
    # Wait for Caddy to start and obtain certificates
    print_info "Waiting for Caddy to obtain SSL certificates..."
    sleep 10
    
    # Check if Caddy is running and certificates are obtained
    if ! systemctl is-active --quiet caddy; then
        print_error "Caddy failed to start. Check logs with: journalctl -u caddy"
        exit 1
    fi
    
    print_status "Caddy configured with GoDaddy DNS provider for automatic SSL certificate management"
}

# Function to build frontend
build_frontend() {
    print_info "Building frontend..."
    
    # Install Node.js 20 and npm if not present
    if ! command_exists node || ! command_exists npm || [ "$(node -v | cut -d'v' -f2 | cut -d'.' -f1)" -lt 20 ]; then
        print_info "Installing Node.js 20 and npm..."
        curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
        apt-get update
        apt-get install -y nodejs
        
        # Verify npm installation
        if ! command_exists npm; then
            print_info "Installing npm..."
            apt-get install -y npm
        fi
        
        # Update npm to latest version
        npm install -g npm@latest
    fi
    
    # Install frontend dependencies
    cd "$PROJECT_ROOT/frontend"
    print_info "Installing frontend dependencies..."
    npm install --legacy-peer-deps
    
    # Build frontend
    print_info "Building frontend application..."
    npm run build
    
    # Copy built files to web root
    cp -r build/* /var/www/cashflow/
    chown -R $SUDO_USER:$SUDO_USER /var/www/cashflow
    
    print_status "Frontend built and deployed"
}

# Function to setup environment files
setup_env_files() {
    print_info "Setting up environment files..."
    
    # Prompt for domain name
    read -p "Enter your domain name (e.g., yourdomain.com): " DOMAIN_NAME
    
    # Prompt for email
    read -p "Enter your email address (for SSL notifications): " EMAIL
    
    # Get PostgreSQL admin password
    echo -e "\n${YELLOW}IMPORTANT: This is the admin password for the PostgreSQL superuser ($PG_USER).${NC}"
    prompt_with_existing "Enter password for PostgreSQL superuser '$PG_USER'" "PG_ADMIN_PASSWORD" "$PROJECT_ROOT/backend/.env"
    
    # Get Cashflow database user password
    echo -e "\n${YELLOW}This is the password for the Cashflow application database user ($CASHFLOW_USER).${NC}"
    prompt_with_existing "Enter password for database user '$CASHFLOW_USER'" "DB_PASSWORD" "$PROJECT_ROOT/backend/.env"
    
    # Get secret key
    echo -e "\n${YELLOW}This is the secret key for the Cashflow application.${NC}"
    prompt_with_existing "Enter secret key" "SECRET_KEY" "$PROJECT_ROOT/backend/.env"
    
    # Get SendGrid API key
    echo -e "\n${YELLOW}This is the SendGrid API key for sending emails.${NC}"
    prompt_with_existing "Enter SendGrid API key" "SENDGRID_API_KEY" "$PROJECT_ROOT/backend/.env"

    # Get company name
    echo -e "\n${YELLOW}This is the company name for the Cashflow application.${NC}"
    read -p "Enter company name: " COMPANY_NAME
    
    # Create or update backend environment file
    if ! file_exists "$PROJECT_ROOT/backend/.env"; then
        cp "$PROJECT_ROOT/backend/.env.example" "$PROJECT_ROOT/backend/.env"
    fi
    
    # Update backend environment variables
    update_env_var "$PROJECT_ROOT/backend/.env" "DATABASE_URL" "postgresql://$CASHFLOW_USER:$DB_PASSWORD@localhost:5432/$CASHFLOW_DB"
    update_env_var "$PROJECT_ROOT/backend/.env" "SECRET_KEY" "$SECRET_KEY"
    update_env_var "$PROJECT_ROOT/backend/.env" "ENVIRONMENT" "production"
    update_env_var "$PROJECT_ROOT/backend/.env" "SENDGRID_API_KEY" "$SENDGRID_API_KEY"
    update_env_var "$PROJECT_ROOT/backend/.env" "DOMAIN_NAME" "$DOMAIN_NAME"
    update_env_var "$PROJECT_ROOT/backend/.env" "BASE_DOMAIN" "$DOMAIN_NAME"
    update_env_var "$PROJECT_ROOT/backend/.env" "ALLOWED_ORIGINS" "https://$DOMAIN_NAME,https://*.$DOMAIN_NAME"
    update_env_var "$PROJECT_ROOT/backend/.env" "SENDGRID_FROM_EMAIL" "noreply@$DOMAIN_NAME"
    update_env_var "$PROJECT_ROOT/backend/.env" "COMPANY_NAME" "$COMPANY_NAME"
    
    # Create or update frontend environment file
    if ! file_exists "$PROJECT_ROOT/frontend/.env.production"; then
        cp "$PROJECT_ROOT/frontend/.env.example" "$PROJECT_ROOT/frontend/.env.production"
    fi
    
    # Update frontend environment variables
    update_env_var "$PROJECT_ROOT/frontend/.env.production" "REACT_APP_PROTOCOL" "https"
    update_env_var "$PROJECT_ROOT/frontend/.env.production" "REACT_APP_BASE_DOMAIN" "$DOMAIN_NAME"
    update_env_var "$PROJECT_ROOT/frontend/.env.production" "NODE_ENV" "production"
    update_env_var "$PROJECT_ROOT/frontend/.env.production" "COMPANY_NAME" "$COMPANY_NAME"
    print_status "Environment files configured"
}

# Function to setup database
setup_database() {
    print_info "Setting up database..."
    
    # Check if required parameters are populated

    if [ -z "$PG_ADMIN_PASSWORD" ]; then
        PG_ADMIN_PASSWORD=$(get_existing_value "$PROJECT_ROOT/backend/.env" "PG_ADMIN_PASSWORD")
        if [ -z "$PG_ADMIN_PASSWORD" ]; then
            echo -e "\n${YELLOW}IMPORTANT: This is the admin password for the PostgreSQL superuser ($PG_USER).${NC}"
            prompt_with_existing "Enter password for PostgreSQL superuser '$PG_USER'" "PG_ADMIN_PASSWORD" "$PROJECT_ROOT/backend/.env"
        fi
    fi

    if [ -z "$DB_PASSWORD" ]; then
        DB_PASSWORD=$(get_existing_value "$PROJECT_ROOT/backend/.env" "DB_PASSWORD")
        if [ -z "$DB_PASSWORD" ]; then
            echo -e "\n${YELLOW}This is the password for the Cashflow application database user ($CASHFLOW_USER).${NC}"
            prompt_with_existing "Enter password for database user '$CASHFLOW_USER'" "DB_PASSWORD" "$PROJECT_ROOT/backend/.env"
        fi
    fi

    # Check if database and users already exist
    if sudo -u $PG_USER psql -lqt 2>/dev/null | cut -d \| -f 1 | grep -qw "$CASHFLOW_DB"; then
        print_warning "Database '$CASHFLOW_DB' already exists"
        if ! prompt_yes_no "Do you want to recreate the database and users?"; then
            print_info "Skipping database setup"
            return 0
        fi
        
        # Drop existing database and users
        sudo -u $PG_USER psql -c "DROP DATABASE IF EXISTS $CASHFLOW_DB;"
        sudo -u $PG_USER psql -c "DROP ROLE IF EXISTS $CASHFLOW_USER;"
    fi
    
    # Run setupdb.sh
    if ! "$SCRIPT_DIR/setupdb.sh" "$PG_USER" "$PG_ADMIN_PASSWORD" "$CASHFLOW_USER" "$DB_PASSWORD" "$CASHFLOW_DB"; then
        print_error "Failed to setup database"
        return 1
    fi
    
    print_status "Database configured"
}

# Function to setup Python environment
setup_python_env() {
    print_info "Setting up Python environment..."
    
    # Install Python 3.11 if not present
    if ! command_exists python3.11; then
        apt-get update
        apt-get install -y software-properties-common
        add-apt-repository -y ppa:deadsnakes/ppa
        apt-get update
        apt-get install -y python3.11 python3.11-venv python3.11-dev
    fi
    
    # Install PostgreSQL development packages
    apt-get update
    apt-get install -y libpq-dev python3.11-dev gcc
    
    # Create virtual environment
    if [ ! -d "$PROJECT_ROOT/backend/venv" ]; then
        python3.11 -m venv "$PROJECT_ROOT/backend/venv"
    fi
    
    # Activate virtual environment and install dependencies
    source "$PROJECT_ROOT/backend/venv/bin/activate"
    pip install --upgrade pip
    pip install -r "$PROJECT_ROOT/backend/requirements.txt"
    
    print_status "Python environment configured"
}

# Function to setup systemd services
setup_services() {
    print_info "Setting up systemd services..."
    
    # Create backend service
    cat > /etc/systemd/system/cashflow-backend.service << EOF
[Unit]
Description=Cashflow Backend Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
User=$SUDO_USER
Group=$SUDO_USER
WorkingDirectory=$PROJECT_ROOT/backend
Environment="PATH=$PROJECT_ROOT/backend/venv/bin"
ExecStart=$PROJECT_ROOT/backend/venv/bin/python -m app.app
Restart=always
RestartSec=10
StartLimitIntervalSec=0
Type=simple

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable services
    systemctl daemon-reload
    systemctl enable cashflow-backend
    
    print_status "Backend service configured"
}

# Function to setup Caddy monitoring
setup_caddy_monitoring() {
    print_info "Setting up Caddy monitoring..."
    
    # Create log directory if it doesn't exist
    mkdir -p /var/log/caddy
    chown -R caddy:caddy /var/log/caddy
    
    # Update email in monitoring script
    sed -i "s/NOTIFICATION_EMAIL=.*/NOTIFICATION_EMAIL=\"$EMAIL\"/" "$SCRIPT_DIR/monitor-caddy-health.sh"
    
    # Make the script executable
    chmod +x "$SCRIPT_DIR/monitor-caddy-health.sh"
    
    # Install mailutils if not present
    if ! command_exists mail; then
        print_info "Installing mailutils..."
        apt-get update
        apt-get install -y mailutils
    fi
    
    # Add cron job if not already present
    if ! crontab -l 2>/dev/null | grep -q "monitor-caddy-health.sh"; then
        (crontab -l 2>/dev/null; echo "*/5 * * * * $SCRIPT_DIR/monitor-caddy-health.sh") | crontab -
    fi
    
    print_status "Caddy monitoring configured"
}

# Main setup process
echo -e "\nStarting production environment setup..."

# Setup system
if prompt_setup "Firewall"; then
    setup_firewall
fi

if prompt_setup "System Updates"; then
    setup_system_updates
fi

if prompt_setup "PostgreSQL"; then
    setup_postgresql
fi

# Setup application
if prompt_setup "Environment Files"; then
    setup_env_files
fi

if prompt_setup "Database"; then
    setup_database
fi

if prompt_setup "Python Environment"; then
    setup_python_env
fi

if prompt_setup "Caddy"; then
    setup_caddy
fi

if prompt_setup "Caddy Monitoring"; then
    setup_caddy_monitoring
fi

if prompt_setup "Frontend Build"; then
    build_frontend
fi

if prompt_setup "Systemd Services"; then
    setup_services
fi

echo -e "\n${GREEN}Production environment setup complete!${NC}"
echo -e "\nNext steps:"
echo "1. Start the services:"
echo "   sudo systemctl start cashflow-backend"
echo "2. Access your application at https://$DOMAIN_NAME"
echo "3. Monitor Caddy health checks at /var/log/caddy/health.log" 