#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the absolute path of the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Get the project root directory (one level up from scripts)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Configuration
PG_USER="supergres"              # PostgreSQL superuser
CASHFLOW_USER="cashgres"         # Cashflow application user
CASHFLOW_DB="cashflow"           # Database name

# Global variables for storing values
PG_ADMIN_PASSWORD=""
DB_PASSWORD=""
SECRET_KEY=""

# Function to print status messages
print_status() { echo -e "\n${GREEN}[✓] $1${NC}"; }
print_error() { echo -e "\n${RED}[✗] $1${NC}"; }
print_warning() { echo -e "\n${YELLOW}[!] $1${NC}"; }
print_info() { echo -e "\n${BLUE}[i] $1${NC}"; }

# Utility functions
command_exists() { command -v "$1" >/dev/null 2>&1; }
service_running() { systemctl is-active --quiet "$1"; }
port_in_use() { lsof -i ":$1" >/dev/null 2>&1; }
file_exists() { [ -f "$1" ]; }
dir_exists() { [ -d "$1" ]; }

# Function to prompt user for yes/no
prompt_yes_no() {
    while true; do
        echo
        read -p "$1 (y/n): " yn
        case $yn in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo -e "\nPlease answer yes or no.";;
        esac
    done
}

# Function to generate a random password
generate_password() {
    tr -dc 'A-Za-z0-9!_-' < /dev/urandom | head -c 24
}

# Function to prompt for password with confirmation
prompt_password() {
    local prompt="$1"
    local password1
    local password2
    
    while true; do
        # Print labels to stderr so they're not suppressed
        read -s -p "$prompt: " password1
        echo >&2
        
        read -s -p "Confirm $prompt: " password2
        echo >&2
        
        if [ "$password1" = "$password2" ]; then
            echo "$password1"
            return 0
        else
            print_error "Passwords do not match. Please try again."
        fi
    done
}

# Function to get existing value from .env file
get_existing_value() {
    local file="$1"
    local key="$2"
    if [ -f "$file" ]; then
        if [ "$key" = "DB_PASSWORD" ]; then
            # Extract password from DATABASE_URL
            local db_url=$(grep "^DATABASE_URL=" "$file" | cut -d'=' -f2-)
            if [ -n "$db_url" ]; then
                # Extract password from postgresql://user:password@host:port/db
                echo "$db_url" | sed -E 's|^postgresql://[^:]+:([^@]+)@.*|\1|'
            fi
        else
            grep "^$key=" "$file" | cut -d'=' -f2-
        fi
    fi
}

# Function to prompt for value with existing value option
prompt_with_existing() {
    local prompt="$1"
    local key="$2"
    local file="$3"
    local existing_value
    
    existing_value=$(get_existing_value "$file" "$key")
    
    # If there's an existing value, ask if they want to use it
    if [ -n "$existing_value" ]; then
        echo -e "\n${YELLOW}Existing value found for $key${NC}"
        if prompt_yes_no "Would you like to use the existing value?"; then
            eval "$key=\"$existing_value\""
            return 0
        fi
    fi
    
    # If no existing value or they don't want to use it, ask about generating new one
    if prompt_yes_no "Would you like to auto-generate a new value?"; then
        local new_value=$(generate_password)
        echo -e "\n${YELLOW}Generated new value for $key${NC}"
        eval "$key=\"$new_value\""
        return 0
    fi
    
    # Only prompt for password if they don't want to auto-generate
    local new_value=$(prompt_password "$prompt")
    eval "$key=\"$new_value\""
    return 0
}

# Function to install Go
install_go() {
    # Remove any existing Go installation
    sudo rm -rf /usr/local/go
    
    # Remove system Go package if it exists
    if command -v go >/dev/null 2>&1; then
        print_info "Removing system Go package..."
        sudo apt remove -y golang-go golang-go-linux-amd64 golang-go-linux-arm64 golang-go-linux-arm
        sudo apt autoremove -y
    fi
    
    # Get the latest Go version
    GO_VERSION=$(curl -s https://go.dev/VERSION?m=text | head -n1 | cut -d' ' -f1)
    if [ -z "$GO_VERSION" ]; then
        print_error "Failed to get Go version. Using fallback version 1.24.2"
        GO_VERSION="go1.24.2"
    fi
    
    GO_TAR="${GO_VERSION}.linux-amd64.tar.gz"
    GO_URL="https://go.dev/dl/${GO_TAR}"
    
    # Download Go
    print_info "Downloading Go ${GO_VERSION}..."
    curl -LO "$GO_URL"
    if [ $? -ne 0 ]; then
        print_error "Failed to download Go. Please install it manually."
        print_info "Visit https://go.dev/dl/ to download the latest version."
        return 1
    fi
    
    # Install Go
    print_info "Installing Go ${GO_VERSION}..."
    sudo tar -C /usr/local -xzf "$GO_TAR"
    rm "$GO_TAR"
    
    # Update PATH in all relevant shell configuration files
    for RC_FILE in ~/.bashrc ~/.profile ~/.zshrc; do
        if [ -f "$RC_FILE" ]; then
            # Remove any existing Go PATH entries
            sed -i '/export PATH=.*\/usr\/local\/go\/bin/d' "$RC_FILE"
            # Add the new Go PATH entry at the beginning to take precedence
            sed -i "1i export PATH=/usr/local/go/bin:\$PATH" "$RC_FILE"
        fi
    done
    
    # Update PATH for current session
    export PATH=/usr/local/go/bin:$PATH
    
    # Verify the installation
    if ! command -v go >/dev/null 2>&1; then
        print_error "Go installation failed. Please check the installation process."
        return 1
    fi
    
    # Verify the version
    INSTALLED_VERSION=$(go version | awk '{print $3}')
    if [ "$INSTALLED_VERSION" != "$GO_VERSION" ]; then
        print_error "Version mismatch: Expected $GO_VERSION but got $INSTALLED_VERSION"
        print_info "Current Go binary location: $(which go)"
        print_info "Please restart your shell or run: source ~/.bashrc"
        return 1
    fi
    
    print_status "Go ${GO_VERSION} installed successfully"
    return 0
}

# Function to check Go version and install if needed
check_go() {
    if ! command_exists go; then
        print_error "Go is not installed (required for MailHog)"
        if prompt_yes_no "Would you like to install Go?"; then
            print_info "Installing Go..."
            install_go || exit 1
        else
            print_error "Go is required for MailHog. Please install it manually."
            print_info "Visit https://go.dev/dl/ to download the latest version."
            exit 1
        fi
    fi

    # Check Go version
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    if [[ "$GO_VERSION" < "1.23" ]]; then
        print_error "Go version $GO_VERSION is too old. Installing latest version..."
        install_go || exit 1
    fi

    print_status "Go version $(go version) is installed"
}

# Function to check Python version
check_python_version() {
    if ! command_exists python3; then
        print_error "Python 3 is not installed"
        if prompt_yes_no "Would you like to install Python 3.11?"; then
            print_info "Installing Python 3.11..."
            sudo apt update
            sudo apt install -y software-properties-common
            sudo add-apt-repository -y ppa:deadsnakes/ppa
            sudo apt update
            sudo apt install -y python3.11 python3.11-venv python3.11-dev
            sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
        else
            print_error "Python 3.11 is required. Please install it manually and run this script again."
            exit 1
        fi
    fi

    python_version=$(python3 --version 2>&1 | awk '{print $2}')
    if [[ "$python_version" < "3.11" ]]; then
        print_error "Python version $python_version is too old. Please install Python 3.11 or higher."
        exit 1
    fi
    print_status "Python version $python_version is compatible"
}

# Function to check Node.js version
check_node_version() {
    if ! command_exists node; then
        print_error "Node.js is not installed"
        if prompt_yes_no "Would you like to install Node.js 20 (LTS)?"; then
            print_info "Installing Node.js 20..."
            curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
            sudo apt install -y nodejs
        else
            print_error "Node.js 20 is required. Please install it manually and run this script again."
            exit 1
        fi
    fi

    node_version=$(node --version 2>&1 | cut -d'v' -f2)
    if [[ "$node_version" < "20" ]]; then
        print_error "Node.js version $node_version is too old"
        if prompt_yes_no "Would you like to upgrade to Node.js 20 (LTS)?"; then
            print_info "Upgrading Node.js..."
            curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
            sudo apt install -y nodejs
        else
            print_error "Node.js 20 is required. Please upgrade manually and run this script again."
            exit 1
        fi
    fi

    print_status "Node.js version $(node --version) is compatible"
}

# Function to check PostgreSQL
check_postgresql() {
    # Check if PostgreSQL is installed
    if command_exists psql; then
        print_info "PostgreSQL is already installed"
        
        # Check if PostgreSQL is running
        if service_running postgresql; then
            print_info "PostgreSQL is already running"
            if prompt_yes_no "Would you like to skip PostgreSQL setup?"; then
                print_info "Skipping PostgreSQL setup"
                return 0
            fi
        else
            print_warning "PostgreSQL is installed but not running"
            if prompt_yes_no "Would you like to start PostgreSQL?"; then
                print_info "Starting PostgreSQL..."
                sudo systemctl start postgresql
                sudo systemctl enable postgresql
            else
                print_error "PostgreSQL must be running to continue"
                exit 1
            fi
        fi
    else
        print_error "PostgreSQL is not installed"
        if prompt_yes_no "Would you like to install PostgreSQL?"; then
            print_info "Installing PostgreSQL..."
            sudo apt update
            sudo apt install -y postgresql postgresql-contrib
            
            # Start PostgreSQL after installation
            print_info "Starting PostgreSQL..."
            sudo systemctl start postgresql
            sudo systemctl enable postgresql
        else
            print_error "PostgreSQL is required. Please install it manually and run this script again."
            exit 1
        fi
    fi

    print_status "PostgreSQL is installed and running"
}

# Function to check all prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    check_python_version
    check_node_version
    check_postgresql
    check_go
    print_status "All prerequisites are satisfied"
}

# Function to setup Python virtual environment
setup_python_env() {
    print_info "Setting up Python virtual environment..."
    
    # Check if virtual environment exists
    if [ ! -d "$PROJECT_ROOT/backend/venv" ]; then
        print_info "Creating Python virtual environment..."
        python3 -m venv "$PROJECT_ROOT/backend/venv"
    fi
    
    # Activate virtual environment
    source "$PROJECT_ROOT/backend/venv/bin/activate"
    
    # Ensure pip is installed and up to date
    if [ ! -f "$PROJECT_ROOT/backend/venv/bin/pip" ]; then
        print_info "Installing pip in virtual environment..."
        python -m ensurepip --upgrade
    fi
    
    # Install required packages
    print_info "Installing required packages..."
    pip install -r "$PROJECT_ROOT/backend/requirements.txt"
    
    print_status "Python virtual environment is set up"
}

# Function to setup frontend dependencies
setup_frontend() {
    if ! dir_exists "$PROJECT_ROOT/frontend/node_modules"; then
        print_warning "Frontend dependencies not installed. Installing..."
        cd "$PROJECT_ROOT/frontend"
        npm install
        cd ..
    fi
    print_status "Frontend dependencies are installed"
}

# Function to setup SSL certificates
setup_ssl() {
    print_info "Setting up SSL certificates for development..."
    
    if ! command_exists mkcert; then
        print_warning "mkcert is not installed. Installing..."
        sudo apt install -y libnss3-tools
        curl -JLO "https://dl.filippo.io/mkcert/latest?for=linux/amd64"
        chmod +x mkcert-v*-linux-amd64
        sudo cp mkcert-v*-linux-amd64 /usr/local/bin/mkcert
    fi

    # Create certs directory if it doesn't exist
    mkdir -p "$PROJECT_ROOT/caddy/certs"
    
    # Check if certificates already exist
    if [ ! -f "$PROJECT_ROOT/caddy/certs/cashflow.app+4.pem" ]; then
        print_warning "SSL certificates not found. Generating..."
        cd "$PROJECT_ROOT/caddy/certs"
        mkcert -install
        mkcert "*.cashflow.app" "cashflow.app" localhost 127.0.0.1 ::1
        
        # Rename certificates to match Caddy's expected format
        if [ -f "_wildcard.cashflow.app+4.pem" ]; then
            mv "_wildcard.cashflow.app+4.pem" "cashflow.app+4.pem"
        fi
        if [ -f "_wildcard.cashflow.app+4-key.pem" ]; then
            mv "_wildcard.cashflow.app+4-key.pem" "cashflow.app+4-key.pem"
        fi
        
        # Ensure proper permissions
        sudo chown -R $USER:$USER "$PROJECT_ROOT/caddy/certs"
        sudo chmod 644 "$PROJECT_ROOT/caddy/certs"/*.pem
        sudo chmod 644 "$PROJECT_ROOT/caddy/certs"/*.key
        cd ../..
    fi
    
    # Verify certificates exist and are readable
    if [ ! -f "$PROJECT_ROOT/caddy/certs/cashflow.app+4.pem" ] || [ ! -f "$PROJECT_ROOT/caddy/certs/cashflow.app+4-key.pem" ]; then
        print_error "SSL certificates not found or not readable"
        print_info "Please check the certificates in $PROJECT_ROOT/caddy/certs/"
        exit 1
    fi
    
    # Update backend environment variables to disable SSL
    update_env_var "$PROJECT_ROOT/backend/.env" "USE_SSL" "false"
    update_env_var "$PROJECT_ROOT/backend/.env" "SSL_KEYFILE" ""
    update_env_var "$PROJECT_ROOT/backend/.env" "SSL_CERTFILE" ""
    
    print_status "SSL certificates are set up for development"
}

# Function to setup hosts file
setup_hosts() {
    local hosts=("cashflow.app" "test.cashflow.app")
    for host in "${hosts[@]}"; do
        if ! grep -q "127.0.0.1 $host" /etc/hosts; then
            print_warning "Adding $host to hosts file..."
            echo "127.0.0.1 $host" | sudo tee -a /etc/hosts
        fi
    done
    print_status "Hosts file is configured"
}

# Function to update or add environment variable
update_env_var() {
    local file="$1"
    local key="$2"
    local value="$3"
    
    # Clean up the value by removing ANSI color codes and extra whitespace
    local clean_value=$(echo "$value" | sed -r "s/\x1B\[([0-9]{1,3}(;[0-9]{1,2})?)?[mGK]//g" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    
    # Create a temporary file
    local temp_file=$(mktemp)
    
    if grep -q "^$key=" "$file"; then
        # Update existing variable
        awk -v key="$key" -v value="$clean_value" '
            BEGIN { FS=OFS="=" }
            $1 == key { $2 = value }
            { print }
        ' "$file" > "$temp_file"
    else
        # Add new variable
        cp "$file" "$temp_file"
        echo "$key=$clean_value" >> "$temp_file"
    fi
    
    # Replace the original file with the temporary file
    mv "$temp_file" "$file"
}

# Function to setup environment files
setup_env_files() {
    print_info "Setting up environment files..."

    # Check PostgreSQL status first
    if command_exists psql && service_running postgresql; then
        print_info "PostgreSQL is installed and running"
        if prompt_yes_no "Would you like to skip database setup?"; then
            print_info "Skipping database setup and password configuration"
            # Create or update .env files without database credentials
            if ! file_exists "$PROJECT_ROOT/backend/.env"; then
                print_info "Creating backend environment file..."
                cp "$PROJECT_ROOT/backend/.env.example" "$PROJECT_ROOT/backend/.env"
            fi
            
            # Update backend environment variables
            update_env_var "$PROJECT_ROOT/backend/.env" "ENVIRONMENT" "development"
            update_env_var "$PROJECT_ROOT/backend/.env" "MAILHOG_HOST" "localhost"
            update_env_var "$PROJECT_ROOT/backend/.env" "MAILHOG_PORT" "1025"
            
            # Clear production and staging email settings
            for setting in SENDGRID MAILTRAP SMTP; do
                update_env_var "$PROJECT_ROOT/backend/.env" "${setting}_" ""
            done

            if ! file_exists "$PROJECT_ROOT/frontend/.env.development"; then
                print_info "Creating frontend environment file..."
                cp "$PROJECT_ROOT/frontend/.env.example" "$PROJECT_ROOT/frontend/.env.development"
            fi

            # Update frontend environment variables
            update_env_var "$PROJECT_ROOT/frontend/.env.development" "REACT_APP_PROTOCOL" "https"
            update_env_var "$PROJECT_ROOT/frontend/.env.development" "REACT_APP_BASE_DOMAIN" "cashflow.app"
            update_env_var "$PROJECT_ROOT/frontend/.env.development" "REACT_APP_PORT" ""
            update_env_var "$PROJECT_ROOT/frontend/.env.development" "REACT_APP_API_PORT" ""
            
            return 0
        fi
    fi

    # Check if we have existing database credentials
    if [ -f "$PROJECT_ROOT/backend/.env" ]; then
        # Try to get existing values
        existing_db_url=$(get_existing_value "$PROJECT_ROOT/backend/.env" "DATABASE_URL")
        if [ -n "$existing_db_url" ]; then
            # Extract values from DATABASE_URL
            existing_db_user=$(echo "$existing_db_url" | sed -E 's|^postgresql://([^:]+):.*|\1|')
            existing_db_name=$(echo "$existing_db_url" | sed -E 's|^postgresql://[^/]+/([^?]+).*|\1|')
            
            # Check if we should use existing values
            if [ -n "$existing_db_user" ] && [ -n "$existing_db_name" ]; then
                echo -e "\n${YELLOW}Existing database configuration found:${NC}"
                echo "Database User: $existing_db_user"
                echo "Database Name: $existing_db_name"
                if prompt_yes_no "Would you like to use these existing database settings?"; then
                    CASHFLOW_USER="$existing_db_user"
                    CASHFLOW_DB="$existing_db_name"
                fi
            fi
        fi
    fi

    # Get PostgreSQL admin password
    echo -e "\n${YELLOW}IMPORTANT: This is the admin password for the PostgreSQL superuser ($PG_USER).${NC}"
    echo -e "${YELLOW}You will need this password for database administration tasks.${NC}"
    prompt_with_existing "Enter password for PostgreSQL superuser '$PG_USER'" "PG_ADMIN_PASSWORD" "$PROJECT_ROOT/backend/.env"

    # Get Cashflow database user password
    echo -e "\n${YELLOW}This is the password for the Cashflow application database user ($CASHFLOW_USER).${NC}"
    echo -e "${YELLOW}This password will be used by the application to connect to the database.${NC}"
    prompt_with_existing "Enter password for database user '$CASHFLOW_USER'" "DB_PASSWORD" "$PROJECT_ROOT/backend/.env"

    # Get secret key
    echo -e "\n${YELLOW}This is the secret key for the Cashflow application.${NC}"
    echo -e "${YELLOW}This key is used for cryptographic operations and should be kept secure.${NC}"
    prompt_with_existing "Enter secret key" "SECRET_KEY" "$PROJECT_ROOT/backend/.env"

    # Create or update backend environment file
    if ! file_exists "$PROJECT_ROOT/backend/.env"; then
        print_info "Creating backend environment file..."
        cp "$PROJECT_ROOT/backend/.env.example" "$PROJECT_ROOT/backend/.env"
    fi

    # Update backend environment variables with new values
    update_env_var "$PROJECT_ROOT/backend/.env" "DATABASE_URL" "postgresql://$CASHFLOW_USER:$DB_PASSWORD@localhost:5432/$CASHFLOW_DB"
    update_env_var "$PROJECT_ROOT/backend/.env" "SECRET_KEY" "$SECRET_KEY"
    update_env_var "$PROJECT_ROOT/backend/.env" "ENVIRONMENT" "development"
    update_env_var "$PROJECT_ROOT/backend/.env" "MAILHOG_HOST" "localhost"
    update_env_var "$PROJECT_ROOT/backend/.env" "MAILHOG_PORT" "1025"
    
    # Clear production and staging email settings
    for setting in SENDGRID MAILTRAP SMTP; do
        update_env_var "$PROJECT_ROOT/backend/.env" "${setting}_" ""
    done

    # Create or update frontend environment file
    if ! file_exists "$PROJECT_ROOT/frontend/.env.development"; then
        print_info "Creating frontend environment file..."
        cp "$PROJECT_ROOT/frontend/.env.example" "$PROJECT_ROOT/frontend/.env.development"
    fi

    # Update frontend environment variables
    update_env_var "$PROJECT_ROOT/frontend/.env.development" "REACT_APP_PROTOCOL" "https"
    update_env_var "$PROJECT_ROOT/frontend/.env.development" "REACT_APP_BASE_DOMAIN" "cashflow.app"
    update_env_var "$PROJECT_ROOT/frontend/.env.development" "REACT_APP_PORT" ""
    update_env_var "$PROJECT_ROOT/frontend/.env.development" "REACT_APP_API_PORT" ""

    print_status "Environment files are configured"
}

# Function to check if MailHog is running
check_mailhog() {
    if ! command_exists ~/go/bin/MailHog; then
        print_error "MailHog is not installed"
        if prompt_yes_no "Would you like to install MailHog?"; then
            print_info "Installing MailHog..."
            go install github.com/mailhog/MailHog@latest
            
            # Add Go bin to PATH if not already there
            if [[ ":$PATH:" != *":$HOME/go/bin:"* ]]; then
                echo 'export PATH=$PATH:$HOME/go/bin' >> ~/.bashrc
                source ~/.bashrc
            fi
        else
            print_error "MailHog is required for development. Please install it manually."
            print_info "Installation command: go install github.com/mailhog/MailHog@latest"
            exit 1
        fi
    fi

    # Start MailHog to verify it works
    print_info "Verifying MailHog installation..."
    ~/go/bin/MailHog &
    MAILHOG_PID=$!
    
    # Wait a moment for MailHog to start
    sleep 2
    
    # Check if MailHog is running and responding
    if ! port_in_use 1025; then
        print_error "Failed to start MailHog. Port 1025 is not in use."
        exit 1
    fi

    # Try to make a request to verify MailHog is responding
    if ! curl -s -o /dev/null -w "%{http_code}" http://localhost:8025 > /dev/null 2>&1; then
        print_error "MailHog is not responding to requests."
        print_info "Check if port 8025 is available and not blocked by firewall."
        exit 1
    fi

    # Verify API endpoint
    if ! curl -s -o /dev/null -w "%{http_code}" http://localhost:8025/api/v2/messages > /dev/null 2>&1; then
        print_error "MailHog API is not accessible."
        print_info "Check if MailHog is properly installed and configured."
        exit 1
    fi
    
    # Stop MailHog after verification
    kill $MAILHOG_PID
    print_status "MailHog is properly installed and configured"
}

# Function to check if Caddy is running
check_caddy() {
    if ! command_exists caddy; then
        print_error "Caddy is not installed"
        if prompt_yes_no "Would you like to install Caddy?"; then
            print_info "Installing Caddy..."
            sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
            curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
            curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
            sudo apt update
            sudo apt install caddy

            if ! command_exists caddy; then
                print_error "Failed to install Caddy. Please install it manually."
                print_info "Installation steps:"
                print_info "1. Add Caddy repository:"
                print_info "   sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https"
                print_info "   curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg"
                print_info "   curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list"
                print_info "2. Install Caddy:"
                print_info "   sudo apt update"
                print_info "   sudo apt install caddy"
                exit 1
            fi
        else
            print_error "Caddy is required. Please install it manually."
            exit 1
        fi
    fi

    # Start Caddy to verify it works
    print_info "Verifying Caddy installation..."
    if [ ! -f "$PROJECT_ROOT/caddy/Caddyfile.dev" ]; then
        print_error "Caddyfile.dev not found at $PROJECT_ROOT/caddy/Caddyfile.dev"
        exit 1
    fi
    
    # Change to caddy directory and start Caddy with relative path
    cd "$PROJECT_ROOT/caddy"
    sudo caddy run --config ./Caddyfile.dev &
    CADDY_PID=$!
    cd "$PROJECT_ROOT"
    
    # Wait a moment for Caddy to start
    sleep 2
    
    # Verify Caddy is running and responding
    if ! port_in_use 443; then
        print_error "Failed to start Caddy. Port 443 is not in use."
        print_info "You can check Caddy logs using: sudo journalctl -u caddy"
        exit 1
    fi

    # Try to make a request to verify Caddy is responding
    if ! curl -k -s -o /dev/null -w "%{http_code}" https://localhost > /dev/null 2>&1; then
        print_error "Caddy is not responding to requests."
        print_info "You can check Caddy logs using: sudo journalctl -u caddy"
        exit 1
    fi

    # Verify SSL certificate
    if ! curl -k -s -o /dev/null -w "%{http_code}" https://cashflow.app > /dev/null 2>&1; then
        print_error "Caddy is not properly handling the cashflow.app domain."
        print_info "Check your Caddyfile.dev configuration and SSL certificates."
        exit 1
    fi
    
    # Stop Caddy after verification
    sudo kill $CADDY_PID
    print_status "Caddy is properly installed and configured"
}

# Function to verify database setup
verify_database() {
    print_info "Verifying database setup..."
    
    # Check if PostgreSQL is running
    if ! service_running postgresql; then
        print_error "PostgreSQL is not running"
        return 1
    fi
    
    # Check if we have all required database values from .env
    if [ ! -f "$PROJECT_ROOT/backend/.env" ]; then
        print_error "Backend .env file not found"
        print_info "Please run the database setup first to create the .env file with database configurations."
        return 1
    fi
    
    # Get database values from .env
    DATABASE_URL=$(get_existing_value "$PROJECT_ROOT/backend/.env" "DATABASE_URL")
    if [ -z "$DATABASE_URL" ]; then
        print_error "DATABASE_URL not found in .env file"
        print_info "Please run the database setup first to configure the database connection."
        return 1
    fi
    
    # Extract values from DATABASE_URL including the password
    CASHFLOW_USER=$(echo "$DATABASE_URL" | sed -E 's|^postgresql://([^:]+):.*|\1|')
    CASHFLOW_DB=$(echo "$DATABASE_URL" | sed -E 's|^postgresql://[^/]+/([^?]+).*|\1|')
    DB_PASSWORD=$(echo "$DATABASE_URL" | sed -E 's|^postgresql://[^:]+:([^:@]+)@.*|\1|')
    if [ -z "$CASHFLOW_USER" ] || [ -z "$CASHFLOW_DB" ]; then
        print_error "Could not extract database user or name from DATABASE_URL"
        print_info "Please ensure DATABASE_URL in .env is properly formatted: postgresql://user:password@host:port/dbname"
        return 1
    fi
    
    # Check if we have the supergres password
    if [ -z "$PG_ADMIN_PASSWORD" ]; then
        echo -e "\n${YELLOW}IMPORTANT: This is the admin password for the PostgreSQL superuser ($PG_USER).${NC}"
        echo -e "${YELLOW}You will need this password for database administration tasks.${NC}"
        prompt_with_existing "Enter password for PostgreSQL superuser '$PG_USER'" "PG_ADMIN_PASSWORD" "$PROJECT_ROOT/backend/.env"
    fi
    echo "admin password: $PG_ADMIN_PASSWORD, user: $PG_USER, db: $CASHFLOW_DB"
    # Check if the database exists using supergres
    if ! PGPASSWORD="$PG_ADMIN_PASSWORD" psql -U "$PG_USER"  -lqt | cut -d \| -f 1 | grep -qw "$CASHFLOW_DB"; then
        print_error "Database '$CASHFLOW_DB' does not exist"
        return 1
    fi

    # Check if the user exists and can connect
    if ! PGPASSWORD="$DB_PASSWORD" psql -U "$CASHFLOW_USER" -d "$CASHFLOW_DB" -c "SELECT 1" >/dev/null 2>&1; then
        print_error "User '$CASHFLOW_USER' cannot connect to database '$CASHFLOW_DB'"
        print_info "This could be due to:"
        print_info "1. Incorrect password"
        print_info "2. User does not exist"
        print_info "3. User does not have proper permissions"
        return 1
    fi
    
    print_status "Database setup verified successfully"
    return 0
}

# Function to setup database
setup_database() {
    print_info "Setting up database..."
    
    # Check if database and users already exist
    if sudo -u postgres psql -lqt 2>/dev/null | cut -d \| -f 1 | grep -qw "$CASHFLOW_DB"; then
        print_warning "Database '$CASHFLOW_DB' already exists"
        if ! prompt_yes_no "Do you want to recreate the database and users?"; then
            print_info "Skipping database setup"
            # Verify existing setup
            if ! verify_database; then
                print_error "Existing database setup is not valid"
                if prompt_yes_no "Would you like to recreate the database and users?"; then
                    # Drop existing database and users
                    sudo -u postgres psql -c "DROP DATABASE IF EXISTS $CASHFLOW_DB;"
                    sudo -u postgres psql -c "DROP ROLE IF EXISTS $CASHFLOW_USER;"
                else
                    return 1
                fi
            else
                return 0
            fi
        else
            # Drop existing database and users
            sudo -u postgres psql -c "DROP DATABASE IF EXISTS $CASHFLOW_DB;"
            sudo -u postgres psql -c "DROP ROLE IF EXISTS $CASHFLOW_USER;"
        fi
    fi
    
    # Run setupdb.sh with all required parameters using the absolute path
    if ! "$SCRIPT_DIR/setupdb.sh" "$PG_USER" "$PG_ADMIN_PASSWORD" "$CASHFLOW_USER" "$DB_PASSWORD" "$CASHFLOW_DB"; then
        print_error "Failed to setup database. Please check setupdb.sh logs."
        return 1
    fi
    
    # Verify the setup
    if ! verify_database; then
        print_error "Database setup verification failed"
        return 1
    fi
    
    print_status "Database is properly configured"
    return 0
}

# Function to setup passwordless Caddy execution
setup_passwordless_caddy() {
    print_info "Setting up passwordless Caddy execution..."
    
    # Check if the sudoers rule already exists
    if [ -f "/etc/sudoers.d/caddy" ]; then
        print_info "Passwordless Caddy execution is already configured"
        return 0
    fi
    
    # Create sudoers rule for Caddy with relative path
    echo "$USER ALL=(ALL) NOPASSWD: /usr/bin/caddy run --config ./Caddyfile.dev" | sudo tee /etc/sudoers.d/caddy
    
    # Verify the rule was added
    if [ ! -f "/etc/sudoers.d/caddy" ]; then
        print_error "Failed to create Caddy sudoers rule"
        return 1
    fi
    
    # Set correct permissions
    sudo chmod 0440 /etc/sudoers.d/caddy
    
    print_status "Passwordless Caddy execution is configured"
    return 0
}

# Function to prompt for setup confirmation
prompt_setup() {
    local component="$1"
    if prompt_yes_no "Would you like to setup $component?"; then
        return 0
    fi
    print_info "Skipping $component setup"
    return 1
}

# Main setup process
echo -e "\nStarting development environment setup..."

# Check prerequisites first
check_prerequisites

# Setup environment files (which now includes PostgreSQL check)
if prompt_setup "Environment Files"; then
    setup_env_files
fi

# Only setup database if we didn't skip it
if [ -n "$PG_ADMIN_PASSWORD" ] && [ -n "$DB_PASSWORD" ]; then
    if prompt_setup "Database"; then
        setup_database
    fi
fi

# Setup Python environment
if prompt_setup "Python Environment"; then
    setup_python_env
fi

# Setup environment
if prompt_setup "Frontend Dependencies"; then
    setup_frontend
fi

if prompt_setup "SSL Certificates"; then
    setup_ssl
fi

if prompt_setup "Hosts File"; then
    setup_hosts
fi

# Setup passwordless Caddy execution
if prompt_setup "Passwordless Caddy Execution"; then
    setup_passwordless_caddy
fi

# Check services
if prompt_setup "Caddy Service"; then
    check_caddy
fi

if prompt_setup "MailHog Service"; then
    check_mailhog
fi

echo -e "\n${GREEN}Development environment setup complete!${NC}"
echo -e "\nYou can now start the application:"
echo "1. Backend: cd backend && source venv/bin/activate && python -m app.app"
echo "2. Frontend: cd frontend && npm start"
echo "3. Access the application at https://test.cashflow.app" 