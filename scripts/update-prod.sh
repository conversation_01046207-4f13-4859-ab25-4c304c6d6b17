#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the absolute path of the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Get the project root directory (one level up from scripts)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print status messages
print_status() { echo -e "\n${GREEN}[✓] $1${NC}"; }
print_error() { echo -e "\n${RED}[✗] $1${NC}"; }
print_warning() { echo -e "\n${YELLOW}[!] $1${NC}"; }
print_info() { echo -e "\n${BLUE}[i] $1${NC}"; }

# Function to ask for confirmation
ask_confirmation() {
    local message=$1
    while true; do
        read -p "$(echo -e "${YELLOW}$message (y/n)${NC}") " yn
        case $yn in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo "Please answer yes or no.";;
        esac
    done
}

# Function to check if a service is running
check_service() {
    if systemctl is-active --quiet "$1"; then
        print_status "$1 is running"
        return 0
    else
        print_error "$1 is not running"
        return 1
    fi
}

# Function to stop a service
stop_service() {
    print_info "Stopping $1..."
    systemctl stop "$1"
    sleep 2
    if ! systemctl is-active --quiet "$1"; then
        print_status "$1 stopped successfully"
    else
        print_error "Failed to stop $1"
        return 1
    fi
}

# Function to start a service
start_service() {
    print_info "Starting $1..."
    systemctl start "$1"
    sleep 2
    if check_service "$1"; then
        print_status "$1 started successfully"
    else
        print_error "Failed to start $1"
        return 1
    fi
}

# Function to update backend
update_backend() {
    print_info "Updating backend..."
    
    # Change to backend directory
    cd "$PROJECT_ROOT/backend"
    
    # Activate virtual environment if it exists
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # Update Python dependencies
    if ask_confirmation "Do you want to update Python dependencies?"; then
        print_info "Installing/updating Python dependencies..."
        pip install -r requirements.txt
    fi
    
    # Run database migrations
    if ask_confirmation "Do you want to run database migrations?"; then
        print_info "Running database migrations..."
        alembic upgrade head
    fi
    
    print_status "Backend update completed"
}

# Function to update frontend
update_frontend() {
    print_info "Updating frontend..."
    
    # Change to frontend directory
    cd "$PROJECT_ROOT/frontend"
    
    # Install/update dependencies
    if ask_confirmation "Do you want to update npm dependencies?"; then
        print_info "Installing/updating npm dependencies..."
        npm install
    fi
    
    # Build frontend
    if ask_confirmation "Do you want to rebuild the frontend?"; then
        print_info "Building frontend..."
        npm run build
        
        # Copy built files to web root
        print_info "Deploying frontend..."
        cp -r build/* /var/www/cashflow/
        chown -R $SUDO_USER:$SUDO_USER /var/www/cashflow
    fi
    
    print_status "Frontend update completed"
}

# Main update process
echo -e "\nStarting production update process..."

# Ask about stopping backend service
if ask_confirmation "Do you want to stop the backend service for update?"; then
    print_info "Stopping backend service..."
    stop_service "cashflow-backend"
fi

# Ask about updating components
if ask_confirmation "Do you want to update the backend?"; then
    update_backend
fi

if ask_confirmation "Do you want to update the frontend?"; then
    update_frontend
fi

# Ask about starting backend service
if ask_confirmation "Do you want to start the backend service?"; then
    print_info "Starting backend service..."
    start_service "cashflow-backend"
fi

# Verify all services are running
echo -e "\nVerifying services..."
check_service "postgresql"
check_service "caddy"
check_service "cashflow-backend"

echo -e "\n${GREEN}Update process completed!${NC}"
echo -e "\nYou can check service status with:"
echo "sudo systemctl status cashflow-backend"
echo "sudo systemctl status caddy"
echo "sudo systemctl status postgresql"

# Show logs if requested
if [ "$1" == "--logs" ]; then
    echo -e "\nShowing service logs (Ctrl+C to exit)..."
    journalctl -f -u cashflow-backend -u caddy
fi 