#!/bin/bash

# Configuration
HEALTH_LOG="/var/log/caddy/health.log"
ERROR_LOG="/var/log/caddy/error.log"
NOTIFICATION_EMAIL="<EMAIL>"  # Change this to your email
LAST_CHECK_FILE="/tmp/caddy_health_last_check"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# Function to send email notification
send_notification() {
    local subject="$1"
    local message="$2"
    echo "$message" | mail -s "$subject" "$NOTIFICATION_EMAIL"
}

# Function to send test notification
send_test_notification() {
    local message="This is a test notification from the Caddy health monitoring script.\n\n"
    message+="Time: $(date)\n"
    message+="Script: $0\n"
    message+="Host: $(hostname)"
    
    send_notification "Caddy Health Monitor Test" "$message"
    echo -e "${GREEN}Test notification sent to $NOTIFICATION_EMAIL${NC}"
}

# Function to check health logs
check_health_logs() {
    local current_time=$(date +%s)
    local last_check=$(cat "$LAST_CHECK_FILE" 2>/dev/null || echo "0")
    
    # Check for health check failures
    local health_failures=$(tail -n 100 "$HEALTH_LOG" 2>/dev/null | grep -i "health.*fail" | grep -v "status=200")
    
    # Check for errors
    local errors=$(tail -n 100 "$ERROR_LOG" 2>/dev/null | grep -i "error")
    
    if [ -n "$health_failures" ] || [ -n "$errors" ]; then
        local message="Caddy Health Check Alert\n\n"
        
        if [ -n "$health_failures" ]; then
            message+="Health Check Failures:\n$health_failures\n\n"
        fi
        
        if [ -n "$errors" ]; then
            message+="Errors:\n$errors\n\n"
        fi
        
        send_notification "Caddy Health Check Alert" "$message"
        echo -e "${RED}Health check failures or errors detected. Notification sent.${NC}"
    else
        echo -e "${GREEN}All health checks passed.${NC}"
    fi
    
    # Update last check time
    echo "$current_time" > "$LAST_CHECK_FILE"
}

# Main execution
if [ "$1" = "--test" ]; then
    echo "Sending test notification..."
    send_test_notification
else
    echo "Starting Caddy health monitoring..."
    check_health_logs
fi 