import os
import json
import psycopg2
from typing import Dict, Any, List, Tuple
from datetime import datetime
import getpass


def format_value(value):
    """Format a value for SQL insertion."""
    if value is None:
        return "NULL"
    elif isinstance(value, str):
        return f"'{value.replace('\\', '\\\\').replace('"', '\\"')}'"
    elif isinstance(value, (int, float)):
        return str(value)
    elif isinstance(value, datetime):
        return f"'{value.isoformat()}'"
    elif isinstance(value, bool):
        return "TRUE" if value else "FALSE"
    else:
        return f"'{str(value)}'"


def get_primary_keys(cursor, table_name: str) -> List[str]:
    """Get primary key columns for a table."""
    cursor.execute(
        f"""
        SELECT a.attname
        FROM   pg_index i
        JOIN   pg_attribute a ON a.attrelid = i.indrelid 
                             AND a.attnum = ANY(i.indkey)
        WHERE  i.indrelid = '{table_name}'::regclass
        AND    i.indisprimary;
    """
    )
    return [row[0] for row in cursor.fetchall()]


def generate_sql_upsert(
    table_name: str, columns: List[str], values: List[Tuple], primary_keys: List[str]
) -> str:
    """Generate SQL UPSERT statements with error handling for the given data."""
    sql_statements = []

    # Create INSERT statement template
    columns_str = ", ".join(columns)

    # Create ON CONFLICT clause
    pk_str = ", ".join(primary_keys)
    update_columns = [col for col in columns if col not in primary_keys]
    update_str = ", ".join([f"{col} = EXCLUDED.{col}" for col in update_columns])

    # Add table-specific error handling
    sql_statements.append("-- Processing {table} data".format(table=table_name))
    sql_statements.append("-- ON CONFLICT ({pk})".format(pk=pk_str))

    for i, row in enumerate(values):
        # Format each value for SQL
        formatted_values = [format_value(v) for v in row]
        values_str = ", ".join(formatted_values)

        sql = """
            -- Row {row_num} of {total_rows}
            INSERT INTO {table} ({columns}) 
            VALUES ({values})
            ON CONFLICT ({pk})
            DO NOTHING;
        """.format(
            row_num=i + 1,
            total_rows=len(values),
            table=table_name,
            columns=columns_str,
            values=values_str,
            pk=pk_str,
        )
        sql_statements.append(sql)

    # Add a newline for readability
    sql_statements.append("\n")

    return "\n".join(sql_statements)


def get_table_dependencies(cursor) -> Dict[str, List[str]]:
    cursor.execute("SELECT column_name FROM information_schema.columns")
    all_columns = cursor.fetchall()
    """Get foreign key dependencies between tables."""
    cursor.execute(
        """
        SELECT 
            tc.table_name as table_name,
            kcu.column_name as column_name,
            ccu.table_name as referenced_table_name
        FROM 
            information_schema.table_constraints tc 
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage ccu 
                ON ccu.constraint_name = tc.constraint_name
        WHERE 
            tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public';
    """
    )

    dependencies = {}
    all_rows = cursor.fetchall()
    for table, column, ref_table in all_rows:
        if table not in dependencies:
            dependencies[table] = []
        dependencies[table].append(ref_table)
    return dependencies


def topological_sort_tables(
    tables: List[str], dependencies: Dict[str, List[str]]
) -> List[str]:
    """Sort tables topologically based on foreign key dependencies."""
    # Create a set of all nodes
    nodes = set(tables)

    # Create adjacency list
    adj_list = {table: set() for table in tables}
    in_degree = {table: 0 for table in tables}

    # Build graph
    for table, deps in dependencies.items():
        if table in nodes:
            for dep in deps:
                if dep in nodes:
                    adj_list[table].add(dep)
                    in_degree[dep] += 1

    # Topological sort using Kahn's algorithm
    sorted_tables = []
    queue = [table for table in tables if in_degree[table] == 0]

    while queue:
        table = queue.pop(0)
        sorted_tables.append(table)

        for neighbor in adj_list[table]:
            in_degree[neighbor] -= 1
            if in_degree[neighbor] == 0:
                queue.append(neighbor)

    if len(sorted_tables) != len(tables):
        raise ValueError("Cyclic dependency detected between tables")

    return sorted_tables[::-1]


def export_organization_data(
    db_config: Dict[str, str], organization_id: str, output_dir: str = "exports"
):
    """
    Export all rows from tables associated with a specific organization_id.
    Generates JSON files for each table and a single consolidated SQL file.
    Uses UPSERT statements to handle existing rows.

    Args:
        db_config: Dictionary containing database connection parameters
        organization_id: The organization_id to filter by
        output_dir: Directory to save the exported files
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Connect to the database
    conn = psycopg2.connect(
        host=db_config["host"],
        database=db_config["database"],
        user=db_config["user"],
        password=db_config["password"],
    )

    cursor = conn.cursor()

    # Get all tables with organization_id column
    cursor.execute(
        """
        SELECT table_name
        FROM information_schema.columns
        WHERE column_name = 'organization_id'
        AND table_schema = 'public'
        ORDER BY table_name;
    """
    )

    # Get the list of tables
    base_tables = [row[0] for row in cursor.fetchall()]

    if not base_tables:
        raise ValueError("No tables found with organization_id column")

    print("\nFound tables with organization_id column:")
    for i, table in enumerate(base_tables):
        print(f"{i+1}. {table}")
    print()
    # Get dependencies and sort tables
    dependencies = get_table_dependencies(cursor)
    tables = topological_sort_tables(base_tables, dependencies)

    print("\nTable processing order:")
    print("(Tables are sorted to respect foreign key dependencies)")
    for i, table in enumerate(tables):
        print(f"{i+1}. {table}")
    print()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Initialize SQL content with transaction handling
    all_sql_content = []

    # Add error handling and transaction management
    # all_sql_content.extend([
    #     "BEGIN;",
    #     ""  # Add empty line for readability
    # ])

    for table in tables:
        try:
            # Get all column names for the table
            cursor.execute(
                f"SELECT column_name FROM information_schema.columns WHERE table_name = '{table}'"
            )
            columns = [col[0] for col in cursor.fetchall()]

            # Build SELECT query with all columns
            columns_str = ", ".join(columns)
            query = f"SELECT {columns_str} FROM {table} WHERE organization_id = %s"

            cursor.execute(query, (organization_id,))
            rows = cursor.fetchall()

            if not rows:
                print(f"No rows found in {table} for organization_id {organization_id}")
                continue

            # Convert rows to list of dictionaries
            data = [dict(zip(columns, row)) for row in rows]

            # Save to JSON file
            json_filename = f"{table}_org_{organization_id}_{timestamp}.json"
            json_filepath = os.path.join(output_dir, json_filename)

            with open(json_filepath, "w") as f:
                json.dump(data, f, indent=2, default=str)

            # Get primary keys for the table
            primary_keys = get_primary_keys(cursor, table)

            # Generate SQL for this table and add to all SQL content
            sql_content = generate_sql_upsert(table, columns, rows, primary_keys)
            all_sql_content.append("BEGIN;")
            all_sql_content.append(f"-- {table} data")
            all_sql_content.append(sql_content)
            all_sql_content.append("COMMIT;")
            all_sql_content.append("\n")

            print(f"Exported {len(data)} rows from {table}")
            print(f"  - JSON: {json_filename}")

        except Exception as e:
            print(f"Error exporting {table}: {str(e)}")

    # Create a single SQL file with all data
    all_sql_filename = f"all_tables_org_{organization_id}_{timestamp}.sql"
    all_sql_filepath = os.path.join(output_dir, all_sql_filename)

    with open(all_sql_filepath, "w") as f:
        f.write("-- Cashflow Organization Data Export\n")
        f.write(f"-- Organization ID: {organization_id}\n")
        f.write(f"-- Exported: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # Add transaction handling comments
        f.write("-- Transaction management\n")
        f.write("-- If any error occurs, the transaction will be rolled back\n")
        f.write("-- If all operations succeed, the transaction will be committed\n\n")

        # Write the SQL content
        f.write("\n".join(all_sql_content))

        # Add COMMIT statement
        # f.write("\n-- Commit the transaction if all operations succeed\n")
        # f.write("COMMIT;\n")

        # Add rollback handling
        # f.write("\n-- If you encounter an error, you can rollback manually\n")
        # f.write("-- ROLLBACK;\n")

    print(f"\nCreated consolidated SQL file: {all_sql_filename}")

    cursor.close()
    conn.close()


def main():
    # Get database credentials from user
    print("Please enter database credentials:")
    db_config = {
        "host": "localhost",
        "database": "cashflow",
        "user": input("Database user: "),
        "password": getpass.getpass("Database password: "),
    }

    # Get organization_id from user
    organization_id = input("Organization ID: ")
    if not organization_id:
        print("Error: Organization ID cannot be empty")
        return

    export_organization_data(db_config, organization_id)


if __name__ == "__main__":
    main()
