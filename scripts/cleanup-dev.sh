#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the absolute path of the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Get the project root directory (one level up from scripts)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Configuration
PG_USER="supergres"              # PostgreSQL superuser
CASHFLOW_USER="cashgres"         # Cashflow application user
CASHFLOW_DB="cashflow"           # Database name

# Function to print status messages
print_status() { echo -e "\n${GREEN}[✓] $1${NC}"; }
print_error() { echo -e "\n${RED}[✗] $1${NC}"; }
print_warning() { echo -e "\n${YELLOW}[!] $1${NC}"; }
print_info() { echo -e "\n${BLUE}[i] $1${NC}"; }

# Function to prompt user for yes/no
prompt_yes_no() {
    while true; do
        echo
        read -p "$1 (y/n): " yn
        case $yn in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo -e "\nPlease answer yes or no.";;
        esac
    done
}

# Function to remove PostgreSQL
remove_postgresql() {
    print_info "Removing PostgreSQL..."
    
    # Stop the service
    sudo systemctl stop postgresql

    # Uninstall packages

    sudo apt remove --purge postgresql postgresql-*
    sudo apt autoremove --purge -y

    # Remove configuration and data files
    sudo rm -rf /etc/postgresql
    sudo rm -rf /var/lib/postgresql
    sudo rm -rf /var/log/postgresql

    print_status "PostgreSQL and postgresql-contrib removed. Verifying..."
    if command -v psql &>/dev/null; then
        print_status "Warning: psql still found. Check for manual installations."
    else
        print_status "Removal complete."
    fi
}

# Function to remove Python virtual environment
remove_python_env() {
    print_info "Removing Python virtual environments..."
    
    # Remove backend virtual environment
    if [ -d "$PROJECT_ROOT/backend/venv" ]; then
        rm -rf "$PROJECT_ROOT/backend/venv"
        print_status "Backend Python virtual environment removed"
    else
        print_info "Backend Python virtual environment not found"
    fi

    # Remove scripts virtual environment
    if [ -d "$PROJECT_ROOT/scripts/venv" ]; then
        rm -rf "$PROJECT_ROOT/scripts/venv"
        print_status "Scripts Python virtual environment removed"
    else
        print_info "Scripts Python virtual environment not found"
    fi
}

# Function to remove frontend dependencies
remove_frontend() {
    print_info "Removing frontend dependencies..."
    
    if [ -d "$PROJECT_ROOT/frontend/node_modules" ]; then
        rm -rf "$PROJECT_ROOT/frontend/node_modules"
        print_status "Frontend dependencies removed"
    else
        print_info "Frontend dependencies not found"
    fi
}

# Function to remove SSL certificates
remove_ssl() {
    print_info "Removing SSL certificates..."
    
    if [ -d "$PROJECT_ROOT/caddy/certs" ]; then
        rm -rf "$PROJECT_ROOT/caddy/certs"
        print_status "SSL certificates removed"
    else
        print_info "SSL certificates not found"
    fi
}

# Function to remove hosts entries
remove_hosts() {
    print_info "Removing hosts entries..."
    
    local hosts=("cashflow.app" "test.cashflow.app")
    for host in "${hosts[@]}"; do
        if grep -q "127.0.0.1 $host" /etc/hosts; then
            sudo sed -i "/127.0.0.1 $host/d" /etc/hosts
        fi
    done
    
    print_status "Hosts entries removed"
}

# Function to remove Caddy
remove_caddy() {
    print_info "Removing Caddy..."
    
    # Remove Caddy sudoers rule
    if [ -f "/etc/sudoers.d/caddy" ]; then
        sudo rm -f /etc/sudoers.d/caddy
    fi
    
    # Remove Caddy
    if command -v apt >/dev/null 2>&1; then
        print_info "Removing Caddy packages..."
        sudo apt remove -y caddy
        sudo apt autoremove -y
    fi
    
    print_status "Caddy removed"
}

# Function to remove MailHog
remove_mailhog() {
    print_info "Removing MailHog..."
    
    # Remove MailHog binary
    if [ -f "$HOME/go/bin/MailHog" ]; then
        rm -f "$HOME/go/bin/MailHog"
    fi
    
    print_status "MailHog removed"
}

# Function to remove environment files
remove_env_files() {
    print_info "Removing environment files..."
    
    # Remove backend .env
    if [ -f "$PROJECT_ROOT/backend/.env" ]; then
        rm -f "$PROJECT_ROOT/backend/.env"
    fi
    
    # Remove frontend .env.development
    if [ -f "$PROJECT_ROOT/frontend/.env.development" ]; then
        rm -f "$PROJECT_ROOT/frontend/.env.development"
    fi
    
    print_status "Environment files removed"
}

# Main cleanup process
echo -e "\nStarting development environment cleanup..."

# Confirm cleanup
if ! prompt_yes_no "Are you sure you want to remove development environment components? This cannot be undone."; then
    print_info "Cleanup cancelled"
    exit 0
fi

# Remove components with individual confirmations
if prompt_yes_no "Do you want to remove PostgreSQL?"; then
    remove_postgresql
fi

if prompt_yes_no "Do you want to remove Python virtual environments?"; then
    remove_python_env
fi

if prompt_yes_no "Do you want to remove frontend dependencies?"; then
    remove_frontend
fi

if prompt_yes_no "Do you want to remove SSL certificates?"; then
    remove_ssl
fi

if prompt_yes_no "Do you want to remove hosts entries?"; then
    remove_hosts
fi

if prompt_yes_no "Do you want to remove Caddy?"; then
    remove_caddy
fi

if prompt_yes_no "Do you want to remove MailHog?"; then
    remove_mailhog
fi

if prompt_yes_no "Do you want to remove environment files?"; then
    remove_env_files
fi

echo -e "\n${GREEN}Development environment cleanup complete!${NC}"
echo -e "\nAll selected components have been removed. You can run setup-dev.sh again to recreate the environment." 