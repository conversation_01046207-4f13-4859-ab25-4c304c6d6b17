#!/bin/bash

# Get the absolute path of the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# Get the project root directory (one level up from scripts)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Configuration
PG_VERSION="16"                  # Target PostgreSQL version
PG_HBA_PATH="/etc/postgresql/$PG_VERSION/main/pg_hba.conf"  # Debian default
PG_CONF_PATH="/etc/postgresql/$PG_VERSION/main/postgresql.conf"
PG_DATA_DIR="/var/lib/postgresql/$PG_VERSION/main"
PG_SERVICE="postgresql"          # Default service name on Debian

# Check if all required parameters are provided
if [ $# -lt 5 ]; then
    echo "Error: All required parameters must be provided"
    echo "Usage: $0 <pg_user> <pg_password> <cashflow_user> <cashflow_password> <cashflow_db>"
    exit 1
fi

# Use provided parameters
PG_USER="$1"                     # PostgreSQL superuser
PG_PASSWORD="$2"                 # PostgreSQL superuser password
CASHFLOW_USER="$3"               # Cashflow application user
CASHFLOW_PASSWORD="$4"           # Cashflow user password
CASHFLOW_DB="$5"                 # Database name

# Function to install PostgreSQL
install_postgres() {
    if command -v psql &>/dev/null; then
        INSTALLED_VERSION=$(psql --version | awk '{print $3}' | cut -d'.' -f1)
        if [ "$INSTALLED_VERSION" -ge "$PG_VERSION" ]; then
            echo "PostgreSQL $INSTALLED_VERSION is already installed."
            return
        else
            echo "PostgreSQL is installed but version ($INSTALLED_VERSION) is older than $PG_VERSION. Upgrading..."
        fi
    fi

    echo "Installing PostgreSQL $PG_VERSION on Debian..."
    sudo apt update
    sudo apt install -y wget ca-certificates

    # Clean up existing PostgreSQL repository configs to avoid conflicts
    sudo rm -f /etc/apt/sources.list.d/pgdg.list
    sudo rm -f /etc/apt/sources.list.d/pgdg.sources
    sudo rm -f /usr/share/postgresql-common/pgdg/apt.postgresql.org.gpg

    # Import the PostgreSQL signing key
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o /usr/share/keyrings/postgresql-archive-keyring.gpg
    # Add the repository with signed-by option
    echo "deb [signed-by=/usr/share/keyrings/postgresql-archive-keyring.gpg] http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
    sudo apt update

    # Install PostgreSQL
    sudo apt install -y postgresql-$PG_VERSION
    # Optional: Install contrib modules (uncomment if needed)
    # sudo apt install -y postgresql-contrib-$PG_VERSION

    if [ $? -ne 0 ]; then
        echo "Error: PostgreSQL installation failed."
        exit 1
    fi
    echo "PostgreSQL $PG_VERSION installed successfully."
}

# Function to start PostgreSQL
start_postgres() {
    if ! sudo systemctl is-active --quiet "$PG_SERVICE"; then
        echo "Starting PostgreSQL $PG_VERSION..."
        sudo systemctl enable "$PG_SERVICE"
        sudo systemctl start "$PG_SERVICE"
    fi
    if ! sudo systemctl is-active --quiet "$PG_SERVICE"; then
        echo "Error: Failed to start PostgreSQL."
        exit 1
    fi
    echo "PostgreSQL is running."
}

# Main script
echo "Setting up PostgreSQL with $PG_USER as the superuser on Debian..."

# Install PostgreSQL if not present
install_postgres

# Start PostgreSQL
start_postgres

# Step 1: Create the new superuser
echo "Creating new superuser '$PG_USER'..."
sudo -u postgres psql -d postgres <<EOF
CREATE ROLE $PG_USER WITH LOGIN PASSWORD '$PG_PASSWORD' SUPERUSER;
EOF

if [ $? -ne 0 ]; then
    echo "Error: Failed to create $PG_USER."
    exit 1
fi

# Step 2: Configure password encryption to scram-sha-256
echo "Setting password_encryption to scram-sha-256..."
sudo sed -i "s/#password_encryption = .*/password_encryption = 'scram-sha-256'/" "$PG_CONF_PATH"

# Step 3: Update pg_hba.conf for scram-sha-256 and restrict postgres
echo "Updating pg_hba.conf..."
sudo bash -c "cat > $PG_HBA_PATH <<EOF
# Local connections
local   all   $PG_USER   scram-sha-256
local   all   $CASHFLOW_USER   scram-sha-256
local   all   postgres   reject
local   all   all        scram-sha-256
# TCP/IP connections (optional)
host    all   all        127.0.0.1/32   scram-sha-256
host    all   all        ::1/128        scram-sha-256
EOF"

# Step 4: Restart PostgreSQL to apply config changes
echo "Restarting PostgreSQL..."
sudo systemctl restart "$PG_SERVICE"

# Step 5: Reassign ownership and lock down postgres
export PGPASSWORD="$PG_PASSWORD"
echo "Reassigning ownership and locking down postgres..."
cat <<EOF > /tmp/setup.sql
ALTER DATABASE postgres OWNER TO $PG_USER;
ALTER DATABASE template1 OWNER TO $PG_USER;
\connect postgres
REASSIGN OWNED BY postgres TO $PG_USER;
\connect template1
REASSIGN OWNED BY postgres TO $PG_USER;
ALTER ROLE postgres WITH NOLOGIN;
EOF
psql -U "$PG_USER" -d postgres -f /tmp/setup.sql
if [ $? -ne 0 ]; then
    echo "Error: Failed to execute SQL commands."
    cat /tmp/setup.sql  # Display for debugging
    rm -f /tmp/setup.sql
    exit 1
fi
rm -f /tmp/setup.sql

# Step 6: Test connection
echo "Testing connection with $PG_USER..."
psql -U "$PG_USER" -d postgres -c "SELECT current_user;" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Warning: Connection test failed. Verify $PG_HBA_PATH and password."
    exit 1
fi

# Step 7: Create the cashflow database and configure cashgres user
echo "Creating database '$CASHFLOW_DB' and configuring '$CASHFLOW_USER' user..."
psql -U "$PG_USER" -d postgres <<EOF
-- Create the database
CREATE DATABASE $CASHFLOW_DB;

-- Create the user role with login and password
CREATE ROLE $CASHFLOW_USER WITH LOGIN PASSWORD '$CASHFLOW_PASSWORD';

-- Grant database-level privileges
GRANT ALL PRIVILEGES ON DATABASE $CASHFLOW_DB TO $CASHFLOW_USER;

-- Connect to the new database to configure schema-level privileges
\c $CASHFLOW_DB

-- Grant privileges on all existing tables in the public schema
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $CASHFLOW_USER;

-- Grant privileges on all existing sequences in the public schema (if applicable)
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $CASHFLOW_USER;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public 
GRANT ALL PRIVILEGES ON TABLES TO $CASHFLOW_USER;

-- Set default privileges for future sequences
ALTER DEFAULT PRIVILEGES IN SCHEMA public 
GRANT ALL PRIVILEGES ON SEQUENCES TO $CASHFLOW_USER;

EOF

if [ $? -ne 0 ]; then
    echo "Error: Failed to create database or configure $CASHFLOW_USER."
    exit 1
fi

# Display connection info without showing passwords
echo "Done. PostgreSQL is installed, configured, and $CASHFLOW_DB is set up with $PG_USER in control."
echo "Connect with: psql -U $CASHFLOW_USER -d $CASHFLOW_DB"