{
    # Global options
    admin off
    servers {
        protocols h1 h2 h2c h3
        # Increase max header size for large cookies/tokens
        max_header_size 16384
    }
    # Configure logging
    log {
        output file /var/log/caddy/access.log {
            roll_size 10MB
            roll_keep 5
            roll_keep_for 720h
        }
        # Add error log
        output file /var/log/caddy/error.log {
            roll_size 10MB
            roll_keep 5
            roll_keep_for 720h
        }
        format json
    }
    # Configure automatic HTTPS
    # auto_https disable_redirects
    # Configure storage
    storage file_system {
        root /var/lib/caddy
    }
}

# Handle main www domain redirect
www.previsioiq.com {
    redir https://previsioiq.com{uri} permanent
}

*.previsioiq.com, previsioiq.com {
    # TLS configuration
    tls {
        protocols tls1.2 tls1.3
        alpn h2 http/1.1
        dns godaddy {$GODADDY_TOKEN}
        # Enable certificate transparency
    }

    # API endpoints
    handle /api/* {
        # Reverse proxy to backend
        reverse_proxy localhost:8000 {
            # Headers
            header_up Host {upstream_hostport}
            header_up X-Real-IP {remote_host}
            
            # Health checks
            health_uri /api/health
            health_interval 30s
            health_timeout 5s
            health_status 200
            health_headers {
                User-Agent "Caddy Health Check"
            }
            
            # Load balancing (if you add more backend instances later)
            lb_policy round_robin
            
            # Timeouts
            transport http {
                dial_timeout 10s
                response_header_timeout 10s
                read_timeout 60s
                write_timeout 60s
            }
        }

        # API-specific headers
        header {
            # Cache control for API
            Cache-Control "no-store, no-cache, must-revalidate"
            Pragma "no-cache"
            Expires "0"
        }
    }

    # Frontend - Production
    handle {
        root * /var/www/cashflow
        try_files {path} /index.html
        file_server

        # Enable compression for static files
        encode gzip
        encode zstd

        # Cache static assets
        header {
            # Cache control for static assets
            Cache-Control "public, max-age=31536000, immutable"
            # Cache control for HTML
            @html {
                file
                path *.html
            }
            header @html Cache-Control "no-cache"
        }
    }

    # Production-specific security headers
    header {
        # Enable HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        # Prevent clickjacking
        X-Frame-Options "SAMEORIGIN"
        # XSS protection
        X-XSS-Protection "1; mode=block"
        # Prevent MIME type sniffing
        X-Content-Type-Options "nosniff"
        # Referrer policy
        Referrer-Policy "strict-origin-when-cross-origin"
        # Content Security Policy
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"
        # Remove server header
        -Server
    }
}
