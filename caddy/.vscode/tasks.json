{"version": "2.0.0", "tasks": [{"label": "Start MailHog", "type": "shell", "command": "MailHog", "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^.*$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": ".*Binding to address.*", "endsPattern": ".*Serving under.*"}}}, {"label": "Stop MailHog", "type": "shell", "command": "pkill -f MailHog", "presentation": {"reveal": "silent", "close": true}}, {"label": "Start Caddy", "type": "shell", "command": "sudo /usr/bin/caddy run --config ./Caddyfile.dev", "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^.*$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": ".*using config from file.*", "endsPattern": ".*serving initial configuration.*"}}}, {"label": "Stop Caddy", "type": "shell", "command": "sudo pkill -f caddy", "presentation": {"reveal": "silent", "close": true}}]}