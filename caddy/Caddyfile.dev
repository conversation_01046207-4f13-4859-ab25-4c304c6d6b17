{
    debug
	admin off
	auto_https off
	servers {
		protocols h1 h2c
	}
	log {
		output stderr
		level DEBUG
	}
}

*.cashflow.app, cashflow.app {
	tls /home/<USER>/projects/Cashflow/caddy/certs/cashflow.app+4.pem /home/<USER>/projects/Cashflow/caddy/certs/cashflow.app+4-key.pem
	
	# API endpoints
	handle /api/* {
		reverse_proxy http://localhost:8000 {
			health_uri /api/health
			health_interval 10s
			health_timeout 5s
			health_status 200
		}
	}

	# Frontend
	handle {
		reverse_proxy http://localhost:3000 {
			health_uri /
			health_interval 10s
			health_timeout 5s
			health_status 200
			header_up Host {host}
			header_up X-Real-IP {remote}
			header_up X-Forwarded-For {remote}
			header_up X-Forwarded-Proto {scheme}
		}
	}
}
